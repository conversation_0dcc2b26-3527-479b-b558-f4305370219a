<?php
// Test blog functionality
require_once 'includes/blog_functions.php';

echo "<h1>Blog Function Test</h1>";

// Test fetching blog posts
$blog_posts = fetch_blog_posts(4);

echo "<h2>Blog Posts Retrieved:</h2>";
echo "<pre>";
print_r($blog_posts);
echo "</pre>";

echo "<h2>Rendered Blog Cards:</h2>";
foreach ($blog_posts as $post) {
    echo "<div style='border: 1px solid #ccc; margin: 10px; padding: 10px;'>";
    echo "<h3>" . htmlspecialchars($post['title']) . "</h3>";
    echo "<p>" . htmlspecialchars($post['excerpt']) . "</p>";
    echo "<p><strong>Date:</strong> " . htmlspecialchars($post['date']) . "</p>";
    echo "<p><strong>Image:</strong> " . htmlspecialchars($post['image']) . "</p>";
    echo "<p><strong>URL:</strong> " . htmlspecialchars($post['url']) . "</p>";
    echo "</div>";
}
?>
