/* Dark Mode Styles */
:root {
    --dark-bg: #121212;
    --dark-card-bg: #1e1e1e;
    --dark-text: #e0e0e0;
    --dark-text-secondary: #aaaaaa;
    --dark-border: #333333;
    --dark-input-bg: #2c2c2c;
    --dark-hover: #2a2a2a;
    --dark-primary: #0d6efd;
    --dark-primary-hover: #0b5ed7;
    --dark-success: #198754;
    --dark-danger: #dc3545;
    --dark-warning: #ffc107;
    --dark-info: #0dcaf0;
}

body.dark-mode {
    background-color: var(--dark-bg);
    color: var(--dark-text);
}

/* Navbar */
body.dark-mode .navbar {
    background-color: var(--dark-card-bg) !important;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

body.dark-mode .navbar-brand,
body.dark-mode .nav-link,
body.dark-mode .navbar-text {
    color: var(--dark-text) !important;
}

body.dark-mode .navbar-toggler {
    border-color: var(--dark-border);
}

/* Sidebar */
body.dark-mode .sidebar {
    background-color: var(--dark-card-bg) !important;
    box-shadow: inset -1px 0 0 var(--dark-border);
}

body.dark-mode .sidebar .nav-link {
    color: var(--dark-text) !important;
}

body.dark-mode .sidebar .nav-link:hover {
    background-color: var(--dark-hover);
}

body.dark-mode .sidebar .nav-link.active {
    background-color: var(--dark-hover);
    color: var(--dark-primary) !important;
}

body.dark-mode .bg-light {
    background-color: var(--dark-card-bg) !important;
}

/* Cards */
body.dark-mode .card {
    background-color: var(--dark-card-bg);
    border-color: var(--dark-border);
}

body.dark-mode .card-header {
    background-color: rgba(0, 0, 0, 0.2);
    border-bottom-color: var(--dark-border);
}

body.dark-mode .list-group-item {
    background-color: var(--dark-card-bg);
    border-color: var(--dark-border);
    color: var(--dark-text);
}

/* Tables */
body.dark-mode .table {
    color: var(--dark-text);
}

body.dark-mode .table th {
    background-color: var(--dark-hover);
}

body.dark-mode .table td,
body.dark-mode .table th {
    border-color: var(--dark-border);
}

body.dark-mode .table-striped tbody tr:nth-of-type(odd) {
    background-color: rgba(255, 255, 255, 0.05);
}

body.dark-mode .table-hover tbody tr:hover {
    background-color: rgba(255, 255, 255, 0.075);
}

/* Forms */
body.dark-mode .form-control,
body.dark-mode .custom-select,
body.dark-mode .custom-file-label {
    background-color: var(--dark-input-bg);
    border-color: var(--dark-border);
    color: var(--dark-text);
}

body.dark-mode .form-control:focus {
    background-color: var(--dark-input-bg);
    border-color: var(--dark-primary);
    color: var(--dark-text);
}

body.dark-mode .input-group-text {
    background-color: var(--dark-hover);
    border-color: var(--dark-border);
    color: var(--dark-text);
}

body.dark-mode .custom-file-label::after {
    background-color: var(--dark-hover);
    color: var(--dark-text);
}

body.dark-mode .form-text {
    color: var(--dark-text-secondary);
}

/* Buttons */
body.dark-mode .btn-outline-secondary {
    color: var(--dark-text);
    border-color: var(--dark-border);
}

body.dark-mode .btn-outline-secondary:hover {
    background-color: var(--dark-hover);
    color: var(--dark-text);
}

/* Alerts */
body.dark-mode .alert-info {
    background-color: rgba(13, 202, 240, 0.15);
    border-color: rgba(13, 202, 240, 0.4);
    color: #8cdbe9;
}

body.dark-mode .alert-warning {
    background-color: rgba(255, 193, 7, 0.15);
    border-color: rgba(255, 193, 7, 0.4);
    color: #ffe083;
}

body.dark-mode .alert-success {
    background-color: rgba(25, 135, 84, 0.15);
    border-color: rgba(25, 135, 84, 0.4);
    color: #75c097;
}

body.dark-mode .alert-danger {
    background-color: rgba(220, 53, 69, 0.15);
    border-color: rgba(220, 53, 69, 0.4);
    color: #e17a85;
}

/* Badges */
body.dark-mode .badge-secondary {
    background-color: #555555;
}

/* Modals */
body.dark-mode .modal-content {
    background-color: var(--dark-card-bg);
    border-color: var(--dark-border);
}

body.dark-mode .modal-header,
body.dark-mode .modal-footer {
    border-color: var(--dark-border);
}

body.dark-mode .close {
    color: var(--dark-text);
}

/* Tabs */
body.dark-mode .nav-tabs {
    border-color: var(--dark-border);
}

body.dark-mode .nav-tabs .nav-link {
    color: var(--dark-text);
}

body.dark-mode .nav-tabs .nav-link.active {
    background-color: var(--dark-card-bg);
    border-color: var(--dark-border);
    border-bottom-color: var(--dark-card-bg);
    color: var(--dark-primary);
}

body.dark-mode .nav-tabs .nav-link:hover {
    border-color: var(--dark-border);
}

/* Chat */
body.dark-mode .chat-container {
    background-color: var(--dark-card-bg);
    border-color: var(--dark-border);
}

body.dark-mode .chat-message-sender {
    background-color: var(--dark-hover);
}

body.dark-mode .chat-message-receiver {
    background-color: rgba(13, 110, 253, 0.2);
}

body.dark-mode .chat-message-time {
    color: var(--dark-text-secondary);
}

/* Footer */
body.dark-mode footer {
    background-color: var(--dark-card-bg) !important;
    color: var(--dark-text-secondary) !important;
}

body.dark-mode footer a {
    color: var(--dark-primary);
}

/* Dark mode toggle button */
.dark-mode-toggle {
    cursor: pointer;
    padding: 0.5rem;
    display: flex;
    align-items: center;
}

.dark-mode-toggle i {
    font-size: 1.2rem;
}

/* Dashboard specific styles */
body.dark-mode .jumbotron {
    background-color: var(--dark-card-bg) !important;
    color: var(--dark-text);
}

body.dark-mode .jumbotron h2,
body.dark-mode .jumbotron p,
body.dark-mode .jumbotron .lead,
body.dark-mode .jumbotron strong {
    color: var(--dark-text);
}

body.dark-mode .quick-access-card {
    background-color: var(--dark-card-bg);
}

body.dark-mode .quick-access-card i,
body.dark-mode .quick-access-card h5,
body.dark-mode .quick-access-card p {
    color: var(--dark-text);
}

body.dark-mode h1,
body.dark-mode h2,
body.dark-mode h3,
body.dark-mode h4,
body.dark-mode h5,
body.dark-mode h6,
body.dark-mode p,
body.dark-mode .text-muted,
body.dark-mode .card-text {
    color: var(--dark-text);
}

body.dark-mode .text-muted {
    color: var(--dark-text-secondary) !important;
}

body.dark-mode .border-bottom {
    border-color: var(--dark-border) !important;
}

/* Notification Styles */
body.dark-mode .notification-item {
    background-color: var(--dark-card-bg);
    border-color: var(--dark-border);
    color: var(--dark-text);
}

body.dark-mode .notification-item:hover {
    background-color: var(--dark-hover);
}

body.dark-mode .notification-item.unread {
    background-color: rgba(12, 25, 218, 0.1);
}

body.dark-mode .notification-item.unread:hover {
    background-color: rgba(3, 16, 202, 0.15);
}

body.dark-mode .notification-item h6,
body.dark-mode .notification-item p {
    color: var(--dark-text);
}

/* Blog Widget Dark Mode Styles */
body.dark-mode .card .card-header.bg-success {
    background-color: var(--dark-success) !important;
    color: #ffffff !important;
    border-bottom-color: var(--dark-border);
}

body.dark-mode .card .card-header.bg-success h5,
body.dark-mode .card .card-header.bg-success i {
    color: #ffffff !important;
}

body.dark-mode .card .card-header.bg-success .btn-outline-light {
    color: #ffffff !important;
    border-color: rgba(255, 255, 255, 0.5) !important;
}

body.dark-mode .card .card-header.bg-success .btn-outline-light:hover {
    background-color: rgba(255, 255, 255, 0.1) !important;
    border-color: #ffffff !important;
    color: #ffffff !important;
}

/* Blog widget list items */
body.dark-mode .list-group-item h6 {
    color: var(--dark-text) !important;
}

body.dark-mode .list-group-item h6 a {
    color: var(--dark-text) !important;
    text-decoration: none;
}

body.dark-mode .list-group-item h6 a:hover {
    color: var(--dark-primary) !important;
}

body.dark-mode .list-group-item p.text-muted {
    color: var(--dark-text-secondary) !important;
}

body.dark-mode .list-group-item small.text-muted {
    color: var(--dark-text-secondary) !important;
}

body.dark-mode .list-group-item small.text-muted i {
    color: var(--dark-success) !important;
}

body.dark-mode .list-group-item .btn-outline-success {
    color: var(--dark-success) !important;
    border-color: var(--dark-success) !important;
}

body.dark-mode .list-group-item .btn-outline-success:hover {
    background-color: var(--dark-success) !important;
    border-color: var(--dark-success) !important;
    color: #ffffff !important;
}

/* Blog widget empty state */
body.dark-mode .list-group-item .fa-blog {
    color: var(--dark-text-secondary) !important;
}

body.dark-mode .list-group-item p.text-muted.mb-0 {
    color: var(--dark-text-secondary) !important;
}

/* Ensure proper text contrast for all blog widget elements */
body.dark-mode .card .list-group-item .text-decoration-none.text-dark {
    color: var(--dark-text) !important;
}

body.dark-mode .card .list-group-item .text-decoration-none.text-dark:hover {
    color: var(--dark-primary) !important;
}

/* ===== Leaderboard Dark Mode Styles ===== */
body.dark-mode .leaderboard-table {
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.3);
}

body.dark-mode .leaderboard-table .table-row:hover {
    background-color: rgba(0, 166, 81, 0.15);
}

body.dark-mode .leaderboard-table .user-row {
    background-color: rgba(0, 166, 81, 0.2);
    border-left: 4px solid var(--primary-color);
}

body.dark-mode .leaderboard-stats-card {
    background-color: var(--dark-card-bg);
    border-color: var(--dark-border);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

body.dark-mode .leaderboard-stats-card:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4);
}

body.dark-mode .leaderboard-preview {
    background: linear-gradient(135deg, rgba(255, 193, 7, 0.15), rgba(0, 166, 81, 0.15));
    border: 1px solid var(--dark-border);
}

/* Leaderboard preview content styling */
body.dark-mode .leaderboard-preview .card-body {
    color: var(--dark-text);
}

body.dark-mode .leaderboard-preview h6 {
    color: var(--dark-text-secondary) !important;
}

body.dark-mode .leaderboard-preview h3 {
    color: var(--dark-text) !important;
}

body.dark-mode .leaderboard-preview small {
    color: var(--dark-text-secondary) !important;
}

body.dark-mode .leaderboard-preview strong {
    color: var(--dark-text) !important;
}

/* Inner card in leaderboard preview */
body.dark-mode .leaderboard-preview .card.bg-light {
    background-color: var(--dark-card-bg) !important;
    border-color: var(--dark-border);
}

body.dark-mode .leaderboard-preview .card.bg-light .card-body {
    color: var(--dark-text);
}

/* Rank badges maintain their colors but adjust for dark mode visibility */
body.dark-mode .rank-badge.gold {
    background: linear-gradient(135deg, #FFD700, #FFA500);
    color: #1a1a1a;
    box-shadow: 0 2px 4px rgba(255, 215, 0, 0.4);
}

body.dark-mode .rank-badge.silver {
    background: linear-gradient(135deg, #C0C0C0, #A8A8A8);
    color: #1a1a1a;
    box-shadow: 0 2px 4px rgba(192, 192, 192, 0.4);
}

body.dark-mode .rank-badge.bronze {
    background: linear-gradient(135deg, #CD7F32, #B8860B);
    color: white;
    box-shadow: 0 2px 4px rgba(205, 127, 50, 0.4);
}

body.dark-mode .rank-badge.default {
    background: linear-gradient(135deg, var(--dark-success), #0f5132);
    color: white;
    box-shadow: 0 2px 4px rgba(25, 135, 84, 0.4);
}

/* Rank icons maintain their colors */
body.dark-mode .rank-icon.gold {
    color: #FFD700;
}

body.dark-mode .rank-icon.silver {
    color: #C0C0C0;
}

body.dark-mode .rank-icon.bronze {
    color: #CD7F32;
}

body.dark-mode .rank-icon.default {
    color: var(--dark-success);
}

/* Page header styling */
body.dark-mode .page-header h1 {
    color: var(--dark-text);
}

body.dark-mode .page-header p.lead {
    color: var(--dark-text-secondary);
}

/* Statistics cards text */
body.dark-mode .leaderboard-stats-card .card-body h4 {
    color: var(--dark-text);
}

body.dark-mode .leaderboard-stats-card .card-body small {
    color: var(--dark-text-secondary);
}

/* Table styling for leaderboard */
body.dark-mode .table {
    color: var(--dark-text);
}

body.dark-mode .table th {
    color: var(--dark-text);
    border-color: var(--dark-border);
    background-color: var(--dark-card-bg);
}

body.dark-mode .table td {
    color: var(--dark-text);
    border-color: var(--dark-border);
}

body.dark-mode .table-hover tbody tr:hover {
    background-color: rgba(0, 166, 81, 0.1);
}

body.dark-mode .table-success {
    background-color: rgba(25, 135, 84, 0.2);
}

body.dark-mode .table-success td {
    color: var(--dark-text);
}

body.dark-mode .thead-light th {
    background-color: var(--dark-hover);
    border-color: var(--dark-border);
    color: var(--dark-text);
}

/* Specific leaderboard table fixes for contrast issues */
body.dark-mode .leaderboard-table .table td {
    color: var(--dark-text) !important;
    border-color: var(--dark-border);
}

body.dark-mode .leaderboard-table .table th {
    color: var(--dark-text) !important;
    background-color: var(--dark-hover);
    border-color: var(--dark-border);
}

body.dark-mode .leaderboard-table .table-row td {
    color: var(--dark-text) !important;
}

body.dark-mode .leaderboard-table .table-row:hover td {
    color: var(--dark-text) !important;
    background-color: rgba(0, 166, 81, 0.1);
}

body.dark-mode .leaderboard-table .user-row td {
    color: var(--dark-text) !important;
    background-color: rgba(25, 135, 84, 0.2);
}

body.dark-mode .leaderboard-table .table-success td {
    color: var(--dark-text) !important;
}

/* Ensure strong text in leaderboard table is visible */
body.dark-mode .leaderboard-table strong {
    color: var(--dark-text) !important;
}

/* Fix for text-warning elements in leaderboard table */
body.dark-mode .leaderboard-table .text-warning {
    color: var(--dark-warning) !important;
}

/* Badge colors in dark mode */
body.dark-mode .badge-warning {
    background-color: var(--dark-warning);
    color: #1a1a1a;
}

body.dark-mode .badge-success {
    background-color: var(--dark-success);
    color: white;
}

body.dark-mode .badge-info {
    background-color: var(--dark-info);
    color: #1a1a1a;
}

body.dark-mode .badge-secondary {
    background-color: #6c757d;
    color: white;
}

body.dark-mode .badge-dark {
    background-color: #495057;
    color: white;
}

/* Card headers with colored backgrounds */
body.dark-mode .card-header.bg-success {
    background-color: var(--dark-success) !important;
    color: white !important;
}

body.dark-mode .card-header.bg-primary {
    background-color: var(--dark-primary) !important;
    color: white !important;
}

body.dark-mode .card-header.bg-warning {
    background-color: var(--dark-warning) !important;
    color: #1a1a1a !important;
}

/* Border colors for cards */
body.dark-mode .card.border-success {
    border-color: var(--dark-success) !important;
}

body.dark-mode .card.border-primary {
    border-color: var(--dark-primary) !important;
}

body.dark-mode .card.border-warning {
    border-color: var(--dark-warning) !important;
}

body.dark-mode .card.border-info {
    border-color: var(--dark-info) !important;
}

/* Text colors for statistics */
body.dark-mode .text-primary {
    color: var(--dark-primary) !important;
}

body.dark-mode .text-success {
    color: var(--dark-success) !important;
}

body.dark-mode .text-warning {
    color: var(--dark-warning) !important;
}

body.dark-mode .text-info {
    color: var(--dark-info) !important;
}

body.dark-mode .text-dark {
    color: var(--dark-text) !important;
}

/* Alert styling for leaderboard */
body.dark-mode .alert-info {
    background-color: rgba(13, 202, 240, 0.1);
    border-color: var(--dark-info);
    color: var(--dark-text);
}

body.dark-mode .alert-warning {
    background-color: rgba(255, 193, 7, 0.1);
    border-color: var(--dark-warning);
    color: var(--dark-text);
}

body.dark-mode .alert-link {
    color: var(--dark-primary);
}

/* Strong text in dark mode */
body.dark-mode strong {
    color: var(--dark-text);
}

/* Leaderboard page specific styling */
body.dark-mode .page-header h1 {
    color: var(--dark-text) !important;
}

body.dark-mode .page-header .lead {
    color: var(--dark-text-secondary) !important;
}

/* User position card styling */
body.dark-mode .card.border-success .card-body h3 {
    color: var(--dark-text) !important;
}

body.dark-mode .card.border-success .card-body small {
    color: var(--dark-text-secondary) !important;
}

/* Call to action card */
body.dark-mode .card.bg-light {
    background-color: var(--dark-card-bg) !important;
    border-color: var(--dark-border);
}

body.dark-mode .card.bg-light .card-body h5 {
    color: var(--dark-text) !important;
}

body.dark-mode .card.bg-light .card-body p {
    color: var(--dark-text-secondary) !important;
}

/* Alert styling in leaderboard */
body.dark-mode .alert-info h5 {
    color: var(--dark-text) !important;
}

body.dark-mode .alert-info p {
    color: var(--dark-text) !important;
}

/* Tutorials Dark Mode Styles */
body.dark-mode .tutorial-card {
    background-color: var(--dark-card-bg);
    border-color: var(--dark-border);
    color: var(--dark-text);
}

body.dark-mode .tutorial-card:hover {
    border-color: var(--dark-success);
    box-shadow: 0 8px 25px rgba(0,0,0,0.4);
}

body.dark-mode .tutorial-card .card-title {
    color: var(--dark-text);
}

body.dark-mode .tutorial-card .card-text {
    color: var(--dark-text-secondary);
}

body.dark-mode .tutorial-card .badge-success {
    background-color: var(--dark-success);
    color: #ffffff;
}

body.dark-mode .tutorial-card .badge-info {
    background-color: var(--dark-info);
    color: #ffffff;
}

body.dark-mode .tutorial-card .text-muted {
    color: var(--dark-text-secondary) !important;
}

/* Tutorial Modal Dark Mode */
body.dark-mode .tutorial-modal .modal-content {
    background-color: var(--dark-card-bg);
    border-color: var(--dark-border);
}

body.dark-mode .tutorial-modal .modal-header {
    background: linear-gradient(135deg, var(--dark-success) 0%, #20c997 100%);
    border-bottom-color: var(--dark-border);
}

body.dark-mode .tutorial-modal .modal-title {
    color: #ffffff;
}

body.dark-mode .tutorial-modal .modal-body {
    background-color: var(--dark-card-bg);
    color: var(--dark-text);
}

body.dark-mode .tutorial-modal .close {
    color: #ffffff;
}

body.dark-mode .tutorial-modal .close:hover {
    color: #ffffff;
    opacity: 0.8;
}

/* Tutorial Content Dark Mode */
body.dark-mode .tutorial-content {
    color: var(--dark-text);
}

body.dark-mode .tutorial-content h1,
body.dark-mode .tutorial-content h2,
body.dark-mode .tutorial-content h3,
body.dark-mode .tutorial-content h4,
body.dark-mode .tutorial-content h5,
body.dark-mode .tutorial-content h6 {
    color: var(--dark-success);
}

body.dark-mode .tutorial-content p {
    color: var(--dark-text);
}

body.dark-mode .tutorial-content a {
    color: var(--dark-success);
}

body.dark-mode .tutorial-content a:hover {
    color: #20c997;
}

body.dark-mode .tutorial-content blockquote {
    background-color: var(--dark-hover);
    border-left-color: var(--dark-success);
    color: var(--dark-text);
}

body.dark-mode .tutorial-content code {
    background-color: var(--dark-hover);
    color: #f687b3;
}

body.dark-mode .tutorial-content pre {
    background-color: var(--dark-hover);
    color: var(--dark-text);
}

/* Tutorial Category Filter Dark Mode */
body.dark-mode .tutorial-category-filter {
    background: linear-gradient(135deg, var(--dark-success) 0%, #20c997 100%);
}

body.dark-mode .tutorial-category-filter .form-control {
    background-color: var(--dark-input-bg);
    border-color: var(--dark-border);
    color: var(--dark-text);
}

body.dark-mode .tutorial-category-filter .btn {
    background-color: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.3);
    color: #ffffff;
}

body.dark-mode .tutorial-category-filter .btn:hover {
    background-color: rgba(255, 255, 255, 0.3);
    border-color: rgba(255, 255, 255, 0.5);
}

/* Tutorial Empty State Dark Mode */
body.dark-mode .tutorial-empty-state {
    color: var(--dark-text-secondary);
}

body.dark-mode .tutorial-empty-state i {
    color: var(--dark-text-secondary);
}

body.dark-mode .tutorial-empty-state h4 {
    color: var(--dark-text-secondary);
}

body.dark-mode .tutorial-empty-state p {
    color: var(--dark-text-secondary);
}

/* Tutorial Setup Alert Dark Mode */
body.dark-mode .alert-warning {
    background-color: rgba(255, 193, 7, 0.15);
    border-color: rgba(255, 193, 7, 0.4);
    color: var(--dark-text);
}

body.dark-mode .alert-warning .alert-heading {
    color: var(--dark-text);
}

body.dark-mode .alert-warning hr {
    border-color: rgba(255, 193, 7, 0.4);
}

/* Admin Tutorial Styles Dark Mode */
body.dark-mode .admin-tutorial-table .table {
    color: var(--dark-text);
}

body.dark-mode .admin-tutorial-table .table th {
    background-color: var(--dark-hover);
    color: var(--dark-text);
    border-color: var(--dark-border);
}

body.dark-mode .admin-tutorial-table .table td {
    border-color: var(--dark-border);
    color: var(--dark-text);
}

body.dark-mode .tutorial-guidelines {
    background-color: var(--dark-hover);
    color: var(--dark-text);
}

body.dark-mode .tutorial-guidelines li {
    border-bottom-color: var(--dark-border);
}

body.dark-mode .tutorial-order-input {
    background-color: var(--dark-input-bg);
    border-color: var(--dark-border);
    color: var(--dark-text);
}

body.dark-mode .tutorial-order-input:focus {
    background-color: var(--dark-input-bg);
    border-color: var(--dark-primary);
    color: var(--dark-text);
}

/* Tutorial Media Badges Dark Mode */
body.dark-mode .tutorial-media-badges .badge-info {
    background-color: var(--dark-info);
}

body.dark-mode .tutorial-media-badges .badge-primary {
    background-color: var(--dark-primary);
}

/* Tutorial Status Badges Dark Mode */
body.dark-mode .tutorial-status-badge.badge-success {
    background-color: var(--dark-success);
}

body.dark-mode .tutorial-status-badge.badge-secondary {
    background-color: #555555;
}

/* Transitions for smooth theme switching */
body, .card, .navbar, .sidebar, .form-control, .btn, .alert, .modal-content, .table, .jumbotron, .leaderboard-stats-card, .rank-badge, .tutorial-card, .tutorial-content {
    transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
}
