<?php
// This script tests MySQL connection with different host settings

echo "<h1>MySQL Connection Test</h1>";

// Test different host configurations
$hosts = [
    'localhost' => 'Standard hostname',
    '127.0.0.1' => 'IP address',
    'localhost:3306' => 'Hostname with explicit port',
    '127.0.0.1:3306' => 'IP address with explicit port'
];

// Default credentials
$user = 'root';
$pass = '';
$dbname = 'p2p_donate';

echo "<h2>Testing MySQL Connection</h2>";
echo "<p>Using username: <strong>{$user}</strong></p>";
echo "<p>Using password: <strong>" . ($pass === '' ? 'empty (no password)' : 'provided') . "</strong></p>";
echo "<p>Database name: <strong>{$dbname}</strong></p>";

echo "<h2>Connection Results</h2>";
echo "<table border='1' cellpadding='10' style='border-collapse: collapse;'>";
echo "<tr><th>Host</th><th>Description</th><th>Result</th><th>Details</th></tr>";

foreach ($hosts as $host => $description) {
    echo "<tr>";
    echo "<td>{$host}</td>";
    echo "<td>{$description}</td>";
    
    try {
        // First test if the port is open
        $parts = explode(':', $host);
        $hostname = $parts[0];
        $port = isset($parts[1]) ? $parts[1] : 3306;
        
        $socket = @fsockopen($hostname, $port, $errno, $errstr, 5);
        
        if (!$socket) {
            echo "<td style='background-color: #f8d7da;'>Failed</td>";
            echo "<td>Could not connect to MySQL server at {$host}: {$errstr} (Error code: {$errno})</td>";
        } else {
            fclose($socket);
            
            // Now try to connect to MySQL
            $dsn = "mysql:host={$host};";
            $options = [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_TIMEOUT => 5
            ];
            
            $pdo = new PDO($dsn, $user, $pass, $options);
            echo "<td style='background-color: #d4edda;'>Success</td>";
            echo "<td>Successfully connected to MySQL server at {$host}</td>";
            
            // Try to select the database
            try {
                $pdo = new PDO("mysql:host={$host};dbname={$dbname}", $user, $pass, $options);
                echo "</tr><tr><td colspan='2'>Database '{$dbname}'</td>";
                echo "<td style='background-color: #d4edda;'>Success</td>";
                echo "<td>Successfully connected to database '{$dbname}'</td>";
                
                // Check if users table exists
                $stmt = $pdo->query("SHOW TABLES LIKE 'users'");
                if ($stmt->rowCount() > 0) {
                    echo "</tr><tr><td colspan='2'>Table 'users'</td>";
                    echo "<td style='background-color: #d4edda;'>Exists</td>";
                    echo "<td>The 'users' table exists in the database</td>";
                } else {
                    echo "</tr><tr><td colspan='2'>Table 'users'</td>";
                    echo "<td style='background-color: #f8d7da;'>Missing</td>";
                    echo "<td>The 'users' table does not exist in the database</td>";
                }
            } catch (PDOException $e) {
                echo "</tr><tr><td colspan='2'>Database '{$dbname}'</td>";
                echo "<td style='background-color: #f8d7da;'>Failed</td>";
                echo "<td>Could not connect to database '{$dbname}': " . $e->getMessage() . "</td>";
            }
        }
    } catch (PDOException $e) {
        echo "<td style='background-color: #f8d7da;'>Failed</td>";
        echo "<td>Error: " . $e->getMessage() . "</td>";
    }
    
    echo "</tr>";
}

echo "</table>";

echo "<h2>Recommendations</h2>";
echo "<ol>";
echo "<li>Make sure the MySQL server is running</li>";
echo "<li>If using XAMPP/WAMP, check the control panel to ensure MySQL service is started</li>";
echo "<li>Try using the host setting that worked in the test above in your config/config.php file</li>";
echo "<li>If no connection worked, check if MySQL is installed and running on a different port</li>";
echo "<li>If the database connection succeeded but the database or table is missing, you need to create the database and import the schema</li>";
echo "</ol>";

echo "<h2>How to Create the Database and Import the Schema</h2>";
echo "<ol>";
echo "<li>Open phpMyAdmin (usually at http://localhost/phpmyadmin)</li>";
echo "<li>Click on 'New' in the left sidebar to create a new database</li>";
echo "<li>Enter 'p2p_donate' as the database name and click 'Create'</li>";
echo "<li>Select the 'p2p_donate' database from the left sidebar</li>";
echo "<li>Click on the 'Import' tab at the top</li>";
echo "<li>Click 'Choose File' and select the 'database/schema.sql' file from your project</li>";
echo "<li>Click 'Go' to import the schema</li>";
echo "</ol>";
?>
