<?php
/**
 * User Migration Script
 * 
 * This script migrates users from a source database to a destination database.
 * It handles conflicts and provides detailed reporting.
 */

// Set error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Start time for performance tracking
$start_time = microtime(true);

// Source database configuration
$source_db_host = 'localhost';
$source_db_name = 'p2p_donate_source';
$source_db_user = 'root';
$source_db_pass = '';

// Destination database configuration
$dest_db_host = 'localhost';
$dest_db_name = 'p2p_donate';
$dest_db_user = 'root';
$dest_db_pass = '';

// Statistics
$total_users = 0;
$migrated_users = 0;
$skipped_users = 0;
$error_users = 0;

// Connect to source database
try {
    $source_dsn = "mysql:host=$source_db_host;dbname=$source_db_name;charset=utf8mb4";
    $source_options = [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        PDO::ATTR_EMULATE_PREPARES => false,
    ];
    $source_db = new PDO($source_dsn, $source_db_user, $source_db_pass, $source_options);
    echo "Connected to source database: $source_db_name\n";
} catch (PDOException $e) {
    die("Source database connection failed: " . $e->getMessage() . "\n");
}

// Connect to destination database
try {
    $dest_dsn = "mysql:host=$dest_db_host;dbname=$dest_db_name;charset=utf8mb4";
    $dest_options = [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        PDO::ATTR_EMULATE_PREPARES => false,
    ];
    $dest_db = new PDO($dest_dsn, $dest_db_user, $dest_db_pass, $dest_options);
    echo "Connected to destination database: $dest_db_name\n";
} catch (PDOException $e) {
    die("Destination database connection failed: " . $e->getMessage() . "\n");
}

// Check if users table exists in source database
try {
    $stmt = $source_db->query("SHOW TABLES LIKE 'users'");
    if ($stmt->rowCount() == 0) {
        die("Error: 'users' table does not exist in source database.\n");
    }
} catch (PDOException $e) {
    die("Error checking source tables: " . $e->getMessage() . "\n");
}

// Check if users table exists in destination database
try {
    $stmt = $dest_db->query("SHOW TABLES LIKE 'users'");
    if ($stmt->rowCount() == 0) {
        die("Error: 'users' table does not exist in destination database.\n");
    }
} catch (PDOException $e) {
    die("Error checking destination tables: " . $e->getMessage() . "\n");
}

// Get column information from source database
try {
    $stmt = $source_db->query("DESCRIBE users");
    $source_columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
    echo "Source database 'users' table has " . count($source_columns) . " columns\n";
} catch (PDOException $e) {
    die("Error getting source columns: " . $e->getMessage() . "\n");
}

// Get column information from destination database
try {
    $stmt = $dest_db->query("DESCRIBE users");
    $dest_columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
    echo "Destination database 'users' table has " . count($dest_columns) . " columns\n";
} catch (PDOException $e) {
    die("Error getting destination columns: " . $e->getMessage() . "\n");
}

// Find common columns
$common_columns = array_intersect($source_columns, $dest_columns);
echo "Found " . count($common_columns) . " common columns to migrate\n";

// Check if essential columns exist
$essential_columns = ['name', 'email', 'password'];
foreach ($essential_columns as $column) {
    if (!in_array($column, $common_columns)) {
        die("Error: Essential column '$column' is not present in both databases.\n");
    }
}

// Get all users from source database
try {
    $stmt = $source_db->query("SELECT * FROM users");
    $users = $stmt->fetchAll();
    $total_users = count($users);
    echo "Found $total_users users in source database\n";
} catch (PDOException $e) {
    die("Error fetching users from source database: " . $e->getMessage() . "\n");
}

// Start transaction in destination database
$dest_db->beginTransaction();

try {
    // Prepare column list for SQL
    $columns_list = implode(', ', $common_columns);
    $placeholders = implode(', ', array_fill(0, count($common_columns), '?'));
    
    // Prepare insert statement
    $insert_sql = "INSERT INTO users ($columns_list) VALUES ($placeholders)";
    $insert_stmt = $dest_db->prepare($insert_sql);
    
    // Prepare update statement for handling duplicates
    $update_parts = [];
    foreach ($common_columns as $column) {
        if ($column != 'id' && $column != 'email') { // Skip primary key and unique email
            $update_parts[] = "$column = VALUES($column)";
        }
    }
    $update_sql = "INSERT INTO users ($columns_list) VALUES ($placeholders) ON DUPLICATE KEY UPDATE " . implode(', ', $update_parts);
    $update_stmt = $dest_db->prepare($update_sql);
    
    // Process each user
    foreach ($users as $user) {
        try {
            // Check if user already exists
            $check_stmt = $dest_db->prepare("SELECT id FROM users WHERE email = ?");
            $check_stmt->execute([$user['email']]);
            $exists = $check_stmt->fetch();
            
            // Extract values for common columns
            $values = [];
            foreach ($common_columns as $column) {
                $values[] = isset($user[$column]) ? $user[$column] : null;
            }
            
            if ($exists) {
                // User exists, update if needed
                echo "User with email {$user['email']} already exists (ID: {$exists['id']}), updating...\n";
                $update_stmt->execute($values);
                $skipped_users++;
            } else {
                // New user, insert
                $insert_stmt->execute($values);
                $migrated_users++;
                echo "Migrated user: {$user['name']} ({$user['email']})\n";
            }
        } catch (PDOException $e) {
            echo "Error processing user {$user['email']}: " . $e->getMessage() . "\n";
            $error_users++;
        }
    }
    
    // Commit transaction
    $dest_db->commit();
    echo "Transaction committed successfully\n";
} catch (PDOException $e) {
    // Rollback transaction on error
    $dest_db->rollBack();
    echo "Transaction rolled back due to error: " . $e->getMessage() . "\n";
}

// Calculate execution time
$end_time = microtime(true);
$execution_time = ($end_time - $start_time);

// Display summary
echo "\n=== Migration Summary ===\n";
echo "Total users in source database: $total_users\n";
echo "Successfully migrated new users: $migrated_users\n";
echo "Updated existing users: $skipped_users\n";
echo "Errors encountered: $error_users\n";
echo "Execution time: " . number_format($execution_time, 2) . " seconds\n";
echo "========================\n";
?>
