<?php
// Include configuration
require_once __DIR__ . '/../../config/config.php';
require_once __DIR__ . '/../../database/db_connect.php';

// Create database instance
$database = new Database();
$db = $database->getConnection();

echo "Adding mobile money columns to users table...\n";

// First check if the columns already exist
$query = "SHOW COLUMNS FROM users LIKE 'mobile_money_number'";
$stmt = $db->prepare($query);
$stmt->execute();
$mobile_money_number_exists = $stmt->rowCount() > 0;

$query = "SHOW COLUMNS FROM users LIKE 'mobile_money_name'";
$stmt = $db->prepare($query);
$stmt->execute();
$mobile_money_name_exists = $stmt->rowCount() > 0;

// If the columns don't exist, add them
if (!$mobile_money_number_exists) {
    echo "Adding mobile_money_number column...\n";
    $query = "ALTER TABLE users ADD COLUMN mobile_money_number VARCHAR(20)";
    $stmt = $db->prepare($query);

    if ($stmt->execute()) {
        echo "mobile_money_number column added successfully.\n";

        // Copy data from mobile_number to mobile_money_number
        echo "Copying data from mobile_number to mobile_money_number...\n";
        $query = "UPDATE users SET mobile_money_number = mobile_number WHERE mobile_number IS NOT NULL";
        $stmt = $db->prepare($query);

        if ($stmt->execute()) {
            echo "Data copied successfully.\n";
        } else {
            echo "Error copying data: " . print_r($stmt->errorInfo(), true) . "\n";
        }
    } else {
        echo "Error adding mobile_money_number column: " . print_r($stmt->errorInfo(), true) . "\n";
    }
}

if (!$mobile_money_name_exists) {
    echo "Adding mobile_money_name column...\n";
    $query = "ALTER TABLE users ADD COLUMN mobile_money_name VARCHAR(100)";
    $stmt = $db->prepare($query);

    if ($stmt->execute()) {
        echo "mobile_money_name column added successfully.\n";

        // Copy data from mobile_name to mobile_money_name
        echo "Copying data from mobile_name to mobile_money_name...\n";
        $query = "UPDATE users SET mobile_money_name = mobile_name WHERE mobile_name IS NOT NULL";
        $stmt = $db->prepare($query);

        if ($stmt->execute()) {
            echo "Data copied successfully.\n";
        } else {
            echo "Error copying data: " . print_r($stmt->errorInfo(), true) . "\n";
        }
    } else {
        echo "Error adding mobile_money_name column: " . print_r($stmt->errorInfo(), true) . "\n";
    }
}

echo "Done!\n";
?>
