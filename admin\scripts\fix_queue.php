<?php
// This script fixes users who have received more than 2 pledges and are still in the queue

// Include configuration
require_once __DIR__ . '/../../config/config.php';
require_once __DIR__ . '/../../database/db_connect.php';
require_once __DIR__ . '/../../includes/functions.php';

// Create database instance
$database = new Database();
$db = $database->getConnection();

echo "Starting queue fix script...\n";

// Find users who are in the queue (pledges_to_receive > 0)
$query = "SELECT u.id, u.name, u.email, u.pledges_to_receive,
          (SELECT COUNT(*) FROM matches WHERE receiver_id = u.id AND status = 'completed') as pledges_received
          FROM users u
          WHERE u.pledges_to_receive > 0
          ORDER BY u.updated_at ASC";
$stmt = $db->prepare($query);
$stmt->execute();
$users = $stmt->fetchAll(PDO::FETCH_OBJ);

echo "Found " . count($users) . " users in the queue.\n\n";

$fixed_count = 0;

foreach ($users as $user) {
    echo "User: {$user->name} (ID: {$user->id})\n";
    echo "  Pledges to receive: {$user->pledges_to_receive}\n";
    echo "  Pledges received: {$user->pledges_received}\n";
    
    // Check if user has received more than 2 pledges
    if ($user->pledges_received >= 2) {
        echo "  ISSUE: User has received {$user->pledges_received} pledges but is still in queue with {$user->pledges_to_receive} pledges to receive.\n";
        
        // Fix: Remove user from queue
        $query = "UPDATE users SET pledges_to_receive = 0 WHERE id = :user_id";
        $stmt = $db->prepare($query);
        $stmt->bindParam(':user_id', $user->id);
        
        if ($stmt->execute()) {
            echo "  FIXED: User removed from queue.\n";
            $fixed_count++;
        } else {
            echo "  ERROR: Failed to remove user from queue.\n";
        }
    } else {
        // Check if user has received more pledges than they should have remaining
        $expected_remaining = 2 - $user->pledges_received;
        
        if ($user->pledges_to_receive > $expected_remaining && $expected_remaining > 0) {
            echo "  ISSUE: User has received {$user->pledges_received} pledges but has {$user->pledges_to_receive} pledges to receive (should be {$expected_remaining}).\n";
            
            // Fix: Update pledges_to_receive to correct value
            $query = "UPDATE users SET pledges_to_receive = :count WHERE id = :user_id";
            $stmt = $db->prepare($query);
            $stmt->bindParam(':count', $expected_remaining);
            $stmt->bindParam(':user_id', $user->id);
            
            if ($stmt->execute()) {
                echo "  FIXED: Updated pledges_to_receive to {$expected_remaining}.\n";
                $fixed_count++;
            } else {
                echo "  ERROR: Failed to update pledges_to_receive.\n";
            }
        } else if ($expected_remaining <= 0) {
            echo "  ISSUE: User has received {$user->pledges_received} pledges but is still in queue with {$user->pledges_to_receive} pledges to receive (should be 0).\n";
            
            // Fix: Remove user from queue
            $query = "UPDATE users SET pledges_to_receive = 0 WHERE id = :user_id";
            $stmt = $db->prepare($query);
            $stmt->bindParam(':user_id', $user->id);
            
            if ($stmt->execute()) {
                echo "  FIXED: User removed from queue.\n";
                $fixed_count++;
            } else {
                echo "  ERROR: Failed to remove user from queue.\n";
            }
        } else {
            echo "  OK: User has correct number of pledges to receive.\n";
        }
    }
    
    echo "\n";
}

echo "Queue fix completed. Fixed {$fixed_count} users.\n";
?>
