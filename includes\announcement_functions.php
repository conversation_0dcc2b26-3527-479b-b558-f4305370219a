<?php
/**
 * Announcement Helper Functions for P2P Donate Platform
 * Handles video embedding, link previews, and enhanced announcement features
 */

/**
 * Validate and extract video ID from popular video platforms
 * @param string $url Video URL
 * @return array|false Array with platform and video_id, or false if invalid
 */
function validate_video_url($url) {
    if (empty($url)) {
        return false;
    }
    
    // YouTube patterns
    $youtube_patterns = [
        '/(?:youtube\.com\/watch\?v=|youtu\.be\/|youtube\.com\/embed\/)([a-zA-Z0-9_-]{11})/',
        '/youtube\.com\/watch\?.*v=([a-zA-Z0-9_-]{11})/'
    ];
    
    foreach ($youtube_patterns as $pattern) {
        if (preg_match($pattern, $url, $matches)) {
            return [
                'platform' => 'youtube',
                'video_id' => $matches[1],
                'embed_url' => "https://www.youtube.com/embed/{$matches[1]}"
            ];
        }
    }
    
    // Vimeo patterns
    $vimeo_patterns = [
        '/vimeo\.com\/(\d+)/',
        '/player\.vimeo\.com\/video\/(\d+)/'
    ];
    
    foreach ($vimeo_patterns as $pattern) {
        if (preg_match($pattern, $url, $matches)) {
            return [
                'platform' => 'vimeo',
                'video_id' => $matches[1],
                'embed_url' => "https://player.vimeo.com/video/{$matches[1]}"
            ];
        }
    }
    
    return false;
}

/**
 * Generate responsive video embed HTML
 * @param string $url Video URL
 * @param string $title Video title for accessibility
 * @return string|false HTML embed code or false if invalid
 */
function generate_video_embed($url, $title = 'Video') {
    $video_data = validate_video_url($url);
    
    if (!$video_data) {
        return false;
    }
    
    $embed_url = $video_data['embed_url'];
    $platform = $video_data['platform'];
    
    // Add platform-specific parameters
    if ($platform === 'youtube') {
        $embed_url .= '?rel=0&modestbranding=1&showinfo=0';
    }
    
    return '
    <div class="video-embed-container">
        <div class="embed-responsive embed-responsive-16by9">
            <iframe class="embed-responsive-item" 
                    src="' . htmlspecialchars($embed_url) . '" 
                    title="' . htmlspecialchars($title) . '"
                    frameborder="0" 
                    allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" 
                    allowfullscreen>
            </iframe>
        </div>
    </div>';
}

/**
 * Extract links from text content
 * @param string $text Text content
 * @return array Array of URLs found in text
 */
function extract_links_from_text($text) {
    $pattern = '/https?:\/\/[^\s<>"\']+/i';
    preg_match_all($pattern, $text, $matches);
    return array_unique($matches[0]);
}

/**
 * Fetch Open Graph metadata from URL
 * @param string $url URL to fetch metadata from
 * @return array|false Array with metadata or false if failed
 */
function fetch_link_preview($url) {
    if (!filter_var($url, FILTER_VALIDATE_URL)) {
        return false;
    }
    
    // Set up cURL with proper headers
    $ch = curl_init();
    curl_setopt_array($ch, [
        CURLOPT_URL => $url,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_FOLLOWLOCATION => true,
        CURLOPT_MAXREDIRS => 3,
        CURLOPT_TIMEOUT => 10,
        CURLOPT_USERAGENT => 'P2P Donate Link Preview Bot/1.0',
        CURLOPT_SSL_VERIFYPEER => false,
        CURLOPT_HTTPHEADER => [
            'Accept: text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'Accept-Language: en-US,en;q=0.5',
            'Accept-Encoding: gzip, deflate',
            'Connection: keep-alive',
        ]
    ]);
    
    $html = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    if ($http_code !== 200 || !$html) {
        return false;
    }
    
    // Parse HTML for Open Graph and meta tags
    $dom = new DOMDocument();
    @$dom->loadHTML($html);
    $xpath = new DOMXPath($dom);
    
    $preview_data = [
        'url' => $url,
        'title' => '',
        'description' => '',
        'image' => '',
        'site_name' => ''
    ];
    
    // Extract Open Graph data
    $og_tags = [
        'title' => '//meta[@property="og:title"]/@content',
        'description' => '//meta[@property="og:description"]/@content',
        'image' => '//meta[@property="og:image"]/@content',
        'site_name' => '//meta[@property="og:site_name"]/@content'
    ];
    
    foreach ($og_tags as $key => $query) {
        $nodes = $xpath->query($query);
        if ($nodes->length > 0) {
            $preview_data[$key] = trim($nodes->item(0)->nodeValue);
        }
    }
    
    // Fallback to regular meta tags if Open Graph not available
    if (empty($preview_data['title'])) {
        $title_nodes = $xpath->query('//title');
        if ($title_nodes->length > 0) {
            $preview_data['title'] = trim($title_nodes->item(0)->nodeValue);
        }
    }
    
    if (empty($preview_data['description'])) {
        $desc_nodes = $xpath->query('//meta[@name="description"]/@content');
        if ($desc_nodes->length > 0) {
            $preview_data['description'] = trim($desc_nodes->item(0)->nodeValue);
        }
    }
    
    // Validate and clean data
    $preview_data['title'] = substr($preview_data['title'], 0, 200);
    $preview_data['description'] = substr($preview_data['description'], 0, 300);
    
    // Validate image URL
    if (!empty($preview_data['image']) && !filter_var($preview_data['image'], FILTER_VALIDATE_URL)) {
        $preview_data['image'] = '';
    }
    
    return $preview_data;
}

/**
 * Generate link preview HTML
 * @param array $preview_data Preview data from fetch_link_preview()
 * @return string HTML for link preview
 */
function generate_link_preview_html($preview_data) {
    if (empty($preview_data) || empty($preview_data['title'])) {
        return '';
    }
    
    $html = '<div class="link-preview-card">';
    
    if (!empty($preview_data['image'])) {
        $html .= '<div class="link-preview-image">';
        $html .= '<img src="' . htmlspecialchars($preview_data['image']) . '" alt="Preview image" loading="lazy">';
        $html .= '</div>';
    }
    
    $html .= '<div class="link-preview-content">';
    $html .= '<h6 class="link-preview-title">' . htmlspecialchars($preview_data['title']) . '</h6>';
    
    if (!empty($preview_data['description'])) {
        $html .= '<p class="link-preview-description">' . htmlspecialchars($preview_data['description']) . '</p>';
    }
    
    if (!empty($preview_data['site_name'])) {
        $html .= '<small class="link-preview-site">' . htmlspecialchars($preview_data['site_name']) . '</small>';
    }
    
    $html .= '<a href="' . htmlspecialchars($preview_data['url']) . '" target="_blank" rel="noopener noreferrer" class="link-preview-url">';
    $html .= '<i class="fas fa-external-link-alt"></i> Visit Link';
    $html .= '</a>';
    $html .= '</div>';
    $html .= '</div>';
    
    return $html;
}

/**
 * Get priority badge class and icon
 * @param string $priority Priority level (urgent, normal, info)
 * @return array Array with badge_class and icon
 */
function get_priority_styling($priority) {
    switch ($priority) {
        case 'urgent':
            return [
                'badge_class' => 'badge-danger',
                'icon' => 'fas fa-exclamation-triangle',
                'alert_class' => 'alert-danger',
                'color' => '#dc3545'
            ];
        case 'info':
            return [
                'badge_class' => 'badge-info',
                'icon' => 'fas fa-info-circle',
                'alert_class' => 'alert-info',
                'color' => '#17a2b8'
            ];
        case 'normal':
        default:
            return [
                'badge_class' => 'badge-success',
                'icon' => 'fas fa-bullhorn',
                'alert_class' => 'alert-success',
                'color' => '#28a745'
            ];
    }
}

/**
 * Process announcement content for links and generate previews
 * @param string $content Announcement content
 * @return array Array with processed content and link previews
 */
function process_announcement_content($content) {
    $links = extract_links_from_text($content);
    $link_previews = [];
    
    foreach ($links as $link) {
        $preview = fetch_link_preview($link);
        if ($preview) {
            $link_previews[] = $preview;
        }
    }
    
    return [
        'content' => $content,
        'links' => $links,
        'previews' => $link_previews
    ];
}
?>
