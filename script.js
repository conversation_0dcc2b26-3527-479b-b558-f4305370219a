/**
 * P2P Donate - Main JavaScript
 * Handles interactive elements on the landing page
 */

document.addEventListener('DOMContentLoaded', function() {
    // FAQ Accordion
    const faqItems = document.querySelectorAll('.faq-item');

    if (faqItems.length > 0) {
        faqItems.forEach(item => {
            const question = item.querySelector('.faq-question');

            question.addEventListener('click', () => {
                // Close all other items
                faqItems.forEach(otherItem => {
                    if (otherItem !== item && otherItem.classList.contains('active')) {
                        otherItem.classList.remove('active');
                    }
                });

                // Toggle current item
                item.classList.toggle('active');
            });
        });

        // Open the first FAQ item by default
        faqItems[0].classList.add('active');
    }
    // Mobile Menu Toggle
    const mobileMenuToggle = document.querySelector('.mobile-menu-toggle');
    const navLinks = document.querySelector('.nav-links');
    const headerElement = document.querySelector('header');
    const body = document.body;

    // Check if we're on an iPad Air or similar device (820px width)
    const isIPadAir = window.matchMedia('(width: 820px) and (height: 1180px)').matches ||
                      window.matchMedia('(width: 1180px) and (height: 820px)').matches;

    // Only enable mobile menu toggle on non-iPad Air devices
    if (mobileMenuToggle && !isIPadAir) {
        mobileMenuToggle.addEventListener('click', function() {
            this.classList.toggle('active');

            // Toggle mobile menu
            if (navLinks) {
                navLinks.classList.toggle('active');

                // Prevent body scrolling when menu is open
                if (navLinks.classList.contains('active')) {
                    body.style.overflow = 'hidden';
                    // Ensure header is visible when menu is open
                    headerElement.style.transform = 'translateY(0)';
                } else {
                    body.style.overflow = '';
                }
            }
        });

        // Close menu when clicking on a menu item
        if (navLinks) {
            const menuItems = navLinks.querySelectorAll('a');
            menuItems.forEach(item => {
                item.addEventListener('click', () => {
                    mobileMenuToggle.classList.remove('active');
                    navLinks.classList.remove('active');
                    body.style.overflow = '';
                });
            });
        }
    }

    // Smooth scrolling for anchor links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function(e) {
            e.preventDefault();

            const targetId = this.getAttribute('href');

            if (targetId === '#') return;

            const targetElement = document.querySelector(targetId);

            if (targetElement) {
                // Close mobile menu if open
                if (navLinks && navLinks.classList.contains('active')) {
                    mobileMenuToggle.classList.remove('active');
                    navLinks.classList.remove('active');
                    body.style.overflow = '';

                    // Add a small delay for mobile menu to close before scrolling
                    setTimeout(() => {
                        scrollToTarget(targetElement);
                    }, 300);
                } else {
                    scrollToTarget(targetElement);
                }
            }
        });
    });

    // Helper function for scrolling to target
    function scrollToTarget(targetElement) {
        // Calculate header height for offset
        const headerHeight = document.querySelector('header').offsetHeight;

        window.scrollTo({
            top: targetElement.offsetTop - headerHeight,
            behavior: 'smooth'
        });
    }

    // Sticky header with scroll effect
    let lastScrollTop = 0;

    window.addEventListener('scroll', function() {
        const scrollTop = window.pageYOffset || document.documentElement.scrollTop;

        if (scrollTop > 100) {
            headerElement.classList.add('scrolled');
        } else {
            headerElement.classList.remove('scrolled');
        }

        // Only hide header when scrolling down if mobile menu is not active
        if (scrollTop > lastScrollTop && scrollTop > 200 && !navLinks.classList.contains('active')) {
            // Scrolling down
            headerElement.style.transform = 'translateY(-100%)';
        } else {
            // Scrolling up or mobile menu is active
            headerElement.style.transform = 'translateY(0)';
        }

        lastScrollTop = scrollTop <= 0 ? 0 : scrollTop;
    });

    // Testimonial slider
    const testimonialSlider = document.querySelector('.testimonial-slider');

    if (testimonialSlider) {
        let isDown = false;
        let startX;
        let scrollLeft;

        testimonialSlider.addEventListener('mousedown', (e) => {
            isDown = true;
            testimonialSlider.classList.add('active');
            startX = e.pageX - testimonialSlider.offsetLeft;
            scrollLeft = testimonialSlider.scrollLeft;
        });

        testimonialSlider.addEventListener('mouseleave', () => {
            isDown = false;
            testimonialSlider.classList.remove('active');
        });

        testimonialSlider.addEventListener('mouseup', () => {
            isDown = false;
            testimonialSlider.classList.remove('active');
        });

        testimonialSlider.addEventListener('mousemove', (e) => {
            if (!isDown) return;
            e.preventDefault();
            const x = e.pageX - testimonialSlider.offsetLeft;
            const walk = (x - startX) * 2;
            testimonialSlider.scrollLeft = scrollLeft - walk;
        });

        // Auto scroll testimonials
        let scrollInterval;

        function startAutoScroll() {
            scrollInterval = setInterval(() => {
                testimonialSlider.scrollLeft += 1;

                // Reset scroll position when reaching the end
                if (testimonialSlider.scrollLeft >= testimonialSlider.scrollWidth - testimonialSlider.clientWidth) {
                    testimonialSlider.scrollLeft = 0;
                }
            }, 20);
        }

        function stopAutoScroll() {
            clearInterval(scrollInterval);
        }

        // Start auto scroll
        startAutoScroll();

        // Pause auto scroll when interacting with the slider
        testimonialSlider.addEventListener('mouseenter', stopAutoScroll);
        testimonialSlider.addEventListener('mouseleave', startAutoScroll);
        testimonialSlider.addEventListener('touchstart', stopAutoScroll);
        testimonialSlider.addEventListener('touchend', startAutoScroll);
    }

    // Form submission
    const signupForm = document.querySelector('.signup-form');
    const newsletterForm = document.querySelector('.newsletter-form');

    if (signupForm) {
        signupForm.addEventListener('submit', function(e) {
            e.preventDefault();

            // Simulate form submission
            const submitButton = this.querySelector('button[type="submit"]');
            const originalText = submitButton.textContent;

            submitButton.disabled = true;
            submitButton.textContent = 'Processing...';

            // Simulate API call
            setTimeout(() => {
                // Show success message
                const formGroups = this.querySelectorAll('.form-group');
                formGroups.forEach(group => {
                    group.style.display = 'none';
                });

                const successMessage = document.createElement('div');
                successMessage.className = 'alert alert-success';
                successMessage.textContent = 'Thank you for signing up! We will contact you shortly.';

                this.appendChild(successMessage);

                // Reset form after delay
                setTimeout(() => {
                    this.reset();
                    formGroups.forEach(group => {
                        group.style.display = 'block';
                    });
                    successMessage.remove();
                    submitButton.disabled = false;
                    submitButton.textContent = originalText;
                }, 5000);
            }, 2000);
        });
    }

    if (newsletterForm) {
        newsletterForm.addEventListener('submit', function(e) {
            e.preventDefault();

            // Simulate form submission
            const submitButton = this.querySelector('button[type="submit"]');
            const originalText = submitButton.textContent;
            const emailInput = this.querySelector('input[type="email"]');

            submitButton.disabled = true;
            submitButton.textContent = '...';

            // Simulate API call
            setTimeout(() => {
                // Show success message
                emailInput.value = '';
                submitButton.textContent = 'Subscribed!';

                // Reset button after delay
                setTimeout(() => {
                    submitButton.disabled = false;
                    submitButton.textContent = originalText;
                }, 3000);
            }, 1500);
        });
    }

    // Contact form handling removed

    // Animation on scroll with improved effects
    const animateElements = document.querySelectorAll('.stat-card, .story-card, .step, .feature, .testimonial, .faq-item');

    // Function to check if element is in viewport
    function isInViewport(element) {
        const rect = element.getBoundingClientRect();
        return (
            rect.top <= (window.innerHeight || document.documentElement.clientHeight) * 0.85 &&
            rect.bottom >= 0
        );
    }

    // Function to handle scroll animation
    function handleScrollAnimation() {
        animateElements.forEach(element => {
            if (isInViewport(element) && !element.classList.contains('animated')) {
                element.classList.add('animated');

                // Add staggered animation for siblings
                if (element.parentElement.children.length > 1) {
                    const siblings = Array.from(element.parentElement.children);
                    const index = siblings.indexOf(element);
                    element.style.animationDelay = `${index * 0.1}s`;
                }
            }
        });
    }

    // Initial check
    handleScrollAnimation();

    // Add scroll event listener
    window.addEventListener('scroll', handleScrollAnimation);

    // Add CSS animations
    const style = document.createElement('style');
    style.textContent = `
        .stat-card, .story-card, .step, .feature, .testimonial, .faq-item {
            opacity: 0;
            transform: translateY(20px);
            transition: opacity 0.6s ease, transform 0.6s ease;
        }
        .stat-card.animated, .story-card.animated, .step.animated,
        .feature.animated, .testimonial.animated, .faq-item.animated {
            opacity: 1;
            transform: translateY(0);
        }

        /* Button hover effects */
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        /* Header animation */
        header.scrolled {
            background-color: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(5px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
        }

        /* Float animation */
        .animate-float {
            animation: float 6s ease-in-out infinite;
        }

        @keyframes float {
            0% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
            100% { transform: translateY(0px); }
        }

        /* Ripple effect for buttons */
        .btn {
            position: relative;
            overflow: hidden;
        }

        .btn::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 5px;
            height: 5px;
            background: rgba(255, 255, 255, 0.5);
            opacity: 0;
            border-radius: 100%;
            transform: scale(1, 1) translate(-50%);
            transform-origin: 50% 50%;
        }

        .btn:hover::after {
            animation: ripple 1s ease-out;
        }

        @keyframes ripple {
            0% {
                transform: scale(0, 0);
                opacity: 0.5;
            }
            100% {
                transform: scale(20, 20);
                opacity: 0;
            }
        }
    `;
    document.head.appendChild(style);
});
