<?php
// Set page title
$page_title = 'Tutorial Form';

// Include configuration
require_once '../config/config.php';
require_once '../includes/functions.php';
require_once '../includes/tutorial_functions.php';
require_once '../database/db_connect.php';

// Start session
start_session();

// Check if user is logged in and is admin
if (!is_logged_in() || !is_admin()) {
    redirect('../login.php');
}

// Create database instance
$database = new Database();
$db = $database->getConnection();

// Get user ID
$user_id = $_SESSION['user_id'];

// Check if editing existing tutorial
$tutorial_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;
$is_editing = $tutorial_id > 0;
$tutorial = null;

if ($is_editing) {
    $tutorial = get_tutorial_by_id($db, $tutorial_id);
    if (!$tutorial) {
        redirect('tutorials.php');
    }
    $page_title = 'Edit Tutorial';
} else {
    $page_title = 'Create Tutorial';
}

// Handle form submission
$success_message = '';
$error_message = '';

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    // Validate and sanitize input
    $title = sanitize($_POST['title']);
    $content = sanitize($_POST['content']);
    $category = sanitize($_POST['category']);
    $video_url = sanitize($_POST['video_url'] ?? '');
    $display_order = (int)($_POST['display_order'] ?? 0);
    $status = sanitize($_POST['status']);

    // Initialize file variables
    $image_file = $is_editing ? $tutorial->image_file : null;
    $video_file = $is_editing ? $tutorial->video_file : null;
    $file_errors = [];

    // Handle image upload
    if (isset($_FILES['image_file']) && $_FILES['image_file']['error'] != UPLOAD_ERR_NO_FILE) {
        $upload_result = upload_tutorial_file($_FILES['image_file'], 'image');
        if ($upload_result['success']) {
            // Delete old image if editing
            if ($is_editing && $tutorial->image_file) {
                $old_image = '../uploads/tutorials/' . $tutorial->image_file;
                if (file_exists($old_image)) {
                    unlink($old_image);
                }
            }
            $image_file = $upload_result['filename'];
        } else {
            $file_errors[] = $upload_result['message'];
        }
    }

    // Handle video upload
    if (isset($_FILES['video_file']) && $_FILES['video_file']['error'] != UPLOAD_ERR_NO_FILE) {
        $upload_result = upload_tutorial_file($_FILES['video_file'], 'video');
        if ($upload_result['success']) {
            // Delete old video if editing
            if ($is_editing && $tutorial->video_file) {
                $old_video = '../uploads/tutorials/' . $tutorial->video_file;
                if (file_exists($old_video)) {
                    unlink($old_video);
                }
            }
            $video_file = $upload_result['filename'];
        } else {
            $file_errors[] = $upload_result['message'];
        }
    }

    // Prepare data for validation
    $tutorial_data = [
        'title' => $title,
        'content' => $content,
        'category' => $category,
        'image_file' => $image_file,
        'video_file' => $video_file,
        'video_url' => $video_url,
        'display_order' => $display_order,
        'status' => $status,
        'created_by' => $user_id
    ];

    // Validate data
    $validation_errors = validate_tutorial_data($tutorial_data);
    $all_errors = array_merge($validation_errors, $file_errors);

    if (empty($all_errors)) {
        try {
            if ($is_editing) {
                if (update_tutorial($db, $tutorial_id, $tutorial_data)) {
                    $success_message = 'Tutorial updated successfully.';
                    // Refresh tutorial data
                    $tutorial = get_tutorial_by_id($db, $tutorial_id);
                } else {
                    $error_message = 'Failed to update tutorial.';
                }
            } else {
                if (create_tutorial($db, $tutorial_data)) {
                    $success_message = 'Tutorial created successfully.';
                    // Clear form data
                    $title = $content = $category = $video_url = '';
                    $display_order = 0;
                    $status = 'draft';
                } else {
                    $error_message = 'Failed to create tutorial.';
                }
            }
        } catch (Exception $e) {
            $error_message = 'Error: ' . $e->getMessage();
        }
    } else {
        $error_message = implode('<br>', $all_errors);
    }
}

// Set default values for form
if (!$is_editing && $_SERVER['REQUEST_METHOD'] != 'POST') {
    $title = $content = $category = $video_url = '';
    $display_order = 0;
    $status = 'draft';
} elseif ($is_editing && $_SERVER['REQUEST_METHOD'] != 'POST') {
    $title = $tutorial->title;
    $content = $tutorial->content;
    $category = $tutorial->category;
    $video_url = $tutorial->video_url;
    $display_order = $tutorial->display_order;
    $status = $tutorial->status;
}

// Get existing categories for dropdown
$categories = get_tutorial_categories($db);
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - <?php echo SITE_NAME; ?></title>
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.1/css/all.min.css">
    <link rel="stylesheet" href="../assets/css/admin.css">
    <link rel="stylesheet" href="../assets/css/admin-dark-mode.css">
    <!-- Rich Text Editor -->
    <script src="https://cdn.tiny.cloud/1/no-api-key/tinymce/5/tinymce.min.js" referrerpolicy="origin"></script>
</head>
<body>
    <?php include 'includes/admin_navbar.php'; ?>

    <div class="container-fluid">
        <div class="row">
            <?php include 'includes/admin_sidebar.php'; ?>

            <main role="main" class="col-md-9 ml-sm-auto col-lg-10 px-md-4 py-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">
                        <i class="fas fa-graduation-cap"></i>
                        <?php echo $page_title; ?>
                    </h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <a href="tutorials.php" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-left"></i> Back to Tutorials
                        </a>
                    </div>
                </div>

                <?php if (!empty($success_message)): ?>
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <?php echo $success_message; ?>
                        <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                <?php endif; ?>

                <?php if (!empty($error_message)): ?>
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <?php echo $error_message; ?>
                        <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                <?php endif; ?>

                <div class="row">
                    <div class="col-lg-8">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="fas fa-edit"></i>
                                    Tutorial Details
                                </h5>
                            </div>
                            <div class="card-body">
                                <form action="<?php echo htmlspecialchars($_SERVER["PHP_SELF"]) . ($is_editing ? '?id=' . $tutorial_id : ''); ?>" method="post" enctype="multipart/form-data">
                                    <div class="form-group">
                                        <label for="title">Title <span class="text-danger">*</span></label>
                                        <input type="text" name="title" id="title" class="form-control"
                                               value="<?php echo htmlspecialchars($title); ?>" required>
                                    </div>

                                    <div class="form-group">
                                        <label for="category">Category <span class="text-danger">*</span></label>
                                        <div class="input-group">
                                            <select name="category" id="category" class="form-control" required>
                                                <option value="">Select Category</option>
                                                <?php foreach ($categories as $cat): ?>
                                                    <option value="<?php echo htmlspecialchars($cat); ?>"
                                                            <?php echo $category === $cat ? 'selected' : ''; ?>>
                                                        <?php echo htmlspecialchars(ucfirst($cat)); ?>
                                                    </option>
                                                <?php endforeach; ?>
                                            </select>
                                            <div class="input-group-append">
                                                <button type="button" class="btn btn-outline-secondary" onclick="addNewCategory()">
                                                    <i class="fas fa-plus"></i>
                                                </button>
                                            </div>
                                        </div>
                                        <small class="form-text text-muted">Select existing category or add new one</small>
                                    </div>

                                    <div class="form-group">
                                        <label for="content">Content <span class="text-danger">*</span></label>
                                        <textarea name="content" id="content" class="form-control" rows="10" required><?php echo htmlspecialchars($content); ?></textarea>
                                    </div>

                                    <div class="form-group">
                                        <label for="video_url">Video URL (Optional)</label>
                                        <input type="url" name="video_url" id="video_url" class="form-control"
                                               value="<?php echo htmlspecialchars($video_url); ?>"
                                               placeholder="https://www.youtube.com/watch?v=... or https://vimeo.com/...">
                                        <small class="form-text text-muted">Supports YouTube and Vimeo URLs</small>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label for="display_order">Display Order</label>
                                                <input type="number" name="display_order" id="display_order" class="form-control"
                                                       value="<?php echo $display_order; ?>" min="0">
                                                <small class="form-text text-muted">Lower numbers appear first</small>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label for="status">Status <span class="text-danger">*</span></label>
                                                <select name="status" id="status" class="form-control" required>
                                                    <option value="draft" <?php echo $status === 'draft' ? 'selected' : ''; ?>>Draft</option>
                                                    <option value="published" <?php echo $status === 'published' ? 'selected' : ''; ?>>Published</option>
                                                </select>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label for="image_file">Tutorial Image (Optional)</label>
                                                <div class="custom-file">
                                                    <input type="file" name="image_file" class="custom-file-input" id="image_file" accept=".jpg,.jpeg,.png,.gif">
                                                    <label class="custom-file-label" for="image_file">Choose image</label>
                                                </div>
                                                <small class="form-text text-muted">Accepted formats: JPG, PNG, GIF. Max size: 20MB</small>
                                                <?php if ($is_editing && $tutorial->image_file): ?>
                                                    <div class="mt-2">
                                                        <small class="text-muted">Current: <?php echo htmlspecialchars($tutorial->image_file); ?></small>
                                                        <br>
                                                        <img src="../uploads/tutorials/<?php echo htmlspecialchars($tutorial->image_file); ?>"
                                                             class="img-thumbnail mt-1" style="max-height: 100px;" alt="Current image">
                                                    </div>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label for="video_file">Tutorial Video (Optional)</label>
                                                <div class="custom-file">
                                                    <input type="file" name="video_file" class="custom-file-input" id="video_file" accept=".mp4,.webm,.mov,.avi">
                                                    <label class="custom-file-label" for="video_file">Choose video</label>
                                                </div>
                                                <small class="form-text text-muted">Accepted formats: MP4, WebM, MOV, AVI. Max size: 20MB</small>
                                                <?php if ($is_editing && $tutorial->video_file): ?>
                                                    <div class="mt-2">
                                                        <small class="text-muted">Current: <?php echo htmlspecialchars($tutorial->video_file); ?></small>
                                                    </div>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="form-group">
                                        <button type="submit" class="btn btn-success">
                                            <i class="fas fa-save"></i>
                                            <?php echo $is_editing ? 'Update Tutorial' : 'Create Tutorial'; ?>
                                        </button>
                                        <a href="tutorials.php" class="btn btn-secondary ml-2">
                                            <i class="fas fa-times"></i> Cancel
                                        </a>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>

                    <div class="col-lg-4">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0">
                                    <i class="fas fa-info-circle"></i>
                                    Tutorial Guidelines
                                </h6>
                            </div>
                            <div class="card-body tutorial-guidelines">
                                <ul class="list-unstyled">
                                    <li class="mb-2">
                                        <i class="fas fa-check text-success"></i>
                                        Use clear, descriptive titles
                                    </li>
                                    <li class="mb-2">
                                        <i class="fas fa-check text-success"></i>
                                        Organize content with proper categories
                                    </li>
                                    <li class="mb-2">
                                        <i class="fas fa-check text-success"></i>
                                        Include step-by-step instructions
                                    </li>
                                    <li class="mb-2">
                                        <i class="fas fa-check text-success"></i>
                                        Add images or videos when helpful
                                    </li>
                                    <li class="mb-2">
                                        <i class="fas fa-check text-success"></i>
                                        Use draft status for work in progress
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.5.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.5.2/dist/js/bootstrap.bundle.min.js"></script>
    <script src="../assets/js/admin.js"></script>
    <script>
        // Initialize TinyMCE with dark mode support
        function initTinyMCE() {
            const isDarkMode = document.body.classList.contains('dark-mode');

            tinymce.init({
                selector: '#content',
                height: 400,
                menubar: false,
                skin: isDarkMode ? 'oxide-dark' : 'oxide',
                content_css: isDarkMode ? 'dark' : 'default',
                plugins: [
                    'advlist autolink lists link image charmap print preview anchor',
                    'searchreplace visualblocks code fullscreen',
                    'insertdatetime media table paste code help wordcount'
                ],
                toolbar: 'undo redo | formatselect | bold italic backcolor | alignleft aligncenter alignright alignjustify | bullist numlist outdent indent | removeformat | help',
                content_style: isDarkMode ?
                    'body { font-family:Helvetica,Arial,sans-serif; font-size:14px; background-color: #2c2c2c; color: #e0e0e0; }' :
                    'body { font-family:Helvetica,Arial,sans-serif; font-size:14px; }'
            });
        }

        // Initialize TinyMCE
        initTinyMCE();

        // Reinitialize TinyMCE when dark mode is toggled
        $(document).on('click', '#dark-mode-toggle', function() {
            setTimeout(function() {
                tinymce.remove('#content');
                initTinyMCE();
            }, 100);
        });

        // Custom file input labels
        $('.custom-file-input').on('change', function() {
            let fileName = $(this).val().split('\\').pop();
            $(this).next('.custom-file-label').addClass("selected").html(fileName);
        });

        // Add new category function
        function addNewCategory() {
            let newCategory = prompt('Enter new category name:');
            if (newCategory && newCategory.trim()) {
                newCategory = newCategory.trim().toLowerCase();
                let categorySelect = document.getElementById('category');
                let option = new Option(newCategory.charAt(0).toUpperCase() + newCategory.slice(1), newCategory);
                categorySelect.add(option);
                categorySelect.value = newCategory;
            }
        }
    </script>
</body>
</html>
