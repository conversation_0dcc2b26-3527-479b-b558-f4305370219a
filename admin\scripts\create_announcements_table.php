<?php
// Include configuration
require_once __DIR__ . '/../../config/config.php';
require_once __DIR__ . '/../../database/db_connect.php';

// Create database instance
$database = new Database();
$db = $database->getConnection();

echo "Creating admin_announcements table...\n";

// Check if the table already exists
$query = "SHOW TABLES LIKE 'admin_announcements'";
$stmt = $db->prepare($query);
$stmt->execute();
$table_exists = $stmt->rowCount() > 0;

if (!$table_exists) {
    // Create the admin_announcements table
    $query = "CREATE TABLE admin_announcements (
        id INT AUTO_INCREMENT PRIMARY KEY,
        admin_id INT NOT NULL,
        title VARCHAR(100) NOT NULL,
        message TEXT NOT NULL,
        recipient_type VARCHAR(20) NOT NULL,
        recipients_count INT NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (admin_id) REFERENCES users(id) ON DELETE CASCADE
    )";

    $stmt = $db->prepare($query);

    if ($stmt->execute()) {
        echo "Table created successfully.\n";
    } else {
        echo "Error creating table: " . print_r($stmt->errorInfo(), true) . "\n";
    }
} else {
    echo "Table already exists.\n";
}

echo "Done!\n";
?>
