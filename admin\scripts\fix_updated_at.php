<?php
// Include configuration
require_once __DIR__ . '/../../config/config.php';
require_once __DIR__ . '/../../database/db_connect.php';

// Create database instance
$database = new Database();
$db = $database->getConnection();

echo "Fixing updated_at column in users table...\n";

// Modify the updated_at column to remove ON UPDATE CURRENT_TIMESTAMP
$query = "ALTER TABLE users MODIFY updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP";
$stmt = $db->prepare($query);

if ($stmt->execute()) {
    echo "Successfully modified updated_at column in users table.\n";
} else {
    echo "Error modifying updated_at column: " . print_r($stmt->errorInfo(), true) . "\n";
}

echo "Done!\n";
?>
