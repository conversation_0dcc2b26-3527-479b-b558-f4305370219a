<?php
// Set page title
$page_title = 'Referral Leaderboard';

// Include header
require_once 'includes/header.php';

// Include leaderboard functions
require_once 'includes/leaderboard_functions.php';

// Get user ID
$user_id = $_SESSION['user_id'];

// Get leaderboard data
$top_referrers = get_top_referrers($db, 50);
$user_rank = get_user_referral_rank($db, $user_id);
$user_summary = get_user_referral_summary($db, $user_id);
$leaderboard_stats = get_leaderboard_stats($db);
?>

<div class="main-content">
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <div class="page-header">
                    <h1><i class="fas fa-trophy text-warning"></i> Referral Leaderboard</h1>
                    <p class="lead">See who's leading the way in building our P2P Donate community!</p>
                </div>
            </div>
        </div>

        <!-- User's Current Position -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card border-success">
                    <div class="card-header bg-success text-white">
                        <h5 class="mb-0"><i class="fas fa-user"></i> Your Position</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3 text-center">
                                <h3 class="text-success">
                                    <i class="<?php echo get_rank_icon($user_rank); ?>"></i>
                                    <?php echo format_rank($user_rank); ?>
                                </h3>
                                <small class="text-muted">Your Rank</small>
                            </div>
                            <div class="col-md-3 text-center">
                                <h3 class="text-primary"><?php echo $user_summary->completed_referrals ?? 0; ?></h3>
                                <small class="text-muted">Successful Referrals</small>
                            </div>
                            <div class="col-md-3 text-center">
                                <h3 class="text-info"><?php echo $user_summary->total_referrals ?? 0; ?></h3>
                                <small class="text-muted">Total Referrals</small>
                            </div>
                            <div class="col-md-3 text-center">
                                <h3 class="text-warning"><?php echo $user_summary->total_bonus_earned ?? 0; ?></h3>
                                <small class="text-muted">Bonus Tokens Earned</small>
                            </div>
                        </div>
                        <?php if ($user_rank == 0): ?>
                            <div class="alert alert-info mt-3">
                                <i class="fas fa-info-circle"></i> Start referring friends to appear on the leaderboard!
                                <a href="referrals.php" class="alert-link">Get your referral code here</a>.
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>

        <!-- Leaderboard Statistics -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card leaderboard-stats-card text-center border-primary">
                    <div class="card-body">
                        <i class="fas fa-users fa-2x text-primary mb-2"></i>
                        <h4 class="text-primary"><?php echo $leaderboard_stats->total_referrers; ?></h4>
                        <small class="text-muted">Active Referrers</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card leaderboard-stats-card text-center border-success">
                    <div class="card-body">
                        <i class="fas fa-handshake fa-2x text-success mb-2"></i>
                        <h4 class="text-success"><?php echo $leaderboard_stats->total_referrals; ?></h4>
                        <small class="text-muted">Total Referrals</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card leaderboard-stats-card text-center border-info">
                    <div class="card-body">
                        <i class="fas fa-check-circle fa-2x text-info mb-2"></i>
                        <h4 class="text-info"><?php echo $leaderboard_stats->completed_referrals; ?></h4>
                        <small class="text-muted">Successful Referrals</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card leaderboard-stats-card text-center border-warning">
                    <div class="card-body">
                        <i class="fas fa-coins fa-2x text-warning mb-2"></i>
                        <h4 class="text-warning"><?php echo $leaderboard_stats->total_bonus_distributed ?? 0; ?></h4>
                        <small class="text-muted">Bonus Tokens Distributed</small>
                    </div>
                </div>
            </div>
        </div>

        <!-- Top Referrers Table -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0"><i class="fas fa-trophy"></i> Top Referrers</h5>
                    </div>
                    <div class="card-body">
                        <?php if (empty($top_referrers)): ?>
                            <div class="alert alert-info text-center">
                                <i class="fas fa-info-circle fa-2x mb-3"></i>
                                <h5>No referrers yet!</h5>
                                <p>Be the first to start referring friends and claim the top spot on our leaderboard.</p>
                                <a href="referrals.php" class="btn btn-success">
                                    <i class="fas fa-user-plus"></i> Start Referring
                                </a>
                            </div>
                        <?php else: ?>
                            <div class="table-responsive leaderboard-table">
                                <table class="table table-hover">
                                    <thead class="thead-light">
                                        <tr>
                                            <th>Rank</th>
                                            <th>Name</th>
                                            <th>Successful Referrals</th>
                                            <th>Total Referrals</th>
                                            <th>Bonus Earned</th>
                                            <th>Member Since</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($top_referrers as $index => $referrer): ?>
                                            <tr class="table-row <?php echo ($referrer->id == $user_id) ? 'table-success user-row' : ''; ?>">
                                                <td>
                                                    <span class="rank-badge <?php echo get_rank_badge_class($index + 1); ?>">
                                                        <i class="rank-icon <?php echo get_rank_icon($index + 1); ?>"></i>
                                                        <?php echo format_rank($index + 1); ?>
                                                    </span>
                                                </td>
                                                <td>
                                                    <strong><?php echo htmlspecialchars($referrer->name); ?></strong>
                                                    <?php if ($referrer->id == $user_id): ?>
                                                        <span class="badge badge-info ml-2">You</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <span class="badge badge-success">
                                                        <?php echo $referrer->completed_referrals; ?>
                                                    </span>
                                                </td>
                                                <td><?php echo $referrer->total_referrals; ?></td>
                                                <td>
                                                    <span class="text-warning">
                                                        <i class="fas fa-coins"></i>
                                                        <?php echo $referrer->total_bonus_earned ?? 0; ?>
                                                    </span>
                                                </td>
                                                <td><?php echo format_date($referrer->joined_date, 'M Y'); ?></td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>

        <!-- Call to Action -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card bg-light">
                    <div class="card-body text-center">
                        <h5>Want to climb the leaderboard?</h5>
                        <p class="text-muted">Invite your friends to join P2P Donate and earn bonus tokens for each successful referral!</p>
                        <div class="btn-group" role="group">
                            <a href="referrals.php" class="btn btn-success">
                                <i class="fas fa-user-plus"></i> Get Referral Code
                            </a>
                            <a href="dashboard.php" class="btn btn-primary">
                                <i class="fas fa-tachometer-alt"></i> Back to Dashboard
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php require_once 'includes/footer.php'; ?>
