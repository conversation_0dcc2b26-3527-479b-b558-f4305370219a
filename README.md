# P2P-Donate Platform

P2P-Donate is a peer-to-peer donation platform that facilitates direct transfers between users. The platform matches donors with recipients and provides a system for verifying transfers.

## Features

- User registration and authentication
- Token-based system for pledges
- Matching system for donors and recipients
- Payment verification with proof uploads
- Chat system for communication between matched users
- Dispute resolution system
- Admin panel for platform management

## Technology Stack

- PHP 7.4+
- MySQL Database
- HTML, CSS, JavaScript
- Bootstrap 4.5
- jQuery
- Font Awesome

## Installation

1. Clone the repository to your local machine or web server
2. Create a MySQL database named `p2p_donate`
3. Import the database schema from `database/schema.sql`
4. Configure the database connection in `config/config.php`
5. Ensure the web server has write permissions to the `uploads` directory
6. Access the platform through your web browser

## Default Admin Credentials

- Email: <EMAIL>
- Password: admin123

## Directory Structure

- `admin/` - Admin panel files
- `assets/` - CSS, JavaScript, and image files
- `config/` - Configuration files
- `controllers/` - Controller files for handling form submissions
- `database/` - Database connection and schema files
- `includes/` - Reusable PHP functions and components
- `uploads/` - Directory for uploaded files
- `views/` - View templates

## User Flow

1. User registers an account
2. User purchases tokens
3. User makes a pledge
4. System matches the pledge with a recipient
5. User sends payment to the recipient
6. User uploads proof of payment
7. Recipient confirms receipt
8. Pledge is marked as completed

## Admin Features

- User management
- Pledge and match management
- Token management
- Transaction logs
- Dispute resolution
- System settings

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

For support, <NAME_EMAIL>.
