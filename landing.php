<?php
// Start session for user authentication
session_start();

// Include blog functions
require_once 'includes/blog_functions.php';

// Set page title and description for SEO
$page_title = "P2P Donate - A Positive Alternative to Sports Betting in Ghana";
$page_description = "P2P Donate offers Ghanaian youth a positive alternative to sports betting. Build wealth without gambling, addiction, or financial loss. Join our community today!";

// Statistics about sports betting in Ghana
$statistics = [
    [
        'number' => '11.18M',
        'description' => 'Ghanaians use the internet to access sports betting sites'
    ],
    [
        'number' => '57%',
        'description' => 'Of Ghana\'s population are youth, with many heavily involved in sports betting'
    ],
    [
        'number' => '$263M+',
        'description' => 'Estimated annual amount spent by Ghanaians on sports betting'
    ],
    [
        'number' => '36%',
        'description' => 'Of Ghana\'s population are youth facing unemployment challenges'
    ]
];

// Impact stories
$impact_stories = [
    [
        'title' => 'Financial Loss',
        'description' => '"I\'ve been betting for five years and have won only GH¢90 ($8.18), while spending thousands of cedis." - Anonymous bettor'
    ],
    [
        'title' => 'Criminal Consequences',
        'description' => 'Multiple young Ghanaians are serving jail time for stealing from employers to fund their betting addiction.'
    ],
    [
        'title' => 'Addiction',
        'description' => '"Just like someone who is smoking... it\'s become part of me. I\'m finding a way to stop because it can\'t help me." - Joel, 32'
    ]
];

// How it works steps
$steps = [
    [
        'icon' => 'icon-signup.svg',
        'title' => 'Sign Up',
        'description' => 'Create your free account with your mobile number and basic information'
    ],
    [
        'icon' => 'icon-pledge.svg',
        'title' => 'Make a Pledge',
        'description' => 'Contribute to the community pool with as little as GH¢5'
    ],
    [
        'icon' => 'icon-complete.svg',
        'title' => 'Complete Tasks',
        'description' => 'Earn tokens by completing simple tasks and participating in the community'
    ],
    [
        'icon' => 'icon-receive.svg',
        'title' => 'Receive Funds',
        'description' => 'Get paid directly to your mobile money account - guaranteed returns!'
    ]
];

// Features
$features = [
    [
        'icon' => 'icon-guaranteed.svg',
        'title' => 'Guaranteed Returns',
        'description' => 'Unlike betting where most people lose, everyone in our system receives their share'
    ],
    [
        'icon' => 'icon-community.svg',
        'title' => 'Community Support',
        'description' => 'Join a positive community that helps each other grow financially'
    ],
    [
        'icon' => 'icon-skills.svg',
        'title' => 'Skill Development',
        'description' => 'Learn financial literacy and valuable skills while receiving donations'
    ],
    [
        'icon' => 'icon-mobile.svg',
        'title' => 'Mobile Money Integration',
        'description' => 'Seamless transactions using the mobile money platforms you already trust'
    ]
];

// Testimonials
$testimonials = [
    [
        'image' => 'testimonial-1.jpg',
        'content' => '"I used to spend GH¢50 daily on betting and rarely won. With P2P Donate, I\'ve received consistent donations and started my own small business."',
        'name' => 'Anthony, 28',
        'location' => 'Accra'
    ],
    [
        'image' => 'testimonial-2.jpg',
        'content' => '"As a university student, I needed extra income. Instead of gambling like my friends, I joined P2P Donate and now pay my fees without stress."',
        'name' => 'Alice, 22',
        'location' => 'Kumasi'
    ],
    [
        'image' => 'testimonial-3.jpg',
        'content' => '"I was addicted to betting for 3 years. With P2P Donate I\'m breaking the cycle and now I will start saving towards my dream business."',
        'name' => 'Hassan, 25',
        'location' => 'Tamale'
    ]
];

// Ghana regions for the form
$regions = [
    'greater-accra' => 'Greater Accra',
    'ashanti' => 'Ashanti',
    'western' => 'Western',
    'eastern' => 'Eastern',
    'central' => 'Central',
    'northern' => 'Northern',
    'upper-east' => 'Upper East',
    'upper-west' => 'Upper West',
    'volta' => 'Volta',
    'bono' => 'Bono',
    'ahafo' => 'Ahafo',
    'bono-east' => 'Bono East',
    'oti' => 'Oti',
    'savannah' => 'Savannah',
    'north-east' => 'North East',
    'western-north' => 'Western North'
];

// Fetch blog posts for the blog section
$blog_posts = fetch_blog_posts(4); // Get 4 latest blog posts

// We're not processing forms directly anymore, just redirecting to login.php and register.php
$success_message = '';
$error_message = '';
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?></title>
    <meta name="description" content="<?php echo $page_description; ?>">
    <meta name="keywords" content="p2p donate, sports betting alternative, ghana, youth employment, financial freedom, betting addiction, mobile money, peer-to-peer">

    <!-- Open Graph / Social Media Meta Tags -->
    <meta property="og:title" content="<?php echo $page_title; ?>">
    <meta property="og:description" content="<?php echo $page_description; ?>">
    <meta property="og:image" content="assets/p2p-donate-social.jpg">
    <meta property="og:url" content="https://p2pdonate.com">
    <meta property="og:type" content="website">

    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="<?php echo $page_title; ?>">
    <meta name="twitter:description" content="<?php echo $page_description; ?>">
    <meta name="twitter:image" content="assets/p2p-donate-social.jpg">

    <!-- Favicon -->
    <link rel="icon" href="assets/favicon.ico">
    <link rel="apple-touch-icon" href="assets/apple-touch-icon.png">

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- CSS -->
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <!-- Header Section -->
    <header>
        <nav class="container">
            <div class="logo">
                <img src="assets/p2p-donate-logo.svg" alt="P2P Donate Logo" href="index.php">
            </div>
            <ul class="nav-links">
                <li><a href="#home">Home</a></li>
                <li><a href="#how-it-works">How It Works</a></li>
                <li><a href="#statistics">The Problem</a></li>
                <li><a href="#testimonials">Success Stories</a></li>
                <li><a href="#blog">Blog</a></li>
                <li><a href="#faq">FAQ</a></li>
                <!-- Mobile CTA buttons inside nav-links -->
                <div class="mobile-cta-container">
                    <?php if (isset($_SESSION['user_id'])): ?>
                    <a href="dashboard.php" class="btn btn-secondary">Dashboard</a>
                    <?php else: ?>
                    <a href="login.php" class="btn btn-secondary">Sign In</a>
                    <a href="register.php" class="btn btn-primary">Join Now</a>
                    <?php endif; ?>
                </div>
            </ul>
            <div class="cta-buttons">
                <?php if (isset($_SESSION['user_id'])): ?>
                <a href="dashboard.php" class="btn btn-secondary btn-sm">Dashboard</a>
                <?php else: ?>
                <a href="login.php" class="btn btn-secondary btn-sm">Sign In</a>
                <a href="register.php" class="btn btn-primary">Join Now</a>
                <?php endif; ?>
            </div>
            <!-- Mobile CTA buttons will be controlled by JavaScript -->
            <div class="mobile-menu-toggle">
                <span></span>
                <span></span>
                <span></span>
            </div>
            <!-- End mobile menu toggle -->
        </nav>
    </header>

    <!-- Hero Section -->
    <section id="home" class="hero">
        <div class="container">
            <div class="hero-content">
                <h1>Build Your Future Without Gambling It Away</h1>
                <p class="hero-subtitle">P2P Donate provides Ghanaian youth with a positive alternative to sports betting - a platform where you can receive donations, develop skills, and build a sustainable future.</p>
                <div class="hero-cta">
                    <a href="register.php" class="btn btn-primary btn-large">Start Donating Today</a>
                    <a href="#how-it-works" class="btn btn-secondary btn-large">Learn More</a>
                </div>
            </div>
            <div class="hero-image">
                <img src="assets/hero-image.png" alt="Young Ghanaians receiving donations through legitimate means" class="animate-float">
            </div>
        </div>
    </section>

    <!-- Wave Divider -->
    <div class="wave-divider">
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1440 70">
            <path d="M0,0V70H1440V0C1082.31,70,357.69,70,0,0Z"></path>
        </svg>
    </div>

    <!-- Statistics Section -->
    <section id="statistics" class="statistics">
        <div class="container">
            <h2 class="section-title">The Betting Crisis in Ghana</h2>
            <p class="section-subtitle">Sports betting has become a serious problem affecting Ghanaian youth, leading to addiction, financial loss, and even crime.</p>

            <div class="stats-grid">
                <?php foreach ($statistics as $stat): ?>
                <div class="stat-card">
                    <div class="stat-number"><?php echo $stat['number']; ?></div>
                    <p class="stat-description"><?php echo $stat['description']; ?></p>
                </div>
                <?php endforeach; ?>
            </div>

            <div class="impact-stories">
                <h3>Real Impact of Betting Addiction</h3>
                <div class="story-cards">
                    <?php foreach ($impact_stories as $story): ?>
                    <div class="story-card">
                        <h4><?php echo $story['title']; ?></h4>
                        <p><?php echo $story['description']; ?></p>
                    </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>
    </section>

    <!-- Wave Divider -->
    <div class="wave-divider white">
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1440 70">
            <path d="M0,0V70H1440V0C1082.31,70,357.69,70,0,0Z"></path>
        </svg>
    </div>

    <!-- How It Works Section -->
    <section id="how-it-works" class="how-it-works">
        <div class="container">
            <h2 class="section-title">How P2P Donate Works</h2>
            <p class="section-subtitle">A positive alternative where you don't lose money - you only gain</p>

            <div class="steps">
                <?php foreach ($steps as $index => $step): ?>
                <div class="step">
                    <div class="step-icon">
                        <img src="assets/<?php echo $step['icon']; ?>" alt="<?php echo $step['title']; ?> Icon">
                    </div>
                    <h3><?php echo $step['title']; ?></h3>
                    <p><?php echo $step['description']; ?></p>
                </div>
                <?php endforeach; ?>
            </div>

            <div class="features">
                <h3>Why Choose P2P Donate?</h3>
                <div class="feature-grid">
                    <?php foreach ($features as $feature): ?>
                    <div class="feature">
                        <img src="assets/<?php echo $feature['icon']; ?>" alt="<?php echo $feature['title']; ?>">
                        <h4><?php echo $feature['title']; ?></h4>
                        <p><?php echo $feature['description']; ?></p>
                    </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>
    </section>

    <!-- Wave Divider -->
    <div class="wave-divider green">
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1440 70">
            <path d="M0,0V70H1440V0C1082.31,70,357.69,70,0,0Z"></path>
        </svg>
    </div>

    <!-- Testimonials Section -->
    <section id="testimonials" class="testimonials">
        <div class="container">
            <h2 class="section-title">Success Stories</h2>
            <p class="section-subtitle">Hear from Ghanaians who chose P2P Donate over betting</p>

            <div class="testimonial-slider">
                <?php foreach ($testimonials as $testimonial): ?>
                <div class="testimonial">
                    <div class="testimonial-image">
                        <img src="assets/<?php echo $testimonial['image']; ?>" alt="<?php echo $testimonial['name']; ?>">
                    </div>
                    <div class="testimonial-content">
                        <p><?php echo $testimonial['content']; ?></p>
                        <h4><?php echo $testimonial['name']; ?></h4>
                        <p class="testimonial-location"><?php echo $testimonial['location']; ?></p>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
        </div>
    </section>

    <!-- Wave Divider -->
    <div class="wave-divider gold">
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1440 70">
            <path d="M0,0V70H1440V0C1082.31,70,357.69,70,0,0Z"></path>
        </svg>
    </div>

    <!-- Sign Up Section -->
    <section id="signup" class="signup">
        <div class="container">
            <div class="signup-content">
                <h2>Join P2P Donate Today</h2>
                <p>Start your journey to financial freedom without the risks of betting</p>

                <?php if (!empty($success_message)): ?>
                <div class="alert alert-success">
                    <?php echo $success_message; ?>
                </div>
                <?php elseif (!empty($error_message)): ?>
                <div class="alert alert-error">
                    <?php echo $error_message; ?>
                </div>
                <?php endif; ?>

                <div class="signup-form-container">
                    <p>Ready to start your journey with P2P Donate?</p>
                    <div class="form-group">
                        <a href="register.php" class="btn btn-primary btn-block">Create Your Account</a>
                    </div>
                    <p class="text-center">Already have an account? <a href="login.php">Sign In</a></p>
                </div>
            </div>
            <div class="signup-image">
                <img src="assets/signup-image.jpg" alt="Mobile phone showing the P2P Donate app" class="animate-float">
            </div>
        </div>
    </section>

    <!-- Wave Divider -->
    <div class="wave-divider">
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1440 70">
            <path d="M0,0V70H1440V0C1082.31,70,357.69,70,0,0Z"></path>
        </svg>
    </div>

    <!-- FAQ Section -->
    <section id="faq" class="faq">
        <div class="container">
            <h2 class="section-title">Questions?</h2>
            <p class="section-subtitle">We've Got Answers</p>

            <div class="faq-container">
                <div class="faq-item">
                    <div class="faq-question">
                        <h3>What is P2P Donate?</h3>
                        <span class="faq-toggle">+</span>
                    </div>
                    <div class="faq-answer">
                        <p>P2P Donate is a positive alternative to sports betting in Ghana. It's a platform that allows you to receive donations through a community-based system where members make pledges and receive returns based on a fair queue system.</p>
                    </div>
                </div>

                <div class="faq-item">
                    <div class="faq-question">
                        <h3>How does P2P Donate work?</h3>
                        <span class="faq-toggle">+</span>
                    </div>
                    <div class="faq-answer">
                        <p>P2P Donate works through a simple 4-step process:</p>
                        <ol>
                            <li>Sign up with your mobile money details</li>
                            <li>Make a pledge to get matched</li>
                            <li>Send the stipulated amount to receiver</li>
                            <li>Get matched twice to receive your pledge amount</li>
                        </ol>
                        <p>Our queue system ensures everyone gets their turn to receive funds in a fair, first-in-first-out order.</p>
                    </div>
                </div>

                <div class="faq-item">
                    <div class="faq-question">
                        <h3>How is P2P Donate different from sports betting?</h3>
                        <span class="faq-toggle">+</span>
                    </div>
                    <div class="faq-answer">
                        <p>Unlike sports betting where most people lose money, P2P Donate guarantees that everyone receives their share. There's no gambling involved - just a community of people helping each other grow financially. We focus on building skills and financial literacy rather than promoting risky behavior.</p>
                    </div>
                </div>

                <div class="faq-item">
                    <div class="faq-question">
                        <h3>How much can I receive?</h3>
                        <span class="faq-toggle">+</span>
                    </div>
                    <div class="faq-answer">
                        <p>Your donations depend on your level of participation and the size of your pledges. Unlike betting where winnings are unpredictable, P2P Donate provides consistent returns based on the community pool. Many of our members receive enough to cover daily expenses, pay school fees, or start small businesses.</p>
                    </div>
                </div>

                <div class="faq-item">
                    <div class="faq-question">
                        <h3>Is my money safe with P2P Donate?</h3>
                        <span class="faq-toggle">+</span>
                    </div>
                    <div class="faq-answer">
                        <p>Yes, your money is safe. We use secure mobile money platforms that you already trust for transactions. Our system is transparent, and all transactions are tracked. We don't hold your money - funds move directly between members through mobile money.</p>
                    </div>
                </div>

                <div class="faq-item">
                    <div class="faq-question">
                        <h3>How does the queue system work?</h3>
                        <span class="faq-toggle">+</span>
                    </div>
                    <div class="faq-answer">
                        <p>Our queue system follows these principles:</p>
                        <ul>
                            <li>First-in-first-out (FIFO) - those who join earlier get priority</li>
                            <li>Users at the top of the queue receive twice consecutively before moving to the next person</li>
                            <li>You must have made a pledge to be eligible to receive</li>
                            <li>You cannot make another pledge while in the queue</li>
                        </ul>
                        <p>This ensures a fair distribution of funds among all members.</p>
                    </div>
                </div>

                <div class="faq-item">
                    <div class="faq-question">
                        <h3>What mobile money platforms do you support?</h3>
                        <span class="faq-toggle">+</span>
                    </div>
                    <div class="faq-answer">
                        <p>We support all major mobile money platforms in Ghana, including MTN Mobile Money, Vodafone Cash, and AirtelTigo Money. You can use whichever platform you're most comfortable with.</p>
                    </div>
                </div>

                <div class="faq-item">
                    <div class="faq-question">
                        <h3>How do I get started?</h3>
                        <span class="faq-toggle">+</span>
                    </div>
                    <div class="faq-answer">
                        <p>Getting started is easy! Just click the "Join Now" button at the top of the page or the "Create Your Account" button in the sign-up section. Fill in your details, make your first pledge, and you're on your way to financial growth without gambling.</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Wave Divider -->
    <div class="wave-divider white">
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1440 70">
            <path d="M0,0V70H1440V0C1082.31,70,357.69,70,0,0Z"></path>
        </svg>
    </div>

    <!-- Wave Divider -->
    <div class="wave-divider">
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1440 70">
            <path d="M0,0V70H1440V0C1082.31,70,357.69,70,0,0Z"></path>
        </svg>
    </div>

    <!-- Blog Section -->
    <section id="blog" class="blog">
        <div class="container">
            <h2 class="section-title">Latest News & Updates</h2>
            <p class="section-subtitle">Stay informed with the latest insights on financial freedom, success stories, and tips for building wealth without gambling</p>

            <div class="blog-grid">
                <?php foreach ($blog_posts as $post): ?>
                <article class="blog-card">
                    <div class="blog-image">
                        <img src="<?php echo htmlspecialchars($post['image']); ?>" alt="<?php echo htmlspecialchars($post['title']); ?>" loading="lazy">
                    </div>
                    <div class="blog-content">
                        <h3 class="blog-title"><?php echo htmlspecialchars($post['title']); ?></h3>
                        <p class="blog-excerpt"><?php echo htmlspecialchars($post['excerpt']); ?></p>
                        <div class="blog-meta">
                            <span class="blog-date">
                                <i class="fas fa-calendar-alt"></i>
                                <?php echo htmlspecialchars($post['date']); ?>
                            </span>
                            <a href="<?php echo htmlspecialchars($post['url']); ?>" class="blog-read-more" target="_blank" rel="noopener noreferrer">
                                Read More <i class="fas fa-arrow-right"></i>
                            </a>
                        </div>
                    </div>
                </article>
                <?php endforeach; ?>
            </div>

            <div class="blog-cta">
                <a href="https://blog.p2pdonate.com" class="btn btn-primary" target="_blank" rel="noopener noreferrer">
                    <i class="fas fa-external-link-alt"></i>
                    View All Posts
                </a>
            </div>
        </div>
    </section>

    <!-- Wave Divider -->
    <div class="wave-divider white">
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1440 70">
            <path d="M0,0V70H1440V0C1082.31,70,357.69,70,0,0Z"></path>
        </svg>
    </div>

    <!-- Footer -->
    <footer>
        <div class="container">
            <div class="footer-grid">
                <div class="footer-about">
                    <img src="assets/p2p-donate-logo-white.svg" alt="P2P Donate Logo" class="footer-logo">
                    <p>P2P Donate is a positive alternative to sports betting, helping Ghanaian youth build financial freedom without gambling.</p>
                    <div class="social-links">
                        <a href="#" aria-label="Facebook"><i class="fab fa-facebook-f"></i></a>
                        <a href="#" aria-label="Twitter"><i class="fab fa-twitter"></i></a>
                        <a href="#" aria-label="Instagram"><i class="fab fa-instagram"></i></a>
                        <a href="https://t.me/P2P_Sysytem" aria-label="Telegram"><i class="fab fa-telegram"></i></a>
                    </div>
                </div>
                <div class="footer-links">
                    <h4>Quick Links</h4>
                    <ul>
                        <li><a href="#home">Home</a></li>
                        <li><a href="#how-it-works">How It Works</a></li>
                        <li><a href="#statistics">The Problem</a></li>
                        <li><a href="#testimonials">Success Stories</a></li>
                        <li><a href="#blog">Blog</a></li>
                        <li><a href="#faq">FAQ</a></li>
                    </ul>
                </div>
                <div class="footer-contact">
                    <h4>Contact Us</h4>
                    <p><i class="fas fa-map-marker-alt"></i> 123 Independence Ave, Accra, Ghana</p>
                    <p><i class="fas fa-phone"></i> +233 20 123 4567</p>
                    <p><i class="fas fa-envelope"></i> <EMAIL></p>
                </div>
                <div class="footer-newsletter">
                    <h4>Stay Updated</h4>
                    <p>Subscribe to our newsletter for tips on financial freedom</p>
                    <form class="newsletter-form">
                        <input type="email" placeholder="Your Email Address" required>
                        <button type="submit" class="btn btn-primary">Subscribe</button>
                    </form>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; <?php echo date('Y'); ?> P2P Donate. All rights reserved.</p>
                <div class="footer-bottom-links">
                    <a href="#">Privacy Policy</a>
                    <a href="#">Terms of Service</a>
                    <a href="#">Responsible Donating</a>
                </div>
            </div>
        </div>
    </footer>

    <!-- No modal needed as we're redirecting to login.php -->

    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css" integrity="sha512-z3gLpd7yknf1YoNbCzqRKc4qyor8gaKU1qmn+CShxbuBusANI9QpRohGBreCFkKxLhei6S9CQXFEbbKuqLg0DA==" crossorigin="anonymous" referrerpolicy="no-referrer" />
    <!-- Main JavaScript -->
    <script src="script.js"></script>
</body>
</html>
