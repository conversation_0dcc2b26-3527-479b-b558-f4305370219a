<?php
/**
 * <PERSON>ron job script to automatically update announcement statuses
 * This script should be run every minute via cron to ensure timely status updates
 * 
 * Cron entry example:
 * * * * * * /usr/bin/php /path/to/your/project/cron/update_announcement_statuses.php >> /var/log/announcement_cron.log 2>&1
 */

// Set script execution time limit
set_time_limit(300); // 5 minutes max

// Include required files
require_once dirname(__DIR__) . '/config/config.php';
require_once dirname(__DIR__) . '/includes/functions.php';
require_once dirname(__DIR__) . '/includes/scheduling_functions.php';
require_once dirname(__DIR__) . '/database/db_connect.php';

// Log function for cron output
function log_message($message) {
    $timestamp = date('Y-m-d H:i:s');
    echo "[{$timestamp}] {$message}\n";
}

try {
    log_message("Starting announcement status update cron job");
    
    // Initialize database connection
    $database = new Database();
    $db = $database->getConnection();
    
    if (!$db) {
        throw new Exception("Failed to connect to database");
    }
    
    log_message("Database connection established");
    
    // Update announcement statuses
    $results = update_announcement_statuses($db);
    
    log_message("Status update completed:");
    log_message("- Published: {$results['published']} announcements");
    log_message("- Expired: {$results['expired']} announcements");
    
    if (!empty($results['errors'])) {
        log_message("Errors encountered:");
        foreach ($results['errors'] as $error) {
            log_message("- ERROR: {$error}");
        }
    }
    
    // Process scheduled announcements that need to send notifications
    log_message("Processing scheduled announcements for notification sending");
    
    $current_utc = get_current_utc();
    
    // Get announcements that just became published and need notifications sent
    $query = "SELECT a.*, 
                     (SELECT COUNT(*) FROM notifications n WHERE n.title = a.title AND n.message = a.message) as notifications_sent
              FROM admin_announcements a
              WHERE a.status = 'published' 
              AND a.scheduled_publish_at IS NOT NULL 
              AND a.scheduled_publish_at <= :current_time
              AND a.scheduled_publish_at >= DATE_SUB(:current_time, INTERVAL 5 MINUTE)";
    
    $stmt = $db->prepare($query);
    $stmt->bindParam(':current_time', $current_utc);
    $stmt->execute();
    $newly_published = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    foreach ($newly_published as $announcement) {
        // Check if notifications have already been sent
        if ($announcement['notifications_sent'] > 0) {
            log_message("Notifications already sent for announcement: {$announcement['title']}");
            continue;
        }
        
        log_message("Sending notifications for newly published announcement: {$announcement['title']}");
        
        try {
            // Get recipients based on recipient type
            $recipients = [];
            
            switch ($announcement['recipient_type']) {
                case 'all':
                    $query = "SELECT id FROM users WHERE status = 'active'";
                    $stmt = $db->prepare($query);
                    $stmt->execute();
                    while ($user = $stmt->fetch(PDO::FETCH_OBJ)) {
                        $recipients[] = $user->id;
                    }
                    break;
                    
                case 'in_queue':
                    $query = "SELECT user_id as id FROM queue";
                    $stmt = $db->prepare($query);
                    $stmt->execute();
                    while ($user = $stmt->fetch(PDO::FETCH_OBJ)) {
                        $recipients[] = $user->id;
                    }
                    break;
                    
                case 'with_tokens':
                    $query = "SELECT user_id as id FROM tokens WHERE balance > 0";
                    $stmt = $db->prepare($query);
                    $stmt->execute();
                    while ($user = $stmt->fetch(PDO::FETCH_OBJ)) {
                        $recipients[] = $user->id;
                    }
                    break;
                    
                default:
                    log_message("Unknown recipient type: {$announcement['recipient_type']}");
                    continue 2;
            }
            
            // Send notifications to recipients
            $notification_count = 0;
            foreach ($recipients as $recipient_id) {
                if (create_notification($recipient_id, $announcement['title'], $announcement['message'], 'system', $db)) {
                    $notification_count++;
                }
            }
            
            // Update recipients count
            $query = "UPDATE admin_announcements SET recipients_count = :count WHERE id = :id";
            $stmt = $db->prepare($query);
            $stmt->bindParam(':count', $notification_count);
            $stmt->bindParam(':id', $announcement['id']);
            $stmt->execute();
            
            log_message("Sent {$notification_count} notifications for announcement: {$announcement['title']}");
            
        } catch (Exception $e) {
            log_message("Error sending notifications for announcement {$announcement['title']}: " . $e->getMessage());
        }
    }
    
    // Clean up expired notifications (optional - remove notifications for expired announcements)
    log_message("Cleaning up notifications for expired announcements");
    
    $query = "DELETE n FROM notifications n
              INNER JOIN admin_announcements a ON n.title = a.title AND n.message = a.message
              WHERE a.status = 'expired' 
              AND a.expires_at IS NOT NULL 
              AND a.expires_at <= :current_time
              AND n.type = 'system'";
    
    $stmt = $db->prepare($query);
    $stmt->bindParam(':current_time', $current_utc);
    $stmt->execute();
    $cleaned_notifications = $stmt->rowCount();
    
    if ($cleaned_notifications > 0) {
        log_message("Cleaned up {$cleaned_notifications} notifications for expired announcements");
    }
    
    // Log completion
    log_message("Announcement status update cron job completed successfully");
    
} catch (Exception $e) {
    log_message("FATAL ERROR: " . $e->getMessage());
    log_message("Stack trace: " . $e->getTraceAsString());
    exit(1);
}

// Log memory usage for monitoring
$memory_usage = memory_get_peak_usage(true);
$memory_mb = round($memory_usage / 1024 / 1024, 2);
log_message("Peak memory usage: {$memory_mb} MB");

exit(0);
?>
