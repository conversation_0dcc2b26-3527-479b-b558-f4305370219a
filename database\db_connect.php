<?php
// Define the root path to make includes work from any directory
if (!defined('ROOT_PATH')) {
    define('ROOT_PATH', dirname(__DIR__) . DIRECTORY_SEPARATOR);
}

// Include configuration file
require_once ROOT_PATH . 'config/config.php';

class Database {
    private $host = DB_HOST;
    private $user = DB_USER;
    private $pass = DB_PASS;
    private $dbname = DB_NAME;

    private $conn;
    private $error;

    public function __construct() {
        // Set DSN (Data Source Name)
        $dsn = 'mysql:host=' . $this->host . ';dbname=' . $this->dbname;

        // Set options
        $options = array(
            PDO::ATTR_PERSISTENT => true,
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION
        );

        // Create PDO instance
        try {
            // Set a shorter timeout for the connection attempt
            $options[PDO::ATTR_TIMEOUT] = 5; // 5 seconds timeout

            $this->conn = new PDO($dsn, $this->user, $this->pass, $options);
        } catch(PDOException $e) {
            $this->error = $e->getMessage();

            // Provide a more user-friendly error message
            if (strpos($e->getMessage(), 'refused') !== false) {
                echo '<div style="background-color: #f8d7da; color: #721c24; padding: 15px; margin: 10px; border-radius: 5px; border: 1px solid #f5c6cb;">';
                echo '<h3>Database Connection Error</h3>';
                echo '<p>Could not connect to the MySQL server. Please make sure that:</p>';
                echo '<ol>';
                echo '<li>The MySQL server is running</li>';
                echo '<li>The database credentials in config/config.php are correct</li>';
                echo '<li>The host and port settings are correct</li>';
                echo '</ol>';
                echo '<p>Technical details: ' . htmlspecialchars($this->error) . '</p>';
                echo '</div>';
            } else {
                echo '<div style="background-color: #f8d7da; color: #721c24; padding: 15px; margin: 10px; border-radius: 5px; border: 1px solid #f5c6cb;">';
                echo '<h3>Database Connection Error</h3>';
                echo '<p>An error occurred while connecting to the database:</p>';
                echo '<p>' . htmlspecialchars($this->error) . '</p>';
                echo '</div>';
            }

            $this->conn = null; // Set connection to null on error
        }
    }

    // Get connection
    public function getConnection() {
        return $this->conn;
    }

    // Execute query
    public function query($query) {
        $stmt = $this->conn->prepare($query);
        return $stmt;
    }

    // Execute statement
    public function execute($stmt, $params = []) {
        return $stmt->execute($params);
    }

    // Get result set as array of objects
    public function resultSet($stmt, $params = []) {
        $this->execute($stmt, $params);
        return $stmt->fetchAll(PDO::FETCH_OBJ);
    }

    // Get single record as object
    public function single($stmt, $params = []) {
        $this->execute($stmt, $params);
        return $stmt->fetch(PDO::FETCH_OBJ);
    }

    // Get row count
    public function rowCount($stmt) {
        return $stmt->rowCount();
    }

    // Get last inserted ID
    public function lastInsertId() {
        return $this->conn->lastInsertId();
    }
}
?>
