# Enable URL rewriting
RewriteEngine On

# Set the base directory
RewriteBase /

# Redirect to HTTPS (uncomment after SSL is set up)
# RewriteCond %{HTTPS} off
# RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]

# Prevent directory listing
Options -Indexes

# Protect sensitive files
<FilesMatch "^\.">
    Order allow,deny
    Deny from all
</FilesMatch>

<FilesMatch "^(config\.php|db_connect\.php|schema\.sql)$">
    Order allow,deny
    Deny from all
</FilesMatch>

# PHP settings
php_flag display_errors off
php_value upload_max_filesize 5M
php_value post_max_size 8M
php_value max_execution_time 60
php_value max_input_time 60

# Handle 404 errors
ErrorDocument 404 /index.php

# Prevent access to .htaccess
<Files .htaccess>
    Order allow,deny
    Deny from all
</Files>

# Prevent PHP execution in uploads directory
<Directory "uploads">
    <FilesMatch "\.php$">
        Order allow,deny
        Deny from all
    </FilesMatch>
</Directory>
