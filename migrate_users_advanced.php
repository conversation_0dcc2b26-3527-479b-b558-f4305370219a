<?php
/**
 * Advanced User Migration Script
 * 
 * This script migrates users from a source database to a destination database
 * with advanced features like dry-run mode, selective migration, and detailed logging.
 * 
 * Usage:
 * php migrate_users_advanced.php [options]
 * 
 * Options:
 *   --source-host=HOST       Source database host (default: localhost)
 *   --source-db=DBNAME       Source database name
 *   --source-user=USERNAME   Source database username (default: root)
 *   --source-pass=PASSWORD   Source database password (default: empty)
 *   --dest-host=HOST         Destination database host (default: localhost)
 *   --dest-db=DBNAME         Destination database name
 *   --dest-user=USERNAME     Destination database username (default: root)
 *   --dest-pass=PASSWORD     Destination database password (default: empty)
 *   --dry-run                Preview changes without making them
 *   --log-file=FILENAME      Log file path (default: migration_log.txt)
 *   --email=EMAIL            Migrate only user with specific email
 *   --id-range=START-END     Migrate users within ID range (e.g., 1-100)
 *   --help                   Display this help message
 */

// Set error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Start time for performance tracking
$start_time = microtime(true);

// Default configuration
$config = [
    'source_host' => 'localhost',
    'source_db' => 'p2p_donate_source',
    'source_user' => 'root',
    'source_pass' => '',
    'dest_host' => 'localhost',
    'dest_db' => 'p2p_donate',
    'dest_user' => 'root',
    'dest_pass' => '',
    'dry_run' => false,
    'log_file' => 'migration_log.txt',
    'email' => null,
    'id_range' => null,
];

// Parse command line arguments
$options = getopt('', [
    'source-host:',
    'source-db:',
    'source-user:',
    'source-pass:',
    'dest-host:',
    'dest-db:',
    'dest-user:',
    'dest-pass:',
    'dry-run',
    'log-file:',
    'email:',
    'id-range:',
    'help',
]);

// Display help if requested
if (isset($options['help'])) {
    echo file_get_contents(__FILE__, false, null, 0, strpos(__FILE__, "*/") + 2);
    exit(0);
}

// Update configuration with command line options
if (isset($options['source-host'])) $config['source_host'] = $options['source-host'];
if (isset($options['source-db'])) $config['source_db'] = $options['source-db'];
if (isset($options['source-user'])) $config['source_user'] = $options['source-user'];
if (isset($options['source-pass'])) $config['source_pass'] = $options['source-pass'];
if (isset($options['dest-host'])) $config['dest_host'] = $options['dest-host'];
if (isset($options['dest-db'])) $config['dest_db'] = $options['dest-db'];
if (isset($options['dest-user'])) $config['dest_user'] = $options['dest-user'];
if (isset($options['dest-pass'])) $config['dest_pass'] = $options['dest-pass'];
if (isset($options['dry-run'])) $config['dry_run'] = true;
if (isset($options['log-file'])) $config['log_file'] = $options['log-file'];
if (isset($options['email'])) $config['email'] = $options['email'];
if (isset($options['id-range'])) $config['id_range'] = $options['id-range'];

// Statistics
$total_users = 0;
$migrated_users = 0;
$skipped_users = 0;
$error_users = 0;

// Initialize log file
$log_file = fopen($config['log_file'], 'w');
if (!$log_file) {
    die("Error: Could not open log file {$config['log_file']} for writing.\n");
}

// Function to log messages
function log_message($message, $log_file) {
    $timestamp = date('Y-m-d H:i:s');
    $log_entry = "[$timestamp] $message\n";
    echo $log_entry;
    fwrite($log_file, $log_entry);
}

// Log configuration
log_message("Starting migration with the following configuration:", $log_file);
log_message("Source DB: {$config['source_db']} on {$config['source_host']}", $log_file);
log_message("Destination DB: {$config['dest_db']} on {$config['dest_host']}", $log_file);
if ($config['dry_run']) {
    log_message("DRY RUN MODE: No changes will be made", $log_file);
}
if ($config['email']) {
    log_message("Migrating only user with email: {$config['email']}", $log_file);
}
if ($config['id_range']) {
    log_message("Migrating users in ID range: {$config['id_range']}", $log_file);
}

// Connect to source database
try {
    $source_dsn = "mysql:host={$config['source_host']};dbname={$config['source_db']};charset=utf8mb4";
    $source_options = [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        PDO::ATTR_EMULATE_PREPARES => false,
    ];
    $source_db = new PDO($source_dsn, $config['source_user'], $config['source_pass'], $source_options);
    log_message("Connected to source database: {$config['source_db']}", $log_file);
} catch (PDOException $e) {
    log_message("Source database connection failed: " . $e->getMessage(), $log_file);
    fclose($log_file);
    die();
}

// Connect to destination database
try {
    $dest_dsn = "mysql:host={$config['dest_host']};dbname={$config['dest_db']};charset=utf8mb4";
    $dest_options = [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        PDO::ATTR_EMULATE_PREPARES => false,
    ];
    $dest_db = new PDO($dest_dsn, $config['dest_user'], $config['dest_pass'], $dest_options);
    log_message("Connected to destination database: {$config['dest_db']}", $log_file);
} catch (PDOException $e) {
    log_message("Destination database connection failed: " . $e->getMessage(), $log_file);
    fclose($log_file);
    die();
}

// Check if users table exists in source database
try {
    $stmt = $source_db->query("SHOW TABLES LIKE 'users'");
    if ($stmt->rowCount() == 0) {
        log_message("Error: 'users' table does not exist in source database.", $log_file);
        fclose($log_file);
        die();
    }
} catch (PDOException $e) {
    log_message("Error checking source tables: " . $e->getMessage(), $log_file);
    fclose($log_file);
    die();
}

// Check if users table exists in destination database
try {
    $stmt = $dest_db->query("SHOW TABLES LIKE 'users'");
    if ($stmt->rowCount() == 0) {
        log_message("Error: 'users' table does not exist in destination database.", $log_file);
        fclose($log_file);
        die();
    }
} catch (PDOException $e) {
    log_message("Error checking destination tables: " . $e->getMessage(), $log_file);
    fclose($log_file);
    die();
}

// Get column information from source database
try {
    $stmt = $source_db->query("DESCRIBE users");
    $source_columns = [];
    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        $source_columns[] = $row['Field'];
    }
    log_message("Source database 'users' table has " . count($source_columns) . " columns", $log_file);
} catch (PDOException $e) {
    log_message("Error getting source columns: " . $e->getMessage(), $log_file);
    fclose($log_file);
    die();
}

// Get column information from destination database
try {
    $stmt = $dest_db->query("DESCRIBE users");
    $dest_columns = [];
    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        $dest_columns[] = $row['Field'];
    }
    log_message("Destination database 'users' table has " . count($dest_columns) . " columns", $log_file);
} catch (PDOException $e) {
    log_message("Error getting destination columns: " . $e->getMessage(), $log_file);
    fclose($log_file);
    die();
}

// Find common columns
$common_columns = array_intersect($source_columns, $dest_columns);
log_message("Found " . count($common_columns) . " common columns to migrate", $log_file);
log_message("Common columns: " . implode(", ", $common_columns), $log_file);

// Check if essential columns exist
$essential_columns = ['name', 'email', 'password'];
foreach ($essential_columns as $column) {
    if (!in_array($column, $common_columns)) {
        log_message("Error: Essential column '$column' is not present in both databases.", $log_file);
        fclose($log_file);
        die();
    }
}

// Build query for fetching users
$query = "SELECT * FROM users";
$params = [];

// Apply filters if specified
if ($config['email']) {
    $query .= " WHERE email = ?";
    $params[] = $config['email'];
} elseif ($config['id_range']) {
    $range = explode('-', $config['id_range']);
    if (count($range) == 2 && is_numeric($range[0]) && is_numeric($range[1])) {
        $query .= " WHERE id BETWEEN ? AND ?";
        $params[] = (int)$range[0];
        $params[] = (int)$range[1];
    } else {
        log_message("Error: Invalid ID range format. Use START-END (e.g., 1-100)", $log_file);
        fclose($log_file);
        die();
    }
}

// Get users from source database
try {
    $stmt = $source_db->prepare($query);
    $stmt->execute($params);
    $users = $stmt->fetchAll();
    $total_users = count($users);
    log_message("Found $total_users users in source database matching criteria", $log_file);
} catch (PDOException $e) {
    log_message("Error fetching users from source database: " . $e->getMessage(), $log_file);
    fclose($log_file);
    die();
}

// Start transaction in destination database (unless dry run)
if (!$config['dry_run']) {
    $dest_db->beginTransaction();
    log_message("Started transaction in destination database", $log_file);
}

try {
    // Prepare column list for SQL
    $columns_list = implode(', ', $common_columns);
    $placeholders = implode(', ', array_fill(0, count($common_columns), '?'));
    
    // Prepare insert statement
    $insert_sql = "INSERT INTO users ($columns_list) VALUES ($placeholders)";
    if (!$config['dry_run']) {
        $insert_stmt = $dest_db->prepare($insert_sql);
    }
    
    // Prepare update statement for handling duplicates
    $update_parts = [];
    foreach ($common_columns as $column) {
        if ($column != 'id' && $column != 'email') { // Skip primary key and unique email
            $update_parts[] = "$column = VALUES($column)";
        }
    }
    $update_sql = "INSERT INTO users ($columns_list) VALUES ($placeholders) ON DUPLICATE KEY UPDATE " . implode(', ', $update_parts);
    if (!$config['dry_run']) {
        $update_stmt = $dest_db->prepare($update_sql);
    }
    
    // Process each user
    foreach ($users as $user) {
        try {
            // Extract values for common columns
            $values = [];
            foreach ($common_columns as $column) {
                $values[] = isset($user[$column]) ? $user[$column] : null;
            }
            
            if (!$config['dry_run']) {
                // Check if user already exists
                $check_stmt = $dest_db->prepare("SELECT id FROM users WHERE email = ?");
                $check_stmt->execute([$user['email']]);
                $exists = $check_stmt->fetch();
                
                if ($exists) {
                    // User exists, update if needed
                    log_message("User with email {$user['email']} already exists (ID: {$exists['id']}), updating...", $log_file);
                    $update_stmt->execute($values);
                    $skipped_users++;
                } else {
                    // New user, insert
                    $insert_stmt->execute($values);
                    $migrated_users++;
                    log_message("Migrated user: {$user['name']} ({$user['email']})", $log_file);
                }
            } else {
                // Dry run mode - just log what would happen
                $check_stmt = $dest_db->prepare("SELECT id FROM users WHERE email = ?");
                $check_stmt->execute([$user['email']]);
                $exists = $check_stmt->fetch();
                
                if ($exists) {
                    log_message("[DRY RUN] Would update user: {$user['name']} ({$user['email']})", $log_file);
                    $skipped_users++;
                } else {
                    log_message("[DRY RUN] Would insert user: {$user['name']} ({$user['email']})", $log_file);
                    $migrated_users++;
                }
            }
        } catch (PDOException $e) {
            log_message("Error processing user {$user['email']}: " . $e->getMessage(), $log_file);
            $error_users++;
        }
    }
    
    // Commit transaction (unless dry run)
    if (!$config['dry_run']) {
        $dest_db->commit();
        log_message("Transaction committed successfully", $log_file);
    } else {
        log_message("[DRY RUN] Would commit transaction", $log_file);
    }
} catch (PDOException $e) {
    // Rollback transaction on error (unless dry run)
    if (!$config['dry_run']) {
        $dest_db->rollBack();
        log_message("Transaction rolled back due to error: " . $e->getMessage(), $log_file);
    } else {
        log_message("[DRY RUN] Would roll back transaction due to error: " . $e->getMessage(), $log_file);
    }
}

// Calculate execution time
$end_time = microtime(true);
$execution_time = ($end_time - $start_time);

// Display summary
log_message("\n=== Migration Summary ===", $log_file);
log_message("Total users in source database matching criteria: $total_users", $log_file);
if ($config['dry_run']) {
    log_message("Would migrate new users: $migrated_users", $log_file);
    log_message("Would update existing users: $skipped_users", $log_file);
} else {
    log_message("Successfully migrated new users: $migrated_users", $log_file);
    log_message("Updated existing users: $skipped_users", $log_file);
}
log_message("Errors encountered: $error_users", $log_file);
log_message("Execution time: " . number_format($execution_time, 2) . " seconds", $log_file);
log_message("Log file: {$config['log_file']}", $log_file);
log_message("========================", $log_file);

// Close log file
fclose($log_file);
?>
