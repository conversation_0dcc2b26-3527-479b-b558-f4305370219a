# 🔧 Tutorials System Troubleshooting Guide

## Common Issues and Solutions

### 1. **"Table 'p2p_donate.tutorials' doesn't exist" Error**

**Problem:** The tutorials table hasn't been created in the database yet.

**Solution:**
1. Run the setup script: `http://localhost:8000/setup_tutorials_db.php`
2. Or manually run the SQL script: `admin/scripts/add_tutorials_table.sql`
3. Refresh the tutorials page

**Alternative Solution:**
```sql
-- Run this SQL in your database
CREATE TABLE tutorials (
    id INT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(200) NOT NULL,
    content TEXT NOT NULL,
    category VARCHAR(50) NOT NULL,
    image_file VARCHAR(255),
    video_file VARCHAR(255),
    video_url VARCHAR(500),
    display_order INT DEFAULT 0,
    status ENUM('draft', 'published') DEFAULT 'draft',
    created_by INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREI<PERSON><PERSON> KEY (created_by) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_status (status),
    INDEX idx_category (category),
    INDEX idx_display_order (display_order)
);
```

### 2. **Database Connection Issues**

**Problem:** Cannot connect to the database.

**Solution:**
1. Check `config/config.php` database settings
2. Ensure MySQL/MariaDB is running
3. Verify database credentials
4. Make sure the `p2p_donate` database exists

### 3. **File Upload Errors**

**Problem:** Cannot upload images or videos.

**Solution:**
1. Create the uploads directory:
   ```bash
   mkdir -p uploads/tutorials
   chmod 755 uploads/tutorials
   ```
2. Check PHP upload settings in `php.ini`:
   - `upload_max_filesize = 20M`
   - `post_max_size = 20M`
   - `max_execution_time = 300`

### 4. **Rich Text Editor Not Loading**

**Problem:** TinyMCE editor doesn't appear in admin form.

**Solution:**
1. Check internet connection (TinyMCE loads from CDN)
2. Verify no JavaScript errors in browser console
3. Ensure proper HTML structure in form

### 5. **Admin Access Issues**

**Problem:** Cannot access admin tutorials page.

**Solution:**
1. Ensure you're logged in as an admin user
2. Check user role in database: `SELECT role FROM users WHERE id = YOUR_ID`
3. Update user role if needed: `UPDATE users SET role = 'admin' WHERE id = YOUR_ID`

### 6. **Styling Issues**

**Problem:** Tutorials don't look right or missing styles.

**Solution:**
1. Clear browser cache
2. Check if `assets/css/style.css` includes tutorial styles
3. Verify CSS file is being loaded in the page
4. Check for CSS conflicts

### 7. **Modal Dialogs Not Working**

**Problem:** Tutorial modals don't open or display incorrectly.

**Solution:**
1. Ensure Bootstrap JavaScript is loaded
2. Check for JavaScript errors in browser console
3. Verify modal HTML structure
4. Clear browser cache

### 8. **Video Embedding Issues**

**Problem:** YouTube/Vimeo videos don't display.

**Solution:**
1. Use proper video URLs:
   - YouTube: `https://www.youtube.com/watch?v=VIDEO_ID`
   - Vimeo: `https://vimeo.com/VIDEO_ID`
2. Check video privacy settings (must be public)
3. Verify internet connection

### 9. **Category Filter Not Working**

**Problem:** Category filtering doesn't show results.

**Solution:**
1. Check if tutorials exist in the selected category
2. Verify category names match exactly (case-sensitive)
3. Clear any URL parameters and try again

### 10. **Performance Issues**

**Problem:** Tutorials page loads slowly.

**Solution:**
1. Optimize images (compress before upload)
2. Check database indexes are created
3. Limit number of tutorials displayed per page
4. Consider implementing pagination for large numbers of tutorials

## Quick Diagnostic Steps

### Step 1: Check Database
```sql
-- Check if table exists
SHOW TABLES LIKE 'tutorials';

-- Check table structure
DESCRIBE tutorials;

-- Check for data
SELECT COUNT(*) FROM tutorials;
```

### Step 2: Check File Permissions
```bash
ls -la uploads/tutorials/
```

### Step 3: Check PHP Errors
- Enable error reporting in PHP
- Check server error logs
- Use browser developer tools

### Step 4: Test Basic Functionality
1. Visit `setup_tutorials_db.php` to verify database setup
2. Try accessing `tutorials.php` as a regular user
3. Try accessing `admin/tutorials.php` as an admin
4. Test creating a simple tutorial

## Getting Help

If you're still experiencing issues:

1. **Check the browser console** for JavaScript errors
2. **Check server logs** for PHP errors
3. **Verify database connection** using a simple test script
4. **Test with minimal data** (create one simple tutorial)
5. **Clear all caches** (browser, server, database query cache)

## System Requirements

- **PHP**: 7.4 or higher
- **MySQL/MariaDB**: 5.7 or higher
- **Web Server**: Apache/Nginx with mod_rewrite
- **Browser**: Modern browser with JavaScript enabled
- **Internet Connection**: Required for TinyMCE editor and video embedding

## Success Indicators

When everything is working correctly, you should see:

✅ **User Side (`tutorials.php`):**
- Tutorials displayed in card layout
- Category filter working
- Modal dialogs opening properly
- Responsive design on mobile

✅ **Admin Side (`admin/tutorials.php`):**
- List of tutorials with management options
- Create/edit forms working
- File uploads functioning
- Rich text editor loading

✅ **Database:**
- Tutorials table exists with proper structure
- Sample data loaded
- Indexes created for performance

## Contact Support

If you continue to experience issues after following this guide, please provide:
- Error messages (exact text)
- Browser and version
- PHP version
- Database version
- Steps to reproduce the issue

The tutorials system is designed to be robust and user-friendly. Most issues can be resolved by following the steps in this guide.
