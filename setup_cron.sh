#!/bin/bash

# Setup script for announcement scheduling cron job
# This script helps configure the cron job for automatic announcement status updates

echo "=== P2P Donate Announcement Scheduling Cron Setup ==="
echo ""

# Get the current directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
CRON_SCRIPT="$SCRIPT_DIR/cron/update_announcement_statuses.php"
LOG_FILE="/var/log/announcement_cron.log"

echo "Project Directory: $SCRIPT_DIR"
echo "Cron Script: $CRON_SCRIPT"
echo "Log File: $LOG_FILE"
echo ""

# Check if PHP is available
if ! command -v php &> /dev/null; then
    echo "❌ PHP is not installed or not in PATH"
    echo "Please install PHP and ensure it's accessible via command line"
    exit 1
fi

echo "✅ PHP is available: $(php --version | head -n 1)"

# Check if the cron script exists
if [ ! -f "$CRON_SCRIPT" ]; then
    echo "❌ Cron script not found: $CRON_SCRIPT"
    echo "Please ensure the cron script is in the correct location"
    exit 1
fi

echo "✅ Cron script found: $CRON_SCRIPT"

# Test the cron script
echo ""
echo "Testing cron script execution..."
php "$CRON_SCRIPT"
SCRIPT_EXIT_CODE=$?

if [ $SCRIPT_EXIT_CODE -eq 0 ]; then
    echo "✅ Cron script executed successfully"
else
    echo "❌ Cron script failed with exit code: $SCRIPT_EXIT_CODE"
    echo "Please check the script for errors before setting up cron"
    exit 1
fi

# Create log directory if it doesn't exist
LOG_DIR=$(dirname "$LOG_FILE")
if [ ! -d "$LOG_DIR" ]; then
    echo ""
    echo "Creating log directory: $LOG_DIR"
    sudo mkdir -p "$LOG_DIR"
    sudo chmod 755 "$LOG_DIR"
fi

# Create log file if it doesn't exist
if [ ! -f "$LOG_FILE" ]; then
    echo "Creating log file: $LOG_FILE"
    sudo touch "$LOG_FILE"
    sudo chmod 644 "$LOG_FILE"
    
    # Set ownership to web server user if possible
    if command -v www-data &> /dev/null; then
        sudo chown www-data:www-data "$LOG_FILE"
    elif command -v apache &> /dev/null; then
        sudo chown apache:apache "$LOG_FILE"
    elif command -v nginx &> /dev/null; then
        sudo chown nginx:nginx "$LOG_FILE"
    fi
fi

echo "✅ Log file ready: $LOG_FILE"

# Generate cron entry
CRON_ENTRY="* * * * * /usr/bin/php $CRON_SCRIPT >> $LOG_FILE 2>&1"

echo ""
echo "=== Cron Job Configuration ==="
echo ""
echo "Add the following line to your crontab:"
echo ""
echo "$CRON_ENTRY"
echo ""
echo "To edit your crontab, run:"
echo "  crontab -e"
echo ""
echo "Or to add it automatically (be careful with existing crontab):"
echo "  (crontab -l 2>/dev/null; echo \"$CRON_ENTRY\") | crontab -"
echo ""

# Check if cron service is running
if systemctl is-active --quiet cron; then
    echo "✅ Cron service is running"
elif systemctl is-active --quiet crond; then
    echo "✅ Crond service is running"
else
    echo "⚠️  Cron service may not be running. Please check:"
    echo "   sudo systemctl status cron"
    echo "   sudo systemctl status crond"
fi

echo ""
echo "=== Setup Complete ==="
echo ""
echo "Next steps:"
echo "1. Add the cron entry to your crontab"
echo "2. Monitor the log file for proper execution:"
echo "   tail -f $LOG_FILE"
echo "3. Test the scheduling system using:"
echo "   http://yoursite.com/test_scheduling_system.php"
echo ""
echo "The cron job will run every minute to:"
echo "- Update announcement statuses (scheduled → published → expired)"
echo "- Send notifications for newly published announcements"
echo "- Clean up expired announcement notifications"
echo ""

# Offer to add cron job automatically
read -p "Would you like to add the cron job automatically? (y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    # Backup existing crontab
    crontab -l > /tmp/crontab_backup_$(date +%Y%m%d_%H%M%S) 2>/dev/null
    
    # Add new cron job if it doesn't already exist
    if ! crontab -l 2>/dev/null | grep -q "update_announcement_statuses.php"; then
        (crontab -l 2>/dev/null; echo "$CRON_ENTRY") | crontab -
        echo "✅ Cron job added successfully!"
        echo "Current crontab:"
        crontab -l
    else
        echo "⚠️  Cron job already exists in crontab"
        echo "Current crontab:"
        crontab -l | grep "update_announcement_statuses.php"
    fi
else
    echo "Cron job not added automatically. Please add it manually when ready."
fi

echo ""
echo "Setup script completed successfully!"
echo "Check the documentation in ANNOUNCEMENT_SCHEDULING_GUIDE.md for more details."
