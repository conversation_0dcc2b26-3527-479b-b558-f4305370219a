<?php
// Set page title
$page_title = 'Tutorials';

// Include configuration
require_once 'config/config.php';
require_once 'includes/functions.php';
require_once 'includes/tutorial_functions.php';
require_once 'database/db_connect.php';

// Start session
start_session();

// Check if user is logged in
if (!is_logged_in()) {
    redirect('login.php');
}

// Create database instance
$database = new Database();
$db = $database->getConnection();

// Get user ID
$user_id = $_SESSION['user_id'];

// Get selected category filter
$selected_category = isset($_GET['category']) ? sanitize($_GET['category']) : '';

// Initialize variables
$tutorials = [];
$categories = [];

try {
    // Get all published tutorials
    $tutorials = get_published_tutorials($db, $selected_category);

    // Get all categories for filter
    $categories = get_tutorial_categories($db);
} catch (Exception $e) {
    // Handle case where tutorials table doesn't exist yet
    $error_message = "Tutorials system is not set up yet. Please run the setup script first.";
}

// Include header
include 'includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">
                    <i class="fas fa-graduation-cap text-success"></i>
                    Tutorials
                </h1>
            </div>

            <?php if (isset($error_message)): ?>
                <div class="alert alert-warning" role="alert">
                    <h4 class="alert-heading">Setup Required</h4>
                    <p><?php echo $error_message; ?></p>
                    <hr>
                    <p class="mb-0">
                        <a href="setup_tutorials_db.php" class="btn btn-primary">
                            <i class="fas fa-cog"></i> Run Setup Script
                        </a>
                    </p>
                </div>
            <?php else: ?>

            <!-- Category Filter -->
            <?php if (!empty($categories)): ?>
            <div class="row mb-4">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-body">
                            <h6 class="card-title">Filter by Category</h6>
                            <form method="GET" action="tutorials.php">
                                <div class="input-group">
                                    <select name="category" class="form-control">
                                        <option value="">All Categories</option>
                                        <?php foreach ($categories as $category): ?>
                                            <option value="<?php echo htmlspecialchars($category); ?>"
                                                    <?php echo $selected_category === $category ? 'selected' : ''; ?>>
                                                <?php echo htmlspecialchars(ucfirst($category)); ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                    <div class="input-group-append">
                                        <button class="btn btn-success" type="submit">
                                            <i class="fas fa-filter"></i> Filter
                                        </button>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
            <?php endif; ?>

            <!-- Tutorials Grid -->
            <?php if (empty($tutorials)): ?>
                <div class="row">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-body text-center py-5">
                                <i class="fas fa-graduation-cap fa-3x text-muted mb-3"></i>
                                <h4 class="text-muted">No Tutorials Available</h4>
                                <p class="text-muted">
                                    <?php if ($selected_category): ?>
                                        No tutorials found in the "<?php echo htmlspecialchars(ucfirst($selected_category)); ?>" category.
                                        <br><a href="tutorials.php" class="btn btn-success mt-2">View All Tutorials</a>
                                    <?php else: ?>
                                        Check back later for helpful tutorials about using the P2P Donate platform.
                                    <?php endif; ?>
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            <?php else: ?>
                <div class="row">
                    <?php foreach ($tutorials as $tutorial): ?>
                        <div class="col-lg-6 col-xl-4 mb-4">
                            <div class="card h-100 tutorial-card">
                                <?php if ($tutorial->image_file): ?>
                                    <img src="uploads/tutorials/<?php echo htmlspecialchars($tutorial->image_file); ?>"
                                         class="card-img-top"
                                         alt="<?php echo htmlspecialchars($tutorial->title); ?>"
                                         style="height: 200px; object-fit: cover;">
                                <?php endif; ?>

                                <div class="card-body d-flex flex-column">
                                    <div class="mb-2">
                                        <span class="badge badge-success">
                                            <?php echo htmlspecialchars(ucfirst($tutorial->category)); ?>
                                        </span>
                                    </div>

                                    <h5 class="card-title">
                                        <?php echo htmlspecialchars($tutorial->title); ?>
                                    </h5>

                                    <div class="card-text flex-grow-1">
                                        <?php
                                        // Show first 150 characters of content
                                        $preview = strip_tags($tutorial->content);
                                        if (strlen($preview) > 150) {
                                            $preview = substr($preview, 0, 150) . '...';
                                        }
                                        echo htmlspecialchars($preview);
                                        ?>
                                    </div>

                                    <div class="mt-3">
                                        <button class="btn btn-success btn-sm"
                                                data-toggle="modal"
                                                data-target="#tutorialModal<?php echo $tutorial->id; ?>">
                                            <i class="fas fa-eye"></i> View Tutorial
                                        </button>

                                        <?php if ($tutorial->video_url): ?>
                                            <span class="badge badge-info ml-2">
                                                <i class="fas fa-play"></i> Video
                                            </span>
                                        <?php endif; ?>
                                    </div>

                                    <small class="text-muted mt-2">
                                        <i class="fas fa-clock"></i>
                                        Created <?php echo date('M j, Y', strtotime($tutorial->created_at)); ?>
                                    </small>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Tutorial Modals -->
<?php foreach ($tutorials as $tutorial): ?>
<div class="modal fade tutorial-modal" id="tutorialModal<?php echo $tutorial->id; ?>" tabindex="-1" role="dialog" aria-labelledby="tutorialModalLabel<?php echo $tutorial->id; ?>" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header bg-success text-white">
                <h5 class="modal-title" id="tutorialModalLabel<?php echo $tutorial->id; ?>">
                    <i class="fas fa-graduation-cap"></i>
                    <?php echo htmlspecialchars($tutorial->title); ?>
                </h5>
                <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <span class="badge badge-success">
                        <?php echo htmlspecialchars(ucfirst($tutorial->category)); ?>
                    </span>
                    <small class="text-muted ml-2">
                        <i class="fas fa-clock"></i>
                        Created <?php echo date('M j, Y', strtotime($tutorial->created_at)); ?>
                    </small>
                </div>

                <?php if ($tutorial->image_file): ?>
                    <div class="mb-3">
                        <img src="uploads/tutorials/<?php echo htmlspecialchars($tutorial->image_file); ?>"
                             class="img-fluid rounded"
                             alt="<?php echo htmlspecialchars($tutorial->title); ?>">
                    </div>
                <?php endif; ?>

                <?php if ($tutorial->video_url): ?>
                    <div class="mb-3">
                        <div class="embed-responsive embed-responsive-16by9">
                            <?php
                            $video_url = $tutorial->video_url;
                            // Convert YouTube URLs to embed format
                            if (strpos($video_url, 'youtube.com/watch') !== false) {
                                $video_id = parse_url($video_url, PHP_URL_QUERY);
                                parse_str($video_id, $params);
                                $video_url = 'https://www.youtube.com/embed/' . $params['v'];
                            } elseif (strpos($video_url, 'youtu.be/') !== false) {
                                $video_id = substr($video_url, strrpos($video_url, '/') + 1);
                                $video_url = 'https://www.youtube.com/embed/' . $video_id;
                            } elseif (strpos($video_url, 'vimeo.com/') !== false) {
                                $video_id = substr($video_url, strrpos($video_url, '/') + 1);
                                $video_url = 'https://player.vimeo.com/video/' . $video_id;
                            }
                            ?>
                            <iframe class="embed-responsive-item"
                                    src="<?php echo htmlspecialchars($video_url); ?>"
                                    allowfullscreen></iframe>
                        </div>
                    </div>
                <?php endif; ?>

                <?php if ($tutorial->video_file): ?>
                    <div class="mb-3">
                        <video class="w-100" controls>
                            <source src="uploads/tutorials/<?php echo htmlspecialchars($tutorial->video_file); ?>" type="video/mp4">
                            Your browser does not support the video tag.
                        </video>
                    </div>
                <?php endif; ?>

                <div class="tutorial-content">
                    <?php echo format_tutorial_content($tutorial->content); ?>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>
<?php endforeach; ?>

<?php endif; // End of error check ?>

<?php include 'includes/footer.php'; ?>
