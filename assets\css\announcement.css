/* Announcement Styles */
.announcement-alert {
    margin-bottom: 2rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    border-left: 4px solid var(--info-color);
}

.announcement-alert .close {
    opacity: 0.7;
}

.announcement-alert .close:hover {
    opacity: 1;
}

/* Announcement Media Styles */
.announcement-media {
    margin-top: 1rem;
    margin-bottom: 1rem;
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.announcement-image {
    max-width: 100%;
    height: auto;
    border-radius: 8px;
    cursor: pointer;
    transition: transform 0.3s ease;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.announcement-image:hover {
    transform: scale(1.02);
}

.announcement-video {
    max-width: 100%;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Image Modal Styles */
.image-modal {
    display: none;
    position: fixed;
    z-index: 2000;
    padding-top: 50px;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: auto;
    background-color: rgba(0, 0, 0, 0.9);
}

.image-modal-content {
    margin: auto;
    display: block;
    max-width: 90%;
    max-height: 90vh;
}

.image-modal-close {
    position: absolute;
    top: 15px;
    right: 35px;
    color: #f1f1f1;
    font-size: 40px;
    font-weight: bold;
    transition: 0.3s;
    cursor: pointer;
}

.image-modal-close:hover,
.image-modal-close:focus {
    color: #bbb;
    text-decoration: none;
    cursor: pointer;
}

/* Responsive Styles */
@media (max-width: 767.98px) {
    .announcement-alert {
        margin-bottom: 1.5rem;
    }
    
    .announcement-media {
        margin-top: 0.75rem;
        margin-bottom: 0.75rem;
    }
    
    .image-modal-content {
        max-width: 95%;
    }
    
    .image-modal-close {
        top: 10px;
        right: 25px;
        font-size: 30px;
    }
}

/* Dark Mode Support */
body.dark-mode .announcement-alert {
    background-color: var(--dark-card-bg);
    border-color: var(--dark-primary);
}

body.dark-mode .announcement-alert .close {
    color: var(--dark-text);
}

body.dark-mode .announcement-image,
body.dark-mode .announcement-video {
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}
