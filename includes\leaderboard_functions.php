<?php
/**
 * Leaderboard Functions for P2P Donate Platform
 * Handles referral leaderboard data and statistics
 */

/**
 * Get top referrers for the leaderboard
 * @param PDO $db Database connection
 * @param int $limit Number of top referrers to return
 * @return array Array of top referrers with their statistics
 */
function get_top_referrers($db, $limit = 50) {
    $query = "SELECT u.id, u.name, u.email, u.bonus_tokens, u.created_at as joined_date,
              (SELECT COUNT(*) FROM referrals WHERE referrer_id = u.id) as total_referrals,
              (SELECT COUNT(*) FROM referrals WHERE referrer_id = u.id AND status = 'completed') as completed_referrals,
              (SELECT COUNT(*) FROM referrals WHERE referrer_id = u.id AND status = 'pending') as pending_referrals,
              (SELECT SUM(bonus_amount) FROM referrals WHERE referrer_id = u.id AND status = 'completed') as total_bonus_earned
              FROM users u
              WHERE (SELECT COUNT(*) FROM referrals WHERE referrer_id = u.id) > 0
              ORDER BY completed_referrals DESC, total_referrals DESC, u.created_at ASC
              LIMIT :limit";

    $stmt = $db->prepare($query);
    $stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
    $stmt->execute();
    return $stmt->fetchAll(PDO::FETCH_OBJ);
}

/**
 * Get user's referral rank
 * @param PDO $db Database connection
 * @param int $user_id User ID to get rank for
 * @return int User's rank (0 if not ranked)
 */
function get_user_referral_rank($db, $user_id) {
    $query = "SELECT rank_position FROM (
                SELECT u.id,
                       ROW_NUMBER() OVER (ORDER BY
                           (SELECT COUNT(*) FROM referrals WHERE referrer_id = u.id AND status = 'completed') DESC,
                           (SELECT COUNT(*) FROM referrals WHERE referrer_id = u.id) DESC,
                           u.created_at ASC
                       ) as rank_position
                FROM users u
                WHERE (SELECT COUNT(*) FROM referrals WHERE referrer_id = u.id) > 0
              ) ranked_users
              WHERE id = :user_id";

    $stmt = $db->prepare($query);
    $stmt->bindParam(':user_id', $user_id, PDO::PARAM_INT);
    $stmt->execute();
    $result = $stmt->fetch(PDO::FETCH_OBJ);

    return $result ? $result->rank_position : 0;
}

/**
 * Get leaderboard statistics
 * @param PDO $db Database connection
 * @return object Statistics object
 */
function get_leaderboard_stats($db) {
    $query = "SELECT
              (SELECT COUNT(*) FROM users WHERE (SELECT COUNT(*) FROM referrals WHERE referrer_id = users.id) > 0) as total_referrers,
              (SELECT COUNT(*) FROM referrals) as total_referrals,
              (SELECT COUNT(*) FROM referrals WHERE status = 'completed') as completed_referrals,
              (SELECT SUM(bonus_amount) FROM referrals WHERE status = 'completed') as total_bonus_distributed";

    $stmt = $db->prepare($query);
    $stmt->execute();
    return $stmt->fetch(PDO::FETCH_OBJ);
}

/**
 * Get user's referral summary for leaderboard display
 * @param PDO $db Database connection
 * @param int $user_id User ID
 * @return object User's referral summary
 */
function get_user_referral_summary($db, $user_id) {
    $query = "SELECT
              (SELECT COUNT(*) FROM referrals WHERE referrer_id = :user_id) as total_referrals,
              (SELECT COUNT(*) FROM referrals WHERE referrer_id = :user_id AND status = 'completed') as completed_referrals,
              (SELECT COUNT(*) FROM referrals WHERE referrer_id = :user_id AND status = 'pending') as pending_referrals,
              (SELECT SUM(bonus_amount) FROM referrals WHERE referrer_id = :user_id AND status = 'completed') as total_bonus_earned,
              (SELECT bonus_tokens FROM users WHERE id = :user_id) as current_bonus_tokens";

    $stmt = $db->prepare($query);
    $stmt->bindParam(':user_id', $user_id, PDO::PARAM_INT);
    $stmt->execute();
    return $stmt->fetch(PDO::FETCH_OBJ);
}

/**
 * Format rank with appropriate suffix (1st, 2nd, 3rd, etc.)
 * @param int $rank The rank number
 * @return string Formatted rank with suffix
 */
function format_rank($rank) {
    if ($rank == 0) return 'Unranked';

    $suffix = 'th';
    if ($rank % 100 < 11 || $rank % 100 > 13) {
        switch ($rank % 10) {
            case 1: $suffix = 'st'; break;
            case 2: $suffix = 'nd'; break;
            case 3: $suffix = 'rd'; break;
        }
    }

    return $rank . $suffix;
}

/**
 * Get rank badge class based on position
 * @param int $rank The rank position
 * @return string CSS class for rank badge
 */
function get_rank_badge_class($rank) {
    switch ($rank) {
        case 1:
            return 'gold'; // Gold
        case 2:
            return 'silver'; // Silver
        case 3:
            return 'bronze'; // Bronze
        default:
            return 'default'; // Green for others
    }
}

/**
 * Get rank icon based on position
 * @param int $rank The rank position
 * @return string Font Awesome icon class with color class
 */
function get_rank_icon($rank) {
    switch ($rank) {
        case 1:
            return 'fas fa-crown gold'; // Crown for 1st place
        case 2:
            return 'fas fa-medal silver'; // Medal for 2nd place
        case 3:
            return 'fas fa-award bronze'; // Award for 3rd place
        default:
            return 'fas fa-star default'; // Star for others
    }
}
?>
