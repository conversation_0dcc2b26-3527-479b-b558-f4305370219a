<?php
// Include configuration
require_once '../config/config.php';
require_once '../includes/functions.php';
require_once '../database/db_connect.php';

// Start session
start_session();

// Check if user is logged in and is admin
if (!is_logged_in() || !is_admin()) {
    redirect('../login.php');
}

// Create database instance
$database = new Database();
$db = $database->getConnection();

// Check if ID is provided
if (!isset($_GET['id']) || empty($_GET['id'])) {
    redirect('announcements.php');
}

$announcement_id = intval($_GET['id']);
$delete_notifications = isset($_GET['delete_notifications']) && $_GET['delete_notifications'] == 1;

// Get announcement details
$query = "SELECT * FROM admin_announcements WHERE id = :id";
$stmt = $db->prepare($query);
$stmt->bindParam(':id', $announcement_id);
$stmt->execute();
$announcement = $stmt->fetch(PDO::FETCH_OBJ);

// Check if announcement exists
if (!$announcement) {
    redirect('announcements.php');
}

// Start transaction
$db->beginTransaction();

try {
    // Delete related notifications if requested
    $notifications_deleted = 0;
    if ($delete_notifications) {
        $query = "DELETE FROM notifications
                  WHERE title = :title AND message = :message AND type = 'system'";
        $stmt = $db->prepare($query);
        $stmt->bindParam(':title', $announcement->title);
        $stmt->bindParam(':message', $announcement->message);
        $stmt->execute();
        $notifications_deleted = $stmt->rowCount();
    }

    // Delete announcement
    $query = "DELETE FROM admin_announcements WHERE id = :id";
    $stmt = $db->prepare($query);
    $stmt->bindParam(':id', $announcement_id);
    $stmt->execute();

    // Delete associated files
    if (!empty($announcement->image_file)) {
        $image_path = '../uploads/announcements/' . $announcement->image_file;
        if (file_exists($image_path)) {
            unlink($image_path);
        }
    }

    if (!empty($announcement->video_file)) {
        $video_path = '../uploads/announcements/' . $announcement->video_file;
        if (file_exists($video_path)) {
            unlink($video_path);
        }
    }

    // Commit transaction
    $db->commit();

    // Set success message
    $message = "Announcement deleted successfully.";
    if ($notifications_deleted > 0) {
        $message .= " $notifications_deleted related notifications were also deleted.";
    }

    flash_message('success_message', $message);

} catch (Exception $e) {
    // Rollback transaction on error
    $db->rollBack();
    flash_message('error_message', 'Error deleting announcement: ' . $e->getMessage());
}

// Redirect back to announcements page in the admin directory
header('Location: announcements.php');
exit;
?>
