/**
 * Mobile Money Payment Method JavaScript
 * Handles payment method selection and UI interactions
 * P2P Donate Platform - Ghana Market Focus
 */

document.addEventListener('DOMContentLoaded', function() {
    initializePaymentMethods();
    initializeMobileMoneyInteractions();
});

/**
 * Initialize payment method selection functionality
 */
function initializePaymentMethods() {
    const paymentMethodCards = document.querySelectorAll('.payment-method-card');
    const paymentInstructions = document.querySelectorAll('.payment-instructions');
    
    paymentMethodCards.forEach(card => {
        card.addEventListener('click', function() {
            const methodType = this.dataset.method;
            
            // Don't allow selection of disabled methods
            if (this.classList.contains('disabled')) {
                showComingSoonMessage();
                return;
            }
            
            // Remove active class from all cards
            paymentMethodCards.forEach(c => c.classList.remove('active'));
            
            // Add active class to clicked card
            this.classList.add('active');
            
            // Hide all payment instructions
            paymentInstructions.forEach(instruction => {
                instruction.classList.remove('active');
            });
            
            // Show relevant payment instructions
            const relevantInstructions = document.querySelector(`.payment-instructions[data-method="${methodType}"]`);
            if (relevantInstructions) {
                relevantInstructions.classList.add('active');
            }
            
            // Update form fields based on selected method
            updateFormForPaymentMethod(methodType);
        });
        
        // Add keyboard support
        card.addEventListener('keydown', function(e) {
            if (e.key === 'Enter' || e.key === ' ') {
                e.preventDefault();
                this.click();
            }
        });
    });
}

/**
 * Initialize mobile money specific interactions
 */
function initializeMobileMoneyInteractions() {
    // Handle mobile money provider selection (for future use)
    const providerLogos = document.querySelectorAll('.provider-logo');
    
    providerLogos.forEach(logo => {
        logo.addEventListener('click', function(e) {
            e.stopPropagation(); // Prevent triggering parent card click
            
            // For now, just show coming soon message
            showComingSoonMessage();
        });
    });
    
    // Handle coming soon overlay interactions
    const comingSoonCards = document.querySelectorAll('.payment-method-card.disabled');
    
    comingSoonCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            showComingSoonTooltip(this);
        });
        
        card.addEventListener('mouseleave', function() {
            hideComingSoonTooltip(this);
        });
    });
}

/**
 * Update form fields based on selected payment method
 * @param {string} methodType - The selected payment method type
 */
function updateFormForPaymentMethod(methodType) {
    const form = document.querySelector('form[action*="wallet.php"]');
    if (!form) return;
    
    // Add hidden input to track selected payment method
    let methodInput = form.querySelector('input[name="payment_method"]');
    if (!methodInput) {
        methodInput = document.createElement('input');
        methodInput.type = 'hidden';
        methodInput.name = 'payment_method';
        form.appendChild(methodInput);
    }
    methodInput.value = methodType;
    
    // Update form validation and fields based on method
    switch (methodType) {
        case 'crypto':
            enableCryptoFields();
            break;
        case 'mobile_money':
            enableMobileMoneyFields();
            break;
        default:
            console.warn('Unknown payment method:', methodType);
    }
}

/**
 * Enable cryptocurrency payment fields
 */
function enableCryptoFields() {
    const cryptoFields = [
        'network',
        'reference',
        'proof_file'
    ];
    
    cryptoFields.forEach(fieldName => {
        const field = document.querySelector(`[name="${fieldName}"]`);
        if (field) {
            field.disabled = false;
            field.required = fieldName !== 'proof_file'; // proof_file is optional
        }
    });
    
    // Update submit button text
    const submitButton = document.querySelector('button[name="buy_tokens"]');
    if (submitButton) {
        submitButton.textContent = 'Submit Purchase';
    }
}

/**
 * Enable mobile money payment fields (for future implementation)
 */
function enableMobileMoneyFields() {
    // Disable crypto-specific fields
    const cryptoFields = [
        'network',
        'reference',
        'proof_file'
    ];
    
    cryptoFields.forEach(fieldName => {
        const field = document.querySelector(`[name="${fieldName}"]`);
        if (field) {
            field.disabled = true;
            field.required = false;
        }
    });
    
    // For now, show coming soon message since mobile money is not yet implemented
    showComingSoonMessage();
    
    // Update submit button text
    const submitButton = document.querySelector('button[name="buy_tokens"]');
    if (submitButton) {
        submitButton.textContent = 'Coming Soon';
        submitButton.disabled = true;
    }
}

/**
 * Show coming soon message
 */
function showComingSoonMessage() {
    // Create or show coming soon modal/alert
    const existingAlert = document.querySelector('.coming-soon-alert');
    if (existingAlert) {
        existingAlert.remove();
    }
    
    const alert = document.createElement('div');
    alert.className = 'alert alert-info coming-soon-alert alert-dismissible fade show';
    alert.style.position = 'fixed';
    alert.style.top = '20px';
    alert.style.right = '20px';
    alert.style.zIndex = '9999';
    alert.style.maxWidth = '400px';
    alert.innerHTML = `
        <div class="d-flex align-items-center">
            <i class="fas fa-mobile-alt mr-2" style="font-size: 1.2rem; color: #28a745;"></i>
            <div>
                <strong>Mobile Money Coming Soon!</strong><br>
                <small>We're working hard to bring you mobile money payments for MTN, Vodafone, and AirtelTigo. Stay tuned!</small>
            </div>
        </div>
        <button type="button" class="close" data-dismiss="alert" aria-label="Close">
            <span aria-hidden="true">&times;</span>
        </button>
    `;
    
    document.body.appendChild(alert);
    
    // Auto-dismiss after 5 seconds
    setTimeout(() => {
        if (alert && alert.parentNode) {
            alert.classList.remove('show');
            setTimeout(() => {
                if (alert && alert.parentNode) {
                    alert.remove();
                }
            }, 150);
        }
    }, 5000);
}

/**
 * Show coming soon tooltip
 * @param {Element} element - The element to show tooltip for
 */
function showComingSoonTooltip(element) {
    const overlay = element.querySelector('.coming-soon-overlay');
    if (overlay) {
        overlay.style.opacity = '1';
    }
}

/**
 * Hide coming soon tooltip
 * @param {Element} element - The element to hide tooltip for
 */
function hideComingSoonTooltip(element) {
    const overlay = element.querySelector('.coming-soon-overlay');
    if (overlay) {
        overlay.style.opacity = '0';
    }
}

/**
 * Initialize mobile money amount calculation (for future use)
 */
function initializeMobileMoneyCalculation() {
    const tokenAmountInput = document.getElementById('token-amount');
    const cedisAmountInput = document.getElementById('cedis-amount');
    
    if (tokenAmountInput && cedisAmountInput) {
        tokenAmountInput.addEventListener('input', function() {
            const tokenAmount = parseFloat(this.value) || 0;
            const tokenRate = parseFloat(document.getElementById('token-rate')?.value) || 0.1;
            const usdAmount = tokenAmount * tokenRate;
            
            // Convert USD to GHS (Ghana Cedis) - rate would come from API in real implementation
            const usdToGhsRate = 12.0; // Example rate - should be dynamic
            const cedisAmount = usdAmount * usdToGhsRate;
            
            cedisAmountInput.value = cedisAmount.toFixed(2);
        });
    }
}

/**
 * Validate mobile money form (for future implementation)
 */
function validateMobileMoneyForm() {
    const form = document.querySelector('form[action*="wallet.php"]');
    if (!form) return true;
    
    const paymentMethod = form.querySelector('input[name="payment_method"]')?.value;
    
    if (paymentMethod === 'mobile_money') {
        // For now, prevent submission since mobile money is not implemented
        showComingSoonMessage();
        return false;
    }
    
    return true;
}

/**
 * Handle form submission
 */
document.addEventListener('submit', function(e) {
    const form = e.target;
    if (form.querySelector('button[name="buy_tokens"]')) {
        if (!validateMobileMoneyForm()) {
            e.preventDefault();
        }
    }
});

/**
 * Utility function to check if mobile money logos exist
 */
function checkMobileMoneyLogos() {
    const providers = ['mtn-mobile-money', 'vodafone-cash', 'airteltigo-money'];
    const logoPath = 'assets/images/mobile-money/';
    
    providers.forEach(provider => {
        const img = new Image();
        img.onload = function() {
            // Logo exists, update UI if needed
            const logoElement = document.querySelector(`img[data-provider="${provider}"]`);
            if (logoElement) {
                logoElement.style.display = 'block';
            }
        };
        img.onerror = function() {
            // Logo doesn't exist, use text fallback
            const logoElement = document.querySelector(`img[data-provider="${provider}"]`);
            if (logoElement) {
                logoElement.style.display = 'none';
                // Show text fallback
                const textFallback = logoElement.nextElementSibling;
                if (textFallback) {
                    textFallback.style.display = 'inline';
                }
            }
        };
        img.src = logoPath + provider + '.png';
    });
}

// Initialize logo checking
document.addEventListener('DOMContentLoaded', checkMobileMoneyLogos);
