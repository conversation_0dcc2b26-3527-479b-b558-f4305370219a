<?php
// Database Configuration
// Try different host settings - uncomment the one that works for you
define('DB_HOST', 'localhost'); // Standard hostname
// define('DB_HOST', '127.0.0.1'); // IP address alternative
// define('DB_HOST', 'localhost:3306'); // Explicit port
// define('DB_HOST', '127.0.0.1:3306'); // IP with explicit port
define('DB_USER', 'root'); // Default MySQL username
define('DB_PASS', ''); // Default MySQL password (empty for XAMPP/WAMP default)
define('DB_NAME', 'p2p_donate'); // Your database name

// Application Configuration
define('SITE_NAME', 'P2P Donate');
define('SITE_URL', 'http://localhost:8000'); // Replace with your actual domain

// Token Configuration
define('TOKEN_RATE', 0.1); // 1 USDT = 10 Token

// Pledge Configuration
// Legacy support for fixed pledge amount
define('PLEDGE_AMOUNT', '200');

// Pledge Categories
define('PLEDGE_AMOUNT_MICRO', '20'); // Micro pledge amount in GHS
define('PLEDGE_AMOUNT_SMALL', '50'); // Small pledge amount in GHS
define('PLEDGE_AMOUNT_MEDIUM', '100'); // Medium pledge amount in GHS
define('PLEDGE_AMOUNT_LARGE', '200'); // Large pledge amount in GHS

// Array of available pledge amounts
$pledge_categories = [
    'micro' => [
        'amount' => PLEDGE_AMOUNT_MICRO,
        'name' => 'Micro',
        'currency' => 'GHS'
    ],
    'small' => [
        'amount' => PLEDGE_AMOUNT_SMALL,
        'name' => 'Small',
        'currency' => 'GHS'
    ],
    'medium' => [
        'amount' => PLEDGE_AMOUNT_MEDIUM,
        'name' => 'Medium',
        'currency' => 'GHS'
    ],
    'large' => [
        'amount' => PLEDGE_AMOUNT_LARGE,
        'name' => 'Large',
        'currency' => 'GHS'
    ]
];

// Session Configuration
define('SESSION_NAME', 'p2p_donate_session');
define('SESSION_LIFETIME', 86400); // 24 hours

// File Upload Configuration
define('UPLOAD_DIR', 'uploads/');
define('MAX_FILE_SIZE', 20 * 1024 * 1024); // 20MB
define('ALLOWED_EXTENSIONS', ['jpg', 'jpeg', 'png', 'pdf', 'mp4', 'webm', 'mov']);

// Analytics Configuration
define('ENABLE_ANALYTICS', false); // Set to true to enable analytics
define('GA_TRACKING_ID', ''); // Google Analytics tracking ID
define('FB_PIXEL_ID', ''); // Facebook Pixel ID
