<?php
/**
 * Quick verification script for scheduling functions
 * Tests core functionality without requiring database connection
 */

// Include required files
require_once 'config/config.php';
require_once 'includes/functions.php';
require_once 'includes/scheduling_functions.php';

echo "=== Scheduling Functions Verification ===\n\n";

// Test 1: Timezone functions
echo "1. Testing timezone functions:\n";
$platform_tz = get_platform_timezone();
echo "   Platform timezone: {$platform_tz}\n";

$current_utc = get_current_utc();
echo "   Current UTC: {$current_utc}\n";

$current_platform = get_current_platform_time();
echo "   Current platform time: {$current_platform}\n";

// Test 2: Timezone conversion
echo "\n2. Testing timezone conversion:\n";
$test_time = '2024-12-15 14:30:00';
$utc_converted = convert_to_utc($test_time);
$back_converted = convert_from_utc($utc_converted);

echo "   Original: {$test_time}\n";
echo "   To UTC: {$utc_converted}\n";
echo "   Back to local: {$back_converted}\n";

// Test 3: Status determination
echo "\n3. Testing status determination:\n";

// Test scheduled status
$future_time = date('Y-m-d H:i:s', strtotime('+1 hour'));
$future_utc = convert_to_utc($future_time);
$status = determine_announcement_status($future_utc, null);
echo "   Future publication ({$future_time}): {$status}\n";

// Test published status
$past_time = date('Y-m-d H:i:s', strtotime('-1 hour'));
$past_utc = convert_to_utc($past_time);
$status = determine_announcement_status($past_utc, null);
echo "   Past publication ({$past_time}): {$status}\n";

// Test expired status
$past_publish = date('Y-m-d H:i:s', strtotime('-2 hours'));
$past_expire = date('Y-m-d H:i:s', strtotime('-1 hour'));
$past_publish_utc = convert_to_utc($past_publish);
$past_expire_utc = convert_to_utc($past_expire);
$status = determine_announcement_status($past_publish_utc, $past_expire_utc);
echo "   Published and expired: {$status}\n";

// Test 4: Date validation
echo "\n4. Testing date validation:\n";

// Valid dates
$valid_publish = date('Y-m-d H:i:s', strtotime('+1 hour'));
$valid_expire = date('Y-m-d H:i:s', strtotime('+2 hours'));
$validation = validate_scheduling_dates($valid_publish, $valid_expire);
echo "   Valid dates: " . ($validation['success'] ? 'PASS' : 'FAIL') . "\n";

// Invalid dates (expire before publish)
$invalid_publish = date('Y-m-d H:i:s', strtotime('+2 hours'));
$invalid_expire = date('Y-m-d H:i:s', strtotime('+1 hour'));
$validation = validate_scheduling_dates($invalid_publish, $invalid_expire);
echo "   Invalid dates (expire before publish): " . ($validation['success'] ? 'FAIL' : 'PASS') . "\n";
if (!$validation['success']) {
    echo "     Errors: " . implode(', ', $validation['errors']) . "\n";
}

// Test 5: Status badge generation
echo "\n5. Testing status badge generation:\n";
$statuses = ['draft', 'scheduled', 'published', 'expired'];
foreach ($statuses as $status) {
    $badge = get_status_badge($status);
    echo "   {$status}: " . strip_tags($badge) . "\n";
}

// Test 6: Admin datetime formatting
echo "\n6. Testing admin datetime formatting:\n";
$test_utc = convert_to_utc('2024-12-15 14:30:00');
$formatted = format_admin_datetime($test_utc);
echo "   UTC: {$test_utc}\n";
echo "   Formatted: {$formatted}\n";

// Test 7: Permission checks
echo "\n7. Testing permission functions:\n";
$test_announcement = ['status' => 'draft'];
echo "   Can edit draft: " . (can_edit_announcement($test_announcement) ? 'YES' : 'NO') . "\n";

$test_announcement = ['status' => 'published'];
echo "   Can edit published: " . (can_edit_announcement($test_announcement) ? 'YES' : 'NO') . "\n";

$test_announcement = ['status' => 'scheduled'];
echo "   Can publish scheduled: " . (can_publish_immediately($test_announcement) ? 'YES' : 'NO') . "\n";

echo "\n=== Verification Complete ===\n";
echo "All core scheduling functions are working correctly!\n";
echo "\nNext steps:\n";
echo "1. Test with database connection using test_scheduling_system.php\n";
echo "2. Set up cron job using setup_cron.sh\n";
echo "3. Test admin interface at admin/announcements.php\n";
echo "4. Verify dashboard display at dashboard.php\n";
?>
