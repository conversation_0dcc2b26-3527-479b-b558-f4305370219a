<?php
// Set page title
$page_title = "Queue Management";

// Include configuration
require_once '../config/config.php';
require_once '../includes/functions.php';
require_once '../database/db_connect.php';

// Start session
start_session();

// Check if user is logged in and is admin
if (!is_logged_in() || !is_admin()) {
    header('Location: ../login.php');
    exit;
}

// Create database instance
$database = new Database();
$db = $database->getConnection();

// Get user ID
$user_id = $_SESSION['user_id'];

// Process actions
$success_message = '';
$error_message = '';

if (isset($_GET['action'])) {
    $action = $_GET['action'];

    switch ($action) {
        case 'add_to_queue':
            if (isset($_GET['user_id']) && isset($_GET['pledge_amount'])) {
                $user_id_to_add = (int)$_GET['user_id'];
                $pledge_amount = (float)$_GET['pledge_amount'];

                // Validate pledge amount
                $valid_amounts = [20, 50, 100, 200];
                if (!in_array($pledge_amount, $valid_amounts)) {
                    $error_message = "Invalid pledge amount. Must be one of: GHS 20, 50, 100, or 200.";
                    break;
                }

                // Check if user exists
                $query = "SELECT * FROM users WHERE id = :user_id";
                $stmt = $db->prepare($query);
                $stmt->bindParam(':user_id', $user_id_to_add);
                $stmt->execute();
                $user = $stmt->fetch(PDO::FETCH_OBJ);

                if ($user) {
                    // Add user to queue with amount-aware system
                    $double_amount = $pledge_amount * 2;
                    $query = "UPDATE users SET
                              pledges_to_receive = 2,
                              amount_to_receive = :amount_to_receive,
                              original_pledge_amount = :original_amount,
                              updated_at = NOW()
                              WHERE id = :user_id";
                    $stmt = $db->prepare($query);
                    $stmt->bindParam(':user_id', $user_id_to_add);
                    $stmt->bindParam(':amount_to_receive', $double_amount);
                    $stmt->bindParam(':original_amount', $pledge_amount);

                    if ($stmt->execute()) {
                        $success_message = "User {$user->name} added to queue to receive GHS {$double_amount} (double of GHS {$pledge_amount} pledge).";
                    } else {
                        $error_message = "Failed to add user to queue.";
                    }
                } else {
                    $error_message = "User not found.";
                }
            } else {
                $error_message = "User ID and pledge amount are required.";
            }
            break;

        case 'remove_from_queue':
            if (isset($_GET['user_id'])) {
                $user_id_to_remove = (int)$_GET['user_id'];

                // Check if user exists
                $query = "SELECT * FROM users WHERE id = :user_id";
                $stmt = $db->prepare($query);
                $stmt->bindParam(':user_id', $user_id_to_remove);
                $stmt->execute();
                $user = $stmt->fetch(PDO::FETCH_OBJ);

                if ($user) {
                    // Remove user from queue and clear amount tracking
                    $query = "UPDATE users SET
                              pledges_to_receive = 0,
                              amount_to_receive = 0,
                              original_pledge_amount = 0,
                              updated_at = NOW()
                              WHERE id = :user_id";
                    $stmt = $db->prepare($query);
                    $stmt->bindParam(':user_id', $user_id_to_remove);

                    if ($stmt->execute()) {
                        $success_message = "User {$user->name} removed from queue.";
                    } else {
                        $error_message = "Failed to remove user from queue.";
                    }
                } else {
                    $error_message = "User not found.";
                }
            }
            break;

        case 'adjust_pledges':
            if (isset($_GET['user_id']) && isset($_GET['count'])) {
                $user_id_to_adjust = (int)$_GET['user_id'];
                $count = (int)$_GET['count'];

                // Validate count
                if ($count < 0) {
                    $error_message = "Pledge count cannot be negative.";
                    break;
                }

                // Check if user exists
                $query = "SELECT * FROM users WHERE id = :user_id";
                $stmt = $db->prepare($query);
                $stmt->bindParam(':user_id', $user_id_to_adjust);
                $stmt->execute();
                $user = $stmt->fetch(PDO::FETCH_OBJ);

                if ($user) {
                    // Adjust pledges to receive
                    // Only update the updated_at timestamp if count is 0 or if we're adding the user to the queue
                    // This maintains consistency with our two-pledge consecutive system
                    if ($count == 0 || $user->pledges_to_receive == 0) {
                        // When removing from queue or adding to queue, update the timestamp
                        $query = "UPDATE users SET
                                  pledges_to_receive = :count,
                                  updated_at = NOW()
                                  WHERE id = :user_id";
                    } else {
                        // When adjusting the count but keeping in queue, don't update the timestamp
                        $query = "UPDATE users SET
                                  pledges_to_receive = :count
                                  WHERE id = :user_id";
                    }
                    $stmt = $db->prepare($query);
                    $stmt->bindParam(':count', $count);
                    $stmt->bindParam(':user_id', $user_id_to_adjust);

                    if ($stmt->execute()) {
                        $success_message = "Updated {$user->name} to receive {$count} pledges.";
                    } else {
                        $error_message = "Failed to update pledge count.";
                    }
                } else {
                    $error_message = "User not found.";
                }
            }
            break;
    }
}

// Get users in queue with amount information
$query = "SELECT u.*,
          (SELECT COUNT(*) FROM pledges WHERE user_id = u.id AND status = 'completed') as pledges_made,
          (SELECT COUNT(*) FROM matches WHERE receiver_id = u.id AND status = 'completed') as pledges_received,
          (SELECT SUM(amount) FROM matches WHERE receiver_id = u.id AND status = 'completed') as total_received
          FROM users u
          WHERE u.pledges_to_receive > 0
          ORDER BY u.updated_at ASC";
$stmt = $db->prepare($query);
$stmt->execute();
$queue_users = $stmt->fetchAll(PDO::FETCH_OBJ);

// Get users not in queue
$query = "SELECT u.*,
          (SELECT COUNT(*) FROM pledges WHERE user_id = u.id AND status = 'completed') as pledges_made,
          (SELECT COUNT(*) FROM matches WHERE receiver_id = u.id AND status = 'completed') as pledges_received
          FROM users u
          WHERE u.pledges_to_receive = 0 OR u.pledges_to_receive IS NULL
          ORDER BY u.name ASC";
$stmt = $db->prepare($query);
$stmt->execute();
$other_users = $stmt->fetchAll(PDO::FETCH_OBJ);
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - <?php echo SITE_NAME; ?></title>
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.1/css/all.min.css">
    <link rel="stylesheet" href="../assets/css/admin.css">
    <link rel="stylesheet" href="../assets/css/admin-dark-mode.css">
    <link rel="stylesheet" href="https://cdn.datatables.net/1.10.22/css/dataTables.bootstrap4.min.css">
</head>
<body>
    <?php include 'includes/admin_navbar.php'; ?>

    <div class="container-fluid">
        <div class="row">
            <?php include 'includes/admin_sidebar.php'; ?>

            <!-- Main Content -->
            <main role="main" class="col-md-9 ml-sm-auto col-lg-10 px-md-4 py-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">Queue Management</h1>
                </div>

                <?php if (!empty($success_message)): ?>
                    <div class="alert alert-success"><?php echo $success_message; ?></div>
                <?php endif; ?>

                <?php if (!empty($error_message)): ?>
                    <div class="alert alert-danger"><?php echo $error_message; ?></div>
                <?php endif; ?>

                <div class="row">
                    <div class="col-md-12">
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="mb-0">Current Queue (<?php echo count($queue_users); ?> users)</h5>
                                <small class="text-muted">Users are listed in order of priority (first in, first out)</small>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-striped">
                                        <thead>
                                            <tr>
                                                <th>Position</th>
                                                <th>Name</th>
                                                <th>Email</th>
                                                <th>Waiting Since</th>
                                                <th>Original Pledge</th>
                                                <th>Amount to Receive</th>
                                                <th>Total Received</th>
                                                <th>Pledges to Receive</th>
                                                <th>Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php if (empty($queue_users)): ?>
                                                <tr>
                                                    <td colspan="9" class="text-center">No users in queue</td>
                                                </tr>
                                            <?php else: ?>
                                                <?php foreach ($queue_users as $index => $user): ?>
                                                    <tr>
                                                        <td><?php echo $index + 1; ?></td>
                                                        <td><?php echo $user->name; ?></td>
                                                        <td><?php echo $user->email; ?></td>
                                                        <td><?php echo format_date($user->updated_at); ?></td>
                                                        <td>
                                                            <?php if ($user->original_pledge_amount > 0): ?>
                                                                <span class="badge badge-info">GHS <?php echo number_format($user->original_pledge_amount, 0); ?></span>
                                                            <?php else: ?>
                                                                <span class="text-muted">Not set</span>
                                                            <?php endif; ?>
                                                        </td>
                                                        <td>
                                                            <?php if ($user->amount_to_receive > 0): ?>
                                                                <span class="badge badge-success">GHS <?php echo number_format($user->amount_to_receive, 0); ?></span>
                                                            <?php else: ?>
                                                                <span class="text-muted">Not set</span>
                                                            <?php endif; ?>
                                                        </td>
                                                        <td>
                                                            <?php if ($user->total_received > 0): ?>
                                                                <span class="badge badge-primary">GHS <?php echo number_format($user->total_received, 0); ?></span>
                                                            <?php else: ?>
                                                                <span class="text-muted">GHS 0</span>
                                                            <?php endif; ?>
                                                        </td>
                                                        <td>
                                                            <form action="queue.php" method="get" class="form-inline">
                                                                <input type="hidden" name="action" value="adjust_pledges">
                                                                <input type="hidden" name="user_id" value="<?php echo $user->id; ?>">
                                                                <div class="input-group input-group-sm">
                                                                    <input type="number" name="count" class="form-control" value="<?php echo $user->pledges_to_receive; ?>" min="0" max="10">
                                                                    <div class="input-group-append">
                                                                        <button type="submit" class="btn btn-outline-secondary">Update</button>
                                                                    </div>
                                                                </div>
                                                            </form>
                                                        </td>
                                                        <td>
                                                            <a href="queue.php?action=remove_from_queue&user_id=<?php echo $user->id; ?>" class="btn btn-sm btn-danger" onclick="return confirm('Are you sure you want to remove this user from the queue?')">
                                                                <i class="fas fa-times"></i> Remove
                                                            </a>
                                                            <a href="users.php?action=view&id=<?php echo $user->id; ?>" class="btn btn-sm btn-info">
                                                                <i class="fas fa-user"></i> View
                                                            </a>
                                                        </td>
                                                    </tr>
                                                <?php endforeach; ?>
                                            <?php endif; ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>

                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">Other Users</h5>
                                <small class="text-muted">Users not currently in the queue</small>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-striped" id="usersTable">
                                        <thead>
                                            <tr>
                                                <th>Name</th>
                                                <th>Email</th>
                                                <th>Pledges Made</th>
                                                <th>Pledges Received</th>
                                                <th>Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php if (empty($other_users)): ?>
                                                <tr>
                                                    <td colspan="5" class="text-center">No users found</td>
                                                </tr>
                                            <?php else: ?>
                                                <?php foreach ($other_users as $user): ?>
                                                    <tr>
                                                        <td><?php echo $user->name; ?></td>
                                                        <td><?php echo $user->email; ?></td>
                                                        <td><?php echo $user->pledges_made; ?></td>
                                                        <td><?php echo $user->pledges_received; ?></td>
                                                        <td>
                                                            <div class="btn-group">
                                                                <button type="button" class="btn btn-sm btn-success dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                                                    <i class="fas fa-plus"></i> Add to Queue
                                                                </button>
                                                                <div class="dropdown-menu">
                                                                    <h6 class="dropdown-header">Select Pledge Amount</h6>
                                                                    <a class="dropdown-item" href="queue.php?action=add_to_queue&user_id=<?php echo $user->id; ?>&pledge_amount=20" onclick="return confirm('Add user to queue with GHS 20 pledge (will receive GHS 40)?')">GHS 20 → GHS 40</a>
                                                                    <a class="dropdown-item" href="queue.php?action=add_to_queue&user_id=<?php echo $user->id; ?>&pledge_amount=50" onclick="return confirm('Add user to queue with GHS 50 pledge (will receive GHS 100)?')">GHS 50 → GHS 100</a>
                                                                    <a class="dropdown-item" href="queue.php?action=add_to_queue&user_id=<?php echo $user->id; ?>&pledge_amount=100" onclick="return confirm('Add user to queue with GHS 100 pledge (will receive GHS 200)?')">GHS 100 → GHS 200</a>
                                                                    <a class="dropdown-item" href="queue.php?action=add_to_queue&user_id=<?php echo $user->id; ?>&pledge_amount=200" onclick="return confirm('Add user to queue with GHS 200 pledge (will receive GHS 400)?')">GHS 200 → GHS 400</a>
                                                                </div>
                                                            </div>
                                                            <a href="users.php?action=view&id=<?php echo $user->id; ?>" class="btn btn-sm btn-info">
                                                                <i class="fas fa-user"></i> View
                                                            </a>
                                                        </td>
                                                    </tr>
                                                <?php endforeach; ?>
                                            <?php endif; ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.5.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/popper.js@1.16.1/dist/umd/popper.min.js"></script>
    <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>
    <script src="https://cdn.datatables.net/1.10.22/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.10.22/js/dataTables.bootstrap4.min.js"></script>
    <?php include 'includes/dark_mode_script.php'; ?>
    <script src="../assets/js/admin.js"></script>

    <script>
    $(document).ready(function() {
        // Initialize DataTable for better user experience
        $('#usersTable').DataTable({
            "pageLength": 10,
            "order": [[0, "asc"]]
        });
    });
    </script>
</body>
</html>
