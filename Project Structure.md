👤 User Pages (After Login)
Dashboard (Landing Page)
Top Nav Bar

Logo, Notification bell, Profile icon

Sidebar Navigation

Links: Wallet, Pledges, Matches, Chat, Notifications, Profile

Welcome Header

Greeting and user summary (e.g., Token Balance, Status)

Quick Access Cards

"Make a Pledge", "Buy Tokens", "Active Match"

Wallet Page
Wallet Summary

Token Balance

USDT Info / Current Rate

Buy Tokens Block

Input amount

Payment instructions (wallet address)

Upload proof form

Transaction History Table

Date, Amount, Type, Status

Pledges Page
Make a Pledge Block

Button + Confirmation modal

Active Pledges

Table or card view of current pledges

Pledge History

Past pledges and outcomes (success, timeout, etc.)

Matches Page
Current Match Block

Receiver's info (name, number, wallet)

Countdown timer

Upload Payment Proof

Upload field, transaction ID, reference

Received Confirmation (if user is receiver)

Button to confirm payment received

History

Past matches with status and links to proof/chat

Chat Page
Active Match Thread

Sender ↔ Receiver conversation

File/image upload if needed

Notifications Page
Alert Feed

System alerts: match assigned, pledge confirmed, token low, etc.

Profile Page
User Info
Name, Email, Mobile Money Number, Mobile Money Name
Edit Form
Update details
Security Block
Change password
Logout Button

⚠️ Dispute Resolution Pages
Report a Problem
Report Form

Match ID dropdown

Reason for dispute

Upload field

Submit button

Guidelines for Disputes
Textual Guide

Step-by-step dispute handling process

What qualifies as a valid issue

Response times and contact info

🛠️ Admin Pages (Protected Route)
Admin Dashboard
Overview Stats

Users, Active Pledges, Matches, Disputes

Quick Actions

Resolve Dispute, Credit Tokens

Users Page
User Table

Name, Email, Token Balance, Status

Actions: Block, View Profile

Pledges & Matches Page
Pending Matches

Assign manually or cancel

Confirmed Matches

Status check, edit or reset match

Disputes

List of active cases with evidence links

Token Management Page
Credit Tokens Form

Select user, amount, reason

Submit button

Transactions Log
All Uploads

Searchable by user, match ID, date

Link to associated match

Admin Settings
Change Admin Credentials

Input form (stored in .env)

📃 Legal Pages
Terms of Use
Full policy text

Privacy Policy
Data handling explanation

Disclaimer
System nature, voluntary gifting, risk statement

🆘 Support Page
Contact Form
Name, Email, Subject, Message

Response Time Info

