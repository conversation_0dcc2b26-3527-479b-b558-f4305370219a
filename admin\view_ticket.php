<?php
// Set page title
$page_title = 'View Ticket';

// Include configuration
require_once '../config/config.php';
require_once '../includes/functions.php';
require_once '../database/db_connect.php';

// Start session
start_session();

// Check if user is logged in and is admin
if (!is_logged_in() || !is_admin()) {
    redirect('../login.php');
}

// Create database instance
$database = new Database();
$db = $database->getConnection();

// Get user ID
$user_id = $_SESSION['user_id'];

// Check if ticket ID is provided
if (!isset($_GET['id']) || empty($_GET['id'])) {
    redirect('support_tickets.php');
}

$ticket_id = $_GET['id'];

// Get ticket details with user information
$query = "SELECT t.*, u.name as user_name, u.email as user_email, a.name as admin_name
          FROM support_tickets t
          JOIN users u ON t.user_id = u.id
          LEFT JOIN users a ON t.admin_id = a.id
          WHERE t.id = :id";
$stmt = $db->prepare($query);
$stmt->bindParam(':id', $ticket_id);
$stmt->execute();
$ticket = $stmt->fetch(PDO::FETCH_OBJ);

// If ticket not found, redirect
if (!$ticket) {
    redirect('support_tickets.php');
}

// Process reply form
$reply_message = '';
$reply_err = '';
$success_message = '';
$error_message = '';

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    if (isset($_POST['update_status'])) {
        $status = $_POST['status'];

        $query = "UPDATE support_tickets SET status = :status, updated_at = NOW() WHERE id = :id";
        $stmt = $db->prepare($query);
        $stmt->bindParam(':status', $status);
        $stmt->bindParam(':id', $ticket_id);

        if ($stmt->execute()) {
            // Create notification for user
            create_notification(
                $ticket->user_id,
                'Support Ticket Updated',
                "Your support ticket #{$ticket->ticket_number} status has been updated to " . ucfirst($status) . ".",
                'support',
                $db
            );

            $success_message = 'Ticket status updated successfully.';

            // Refresh ticket data
            $refresh_query = "SELECT t.*, u.name as user_name, u.email as user_email, a.name as admin_name
                             FROM support_tickets t
                             JOIN users u ON t.user_id = u.id
                             LEFT JOIN users a ON t.admin_id = a.id
                             WHERE t.id = :id";
            $stmt = $db->prepare($refresh_query);
            $stmt->bindParam(':id', $ticket_id);
            $stmt->execute();
            $ticket = $stmt->fetch(PDO::FETCH_OBJ);
        } else {
            $error_message = 'Failed to update ticket status.';
        }
    } elseif (isset($_POST['submit_reply'])) {
        // Validate reply message
        if (empty(trim($_POST['reply_message']))) {
            $reply_err = 'Please enter your reply.';
        } else {
            $reply_message = sanitize($_POST['reply_message']);
        }

        // Process file upload if provided
        $attachment = null;
        if (isset($_FILES['reply_attachment']) && $_FILES['reply_attachment']['error'] != UPLOAD_ERR_NO_FILE) {
            $upload_result = upload_file($_FILES['reply_attachment'], '../uploads/support/');

            if (!$upload_result['success']) {
                $file_err = $upload_result['message'];
            } else {
                $attachment = $upload_result['filename'];
            }
        }

        // If no errors, process the reply
        if (empty($reply_err) && empty($file_err)) {
            // Start transaction
            $db->beginTransaction();

            try {
                // Update ticket status to in_progress if it was open
                if ($ticket->status == 'open') {
                    $query = "UPDATE support_tickets SET status = 'in_progress', admin_id = :admin_id, updated_at = NOW() WHERE id = :id";
                    $stmt = $db->prepare($query);
                    $stmt->bindParam(':admin_id', $user_id);
                    $stmt->bindParam(':id', $ticket_id);
                    $stmt->execute();
                }

                // Insert reply as a new message
                $query = "INSERT INTO support_ticket_replies (ticket_id, user_id, message, attachment, is_admin)
                          VALUES (:ticket_id, :user_id, :message, :attachment, 1)";
                $stmt = $db->prepare($query);

                $stmt->bindParam(':ticket_id', $ticket_id);
                $stmt->bindParam(':user_id', $user_id);
                $stmt->bindParam(':message', $reply_message);
                $stmt->bindParam(':attachment', $attachment);

                $stmt->execute();

                // Create notification for user
                create_notification(
                    $ticket->user_id,
                    'Support Ticket Response',
                    "Your support ticket #{$ticket->ticket_number} has received a response from our support team.",
                    'support',
                    $db
                );

                // Commit transaction
                $db->commit();

                $success_message = 'Your reply has been sent successfully.';
                $reply_message = '';

                // Refresh ticket data
                $refresh_query = "SELECT t.*, u.name as user_name, u.email as user_email, a.name as admin_name
                                 FROM support_tickets t
                                 JOIN users u ON t.user_id = u.id
                                 LEFT JOIN users a ON t.admin_id = a.id
                                 WHERE t.id = :id";
                $stmt = $db->prepare($refresh_query);
                $stmt->bindParam(':id', $ticket_id);
                $stmt->execute();
                $ticket = $stmt->fetch(PDO::FETCH_OBJ);
            } catch (Exception $e) {
                // Rollback transaction on error
                $db->rollBack();
                $error_message = 'An error occurred while sending your reply. Please try again.';
                error_log("Error replying to support ticket: " . $e->getMessage());
            }
        }
    }
}

// Get ticket replies
$query = "SELECT r.*, u.name as user_name
          FROM support_ticket_replies r
          LEFT JOIN users u ON r.user_id = u.id
          WHERE r.ticket_id = :ticket_id
          ORDER BY r.created_at ASC";
$stmt = $db->prepare($query);
$stmt->bindParam(':ticket_id', $ticket_id);
$stmt->execute();
$replies = $stmt->fetchAll(PDO::FETCH_OBJ);
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - <?php echo SITE_NAME; ?></title>
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.1/css/all.min.css">
    <link rel="stylesheet" href="../assets/css/admin.css">
    <link rel="stylesheet" href="../assets/css/admin-dark-mode.css">
</head>
<body>
    <?php include 'includes/admin_navbar.php'; ?>

    <div class="container-fluid">
        <div class="row">
            <?php include 'includes/admin_sidebar.php'; ?>

            <!-- Main Content -->
            <main role="main" class="col-md-9 ml-sm-auto col-lg-10 px-md-4 py-4">
    <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
        <h1 class="h2">Ticket #<?php echo $ticket->ticket_number; ?></h1>
        <div class="btn-toolbar mb-2 mb-md-0">
            <div class="btn-group mr-2">
                <?php if ($ticket->status != 'open'): ?>
                    <form action="<?php echo htmlspecialchars($_SERVER["PHP_SELF"] . '?id=' . $ticket_id); ?>" method="post" class="d-inline">
                        <input type="hidden" name="status" value="open">
                        <button type="submit" name="update_status" class="btn btn-sm btn-outline-primary">
                            <i class="fas fa-envelope-open"></i> Mark as Open
                        </button>
                    </form>
                <?php endif; ?>

                <?php if ($ticket->status != 'in_progress'): ?>
                    <form action="<?php echo htmlspecialchars($_SERVER["PHP_SELF"] . '?id=' . $ticket_id); ?>" method="post" class="d-inline">
                        <input type="hidden" name="status" value="in_progress">
                        <button type="submit" name="update_status" class="btn btn-sm btn-outline-warning">
                            <i class="fas fa-spinner"></i> Mark as In Progress
                        </button>
                    </form>
                <?php endif; ?>

                <?php if ($ticket->status != 'closed'): ?>
                    <form action="<?php echo htmlspecialchars($_SERVER["PHP_SELF"] . '?id=' . $ticket_id); ?>" method="post" class="d-inline">
                        <input type="hidden" name="status" value="closed">
                        <button type="submit" name="update_status" class="btn btn-sm btn-outline-success">
                            <i class="fas fa-check-circle"></i> Mark as Closed
                        </button>
                    </form>
                <?php endif; ?>
            </div>

            <a href="support_tickets.php" class="btn btn-sm btn-secondary">
                <i class="fas fa-arrow-left"></i> Back to Tickets
            </a>
        </div>
    </div>

    <?php if (!empty($success_message)): ?>
        <div class="alert alert-success"><?php echo $success_message; ?></div>
    <?php endif; ?>

    <?php if (!empty($error_message)): ?>
        <div class="alert alert-danger"><?php echo $error_message; ?></div>
    <?php endif; ?>

    <!-- Ticket Details -->
    <div class="row">
        <div class="col-md-8">
            <div class="card mb-4">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0"><?php echo $ticket->subject; ?></h5>
                    <span class="badge badge-<?php echo ($ticket->status == 'open') ? 'primary' : (($ticket->status == 'in_progress') ? 'warning' : 'success'); ?>">
                        <?php echo ucfirst($ticket->status); ?>
                    </span>
                </div>
                <div class="card-body">
                    <div class="ticket-message p-3 bg-light rounded mb-3">
                        <div class="d-flex justify-content-between mb-2">
                            <span><strong><?php echo $ticket->user_name; ?></strong></span>
                            <small><?php echo format_date($ticket->created_at); ?></small>
                        </div>
                        <p><?php echo nl2br(htmlspecialchars($ticket->message)); ?></p>
                        <?php if ($ticket->attachment): ?>
                            <div class="mt-2">
                                <a href="../uploads/support/<?php echo $ticket->attachment; ?>" target="_blank" class="btn btn-sm btn-outline-primary">
                                    <i class="fas fa-paperclip"></i> View Attachment
                                </a>
                            </div>
                        <?php endif; ?>
                    </div>

                    <?php if ($ticket->admin_response): ?>
                        <div class="ticket-response p-3 bg-info text-white rounded mb-3">
                            <div class="d-flex justify-content-between mb-2">
                                <span><strong>Support Team (<?php echo $ticket->admin_name; ?>)</strong></span>
                                <small><?php echo format_date($ticket->updated_at); ?></small>
                            </div>
                            <p><?php echo nl2br(htmlspecialchars($ticket->admin_response)); ?></p>
                        </div>
                    <?php endif; ?>

                    <!-- Ticket Replies -->
                    <?php if (!empty($replies)): ?>
                        <h6 class="mt-4 mb-3">Conversation History</h6>
                        <?php foreach ($replies as $reply): ?>
                            <div class="ticket-reply p-3 <?php echo $reply->is_admin ? 'bg-info text-white' : 'bg-light'; ?> rounded mb-3">
                                <div class="d-flex justify-content-between mb-2">
                                    <span><strong><?php echo $reply->is_admin ? 'Support Team (' . $reply->user_name . ')' : $reply->user_name; ?></strong></span>
                                    <small><?php echo format_date($reply->created_at); ?></small>
                                </div>
                                <p><?php echo nl2br(htmlspecialchars($reply->message)); ?></p>
                                <?php if ($reply->attachment): ?>
                                    <div class="mt-2">
                                        <a href="../uploads/support/<?php echo $reply->attachment; ?>" target="_blank" class="btn btn-sm btn-outline-<?php echo $reply->is_admin ? 'light' : 'primary'; ?>">
                                            <i class="fas fa-paperclip"></i> View Attachment
                                        </a>
                                    </div>
                                <?php endif; ?>
                            </div>
                        <?php endforeach; ?>
                    <?php endif; ?>

                    <!-- Reply Form -->
                    <?php if ($ticket->status != 'closed'): ?>
                        <div class="card mt-4">
                            <div class="card-header">
                                <h6 class="mb-0">Add Reply</h6>
                            </div>
                            <div class="card-body">
                                <form action="<?php echo htmlspecialchars($_SERVER["PHP_SELF"] . '?id=' . $ticket_id); ?>" method="post" enctype="multipart/form-data">
                                    <div class="form-group">
                                        <textarea name="reply_message" class="form-control <?php echo (!empty($reply_err)) ? 'is-invalid' : ''; ?>" rows="4" placeholder="Type your reply here..."><?php echo $reply_message; ?></textarea>
                                        <span class="invalid-feedback"><?php echo $reply_err; ?></span>
                                    </div>

                                    <div class="form-group">
                                        <div class="custom-file">
                                            <input type="file" name="reply_attachment" class="custom-file-input" id="reply_attachment">
                                            <label class="custom-file-label" for="reply_attachment">Attach a file (optional)</label>
                                        </div>
                                        <small class="form-text text-muted">Accepted formats: JPG, PNG, PDF. Max size: 20MB</small>
                                    </div>

                                    <button type="submit" name="submit_reply" class="btn btn-primary">Send Reply</button>
                                </form>
                            </div>
                        </div>
                    <?php else: ?>
                        <div class="alert alert-info mt-4">
                            <p class="mb-0">This ticket is closed. To respond, please reopen the ticket first.</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <!-- User Information -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">User Information</h5>
                </div>
                <div class="card-body">
                    <p><strong>Name:</strong> <?php echo $ticket->user_name; ?></p>
                    <p><strong>Email:</strong> <?php echo $ticket->user_email; ?></p>
                    <p><strong>User ID:</strong> <?php echo $ticket->user_id; ?></p>
                    <a href="users.php?id=<?php echo $ticket->user_id; ?>" class="btn btn-sm btn-outline-primary">
                        <i class="fas fa-user"></i> View User Profile
                    </a>
                </div>
            </div>

            <!-- Ticket Information -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">Ticket Information</h5>
                </div>
                <div class="card-body">
                    <p><strong>Ticket ID:</strong> <?php echo $ticket->id; ?></p>
                    <p><strong>Ticket Number:</strong> <?php echo $ticket->ticket_number; ?></p>
                    <p><strong>Category:</strong> <?php echo $ticket->category; ?></p>
                    <p><strong>Status:</strong>
                        <span class="badge badge-<?php echo ($ticket->status == 'open') ? 'primary' : (($ticket->status == 'in_progress') ? 'warning' : 'success'); ?>">
                            <?php echo ucfirst($ticket->status); ?>
                        </span>
                    </p>
                    <p><strong>Created:</strong> <?php echo format_date($ticket->created_at); ?></p>
                    <p><strong>Last Updated:</strong> <?php echo format_date($ticket->updated_at); ?></p>
                    <?php if ($ticket->admin_id): ?>
                        <p><strong>Assigned To:</strong> <?php echo $ticket->admin_name; ?></p>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Update file input label with selected filename
$(document).ready(function() {
    $('.custom-file-input').on('change', function() {
        var fileName = $(this).val().split('\\').pop();
        $(this).next('.custom-file-label').html(fileName);
    });
});
</script>
</main>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.5.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/popper.js@1.16.1/dist/umd/popper.min.js"></script>
    <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>
    <script src="../assets/js/admin.js"></script>
</body>
</html>
