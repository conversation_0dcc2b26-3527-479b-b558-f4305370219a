<?php
// Set page title
$page_title = 'Announcements';

// Include configuration
require_once '../config/config.php';
require_once '../includes/functions.php';
require_once '../includes/announcement_functions.php';
require_once '../includes/scheduling_functions.php';
require_once '../database/db_connect.php';

// Start session
start_session();

// Check if user is logged in and is admin
if (!is_logged_in() || !is_admin()) {
    redirect('../login.php');
}

// Create database instance
$database = new Database();
$db = $database->getConnection();

// Get user ID
$user_id = $_SESSION['user_id'];

// Process form submission
$success_message = '';
$error_message = '';

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    // Validate input
    $title = sanitize($_POST['title']);
    $message = sanitize($_POST['message']);
    $recipient_type = sanitize($_POST['recipient_type']);
    $video_url = sanitize($_POST['video_url'] ?? '');
    $priority = sanitize($_POST['priority'] ?? 'normal');
    $is_pinned = isset($_POST['is_pinned']) ? 1 : 0;

    // Handle scheduling
    $scheduled_publish_at = sanitize($_POST['scheduled_publish_at'] ?? '');
    $expires_at = sanitize($_POST['expires_at'] ?? '');
    $publish_immediately = isset($_POST['publish_immediately']);

    // Convert scheduling dates to UTC
    $scheduled_publish_utc = null;
    $expires_at_utc = null;

    if (!$publish_immediately && !empty($scheduled_publish_at)) {
        $scheduled_publish_utc = convert_to_utc($scheduled_publish_at);
    }

    if (!empty($expires_at)) {
        $expires_at_utc = convert_to_utc($expires_at);
    }

    // Validate scheduling dates
    $scheduling_validation = validate_scheduling_dates($scheduled_publish_at, $expires_at);
    if (!$scheduling_validation['success']) {
        $file_err = implode(' ', $scheduling_validation['errors']);
    }

    // Initialize file variables
    $image_file = null;
    $video_file = null;
    $file_err = '';
    $link_preview_data = null;

    // Validate video URL if provided
    if (!empty($video_url)) {
        $video_validation = validate_video_url($video_url);
        if (!$video_validation) {
            $file_err = 'Invalid video URL. Please provide a valid YouTube or Vimeo URL.';
        }
    }

    // Process link previews from message content
    if (empty($file_err)) {
        $content_data = process_announcement_content($message);
        if (!empty($content_data['previews'])) {
            $link_preview_data = json_encode($content_data['previews']);
        }
    }

    // Process image upload if provided
    if (isset($_FILES['image_file']) && $_FILES['image_file']['error'] != UPLOAD_ERR_NO_FILE) {
        // Ensure the upload directory exists
        $upload_dir = '../uploads/announcements/';
        if (!file_exists($upload_dir)) {
            mkdir($upload_dir, 0777, true);
        }

        $upload_result = upload_file($_FILES['image_file'], $upload_dir);

        if (!$upload_result['success']) {
            $file_err = 'Image upload failed: ' . $upload_result['message'];
        } else {
            $image_file = $upload_result['filename'];
        }
    }

    // Process video upload if provided
    if (empty($file_err) && isset($_FILES['video_file']) && $_FILES['video_file']['error'] != UPLOAD_ERR_NO_FILE) {
        // Ensure the upload directory exists
        $upload_dir = '../uploads/announcements/';
        if (!file_exists($upload_dir)) {
            mkdir($upload_dir, 0777, true);
        }

        $upload_result = upload_file($_FILES['video_file'], $upload_dir);

        if (!$upload_result['success']) {
            $file_err = 'Video upload failed: ' . $upload_result['message'];
        } else {
            $video_file = $upload_result['filename'];
        }
    }

    // Validate title and message
    if (empty($title)) {
        $error_message = 'Please enter a title for the announcement.';
    } elseif (empty($message)) {
        $error_message = 'Please enter a message for the announcement.';
    } elseif (!empty($file_err)) {
        $error_message = $file_err;
    } else {
        // Start transaction
        $db->beginTransaction();

        try {
            // Get recipients based on selection
            $recipients = [];

            if ($recipient_type == 'all') {
                // Get all active users
                $query = "SELECT id FROM users WHERE status = 'active'";
                $stmt = $db->prepare($query);
                $stmt->execute();
                while ($user = $stmt->fetch(PDO::FETCH_OBJ)) {
                    $recipients[] = $user->id;
                }
            } elseif ($recipient_type == 'in_queue') {
                // Get users in the pledge queue
                $query = "SELECT id FROM users WHERE pledges_to_receive > 0 AND status = 'active'";
                $stmt = $db->prepare($query);
                $stmt->execute();
                while ($user = $stmt->fetch(PDO::FETCH_OBJ)) {
                    $recipients[] = $user->id;
                }
            } elseif ($recipient_type == 'with_tokens') {
                // Get users with token balance > 0
                $query = "SELECT id FROM users WHERE token_balance > 0 AND status = 'active'";
                $stmt = $db->prepare($query);
                $stmt->execute();
                while ($user = $stmt->fetch(PDO::FETCH_OBJ)) {
                    $recipients[] = $user->id;
                }
            } elseif ($recipient_type == 'custom' && !empty($_POST['selected_users'])) {
                // Use selected users
                $recipients = $_POST['selected_users'];
            }

            // Create notifications for each recipient
            $count = 0;
            foreach ($recipients as $recipient_id) {
                create_notification($recipient_id, $title, $message, 'system', $db);
                $count++;
            }

            // Determine announcement status
            $announcement_status = 'published'; // Default for immediate publication
            if (!$publish_immediately) {
                $announcement_status = determine_announcement_status($scheduled_publish_utc, $expires_at_utc);
            }

            // Log the announcement
            $query = "INSERT INTO admin_announcements (admin_id, title, message, image_file, video_file, video_url, priority, link_preview_data, is_pinned, recipient_type, recipients_count, scheduled_publish_at, expires_at, status)
                      VALUES (:admin_id, :title, :message, :image_file, :video_file, :video_url, :priority, :link_preview_data, :is_pinned, :recipient_type, :recipients_count, :scheduled_publish_at, :expires_at, :status)";
            $stmt = $db->prepare($query);
            $stmt->bindParam(':admin_id', $user_id);
            $stmt->bindParam(':title', $title);
            $stmt->bindParam(':message', $message);
            $stmt->bindParam(':image_file', $image_file);
            $stmt->bindParam(':video_file', $video_file);
            $stmt->bindParam(':video_url', $video_url);
            $stmt->bindParam(':priority', $priority);
            $stmt->bindParam(':link_preview_data', $link_preview_data);
            $stmt->bindParam(':is_pinned', $is_pinned);
            $stmt->bindParam(':recipient_type', $recipient_type);
            $stmt->bindParam(':recipients_count', $count);
            $stmt->bindParam(':scheduled_publish_at', $scheduled_publish_utc);
            $stmt->bindParam(':expires_at', $expires_at_utc);
            $stmt->bindParam(':status', $announcement_status);
            $stmt->execute();

            // Commit transaction
            $db->commit();

            $success_message = "Announcement sent successfully to $count users.";

            // Clear form data
            $title = $message = '';
            $recipient_type = 'all';
        } catch (Exception $e) {
            // Rollback transaction on error
            $db->rollBack();
            $error_message = 'Error sending announcement: ' . $e->getMessage();
        }
    }
}

// Update announcement statuses first
update_announcement_statuses($db);

// Get recent announcements with status information
$query = "SELECT a.*, u.name as admin_name,
                 CASE
                     WHEN a.scheduled_publish_at IS NULL THEN 'published'
                     WHEN a.scheduled_publish_at > UTC_TIMESTAMP() THEN 'scheduled'
                     WHEN a.expires_at IS NOT NULL AND a.expires_at <= UTC_TIMESTAMP() THEN 'expired'
                     ELSE 'published'
                 END as calculated_status
          FROM admin_announcements a
          JOIN users u ON a.admin_id = u.id
          ORDER BY
              CASE a.status
                  WHEN 'scheduled' THEN 1
                  WHEN 'published' THEN 2
                  WHEN 'draft' THEN 3
                  WHEN 'expired' THEN 4
              END,
              a.created_at DESC
          LIMIT 20";
$stmt = $db->prepare($query);
$stmt->execute();
$recent_announcements = $stmt->fetchAll(PDO::FETCH_OBJ);

// Get user list for custom recipients
$query = "SELECT id, name, email FROM users WHERE status = 'active' ORDER BY name";
$stmt = $db->prepare($query);
$stmt->execute();
$all_users = $stmt->fetchAll(PDO::FETCH_OBJ);
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - <?php echo SITE_NAME; ?></title>
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.1/css/all.min.css">
    <link rel="stylesheet" href="../assets/css/admin.css">
    <link rel="stylesheet" href="../assets/css/admin-dark-mode.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/select2-bootstrap4-theme@1.0.0/dist/select2-bootstrap4.min.css">
</head>
<body>
    <?php include 'includes/admin_navbar.php'; ?>

    <div class="container-fluid">
        <div class="row">
            <?php include 'includes/admin_sidebar.php'; ?>

            <main role="main" class="col-md-9 ml-sm-auto col-lg-10 px-md-4 py-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2"><?php echo $page_title; ?></h1>
                </div>

                <?php if (!empty($success_message)): ?>
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <?php echo $success_message; ?>
                        <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                <?php endif; ?>

                <?php if (!empty($error_message)): ?>
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <?php echo $error_message; ?>
                        <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                <?php endif; ?>

                <?php
                // Display flash messages
                $flash_success = flash_message('success_message');
                $flash_error = flash_message('error_message');

                if (!empty($flash_success)) {
                    echo $flash_success;
                }

                if (!empty($flash_error)) {
                    echo $flash_error;
                }
                ?>

                <div class="row">
                    <div class="col-md-6">
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="mb-0">Send Announcement</h5>
                            </div>
                            <div class="card-body">
                                <form action="<?php echo htmlspecialchars($_SERVER["PHP_SELF"]); ?>" method="post" enctype="multipart/form-data">
                                    <div class="form-group">
                                        <label for="title">Title</label>
                                        <input type="text" name="title" id="title" class="form-control" value="<?php echo isset($title) ? $title : ''; ?>" required>
                                    </div>
                                    <div class="form-group">
                                        <label for="message">Message</label>
                                        <textarea name="message" id="message" class="form-control" rows="5" required><?php echo isset($message) ? $message : ''; ?></textarea>
                                        <small class="form-text text-muted">URLs in the message will automatically generate link previews</small>
                                    </div>

                                    <!-- Link Preview Container -->
                                    <div id="link-preview-container" class="form-group" style="display: none;">
                                        <label>Link Previews</label>
                                        <div id="link-previews"></div>
                                    </div>

                                    <div class="form-group">
                                        <label for="video_url">Video URL (Optional)</label>
                                        <input type="url" name="video_url" id="video_url" class="form-control"
                                               value="<?php echo isset($video_url) ? $video_url : ''; ?>"
                                               placeholder="https://www.youtube.com/watch?v=... or https://vimeo.com/...">
                                        <small class="form-text text-muted">Supports YouTube and Vimeo URLs</small>
                                        <div id="video-preview" class="mt-2" style="display: none;"></div>
                                    </div>

                                    <div class="form-group">
                                        <label for="priority">Priority Level</label>
                                        <select name="priority" id="priority" class="form-control">
                                            <option value="normal" <?php echo (isset($priority) && $priority == 'normal') ? 'selected' : ''; ?>>Normal</option>
                                            <option value="info" <?php echo (isset($priority) && $priority == 'info') ? 'selected' : ''; ?>>Info</option>
                                            <option value="urgent" <?php echo (isset($priority) && $priority == 'urgent') ? 'selected' : ''; ?>>Urgent</option>
                                        </select>
                                        <small class="form-text text-muted">Urgent announcements will be highlighted prominently</small>
                                    </div>

                                    <div class="form-group">
                                        <div class="custom-control custom-checkbox">
                                            <input type="checkbox" name="is_pinned" class="custom-control-input" id="is_pinned"
                                                   <?php echo (isset($is_pinned) && $is_pinned) ? 'checked' : ''; ?>>
                                            <label class="custom-control-label" for="is_pinned">Pin this announcement</label>
                                            <small class="form-text text-muted">Pinned announcements remain visible until dismissed</small>
                                        </div>
                                    </div>

                                    <!-- Scheduling Section -->
                                    <div class="card mt-4">
                                        <div class="card-header">
                                            <h6 class="mb-0"><i class="fas fa-clock"></i> Scheduling Options</h6>
                                        </div>
                                        <div class="card-body">
                                            <div class="form-group">
                                                <div class="custom-control custom-checkbox">
                                                    <input type="checkbox" name="publish_immediately" class="custom-control-input" id="publish_immediately" checked>
                                                    <label class="custom-control-label" for="publish_immediately">Publish immediately</label>
                                                    <small class="form-text text-muted">Uncheck to schedule for later publication</small>
                                                </div>
                                            </div>

                                            <div id="scheduling-fields" style="display: none;">
                                                <div class="form-group">
                                                    <label for="scheduled_publish_at">Schedule Publication</label>
                                                    <input type="datetime-local" name="scheduled_publish_at" id="scheduled_publish_at" class="form-control">
                                                    <small class="form-text text-muted">
                                                        Set when this announcement should be published (<?php echo get_platform_timezone(); ?> timezone)
                                                    </small>
                                                </div>
                                            </div>

                                            <div class="form-group">
                                                <label for="expires_at">Auto-Expiration (Optional)</label>
                                                <input type="datetime-local" name="expires_at" id="expires_at" class="form-control">
                                                <small class="form-text text-muted">
                                                    Set when this announcement should automatically disappear (<?php echo get_platform_timezone(); ?> timezone)
                                                </small>
                                            </div>

                                            <div class="alert alert-info">
                                                <small>
                                                    <i class="fas fa-info-circle"></i>
                                                    <strong>Current Time:</strong> <?php echo get_current_platform_time(); ?> (<?php echo get_platform_timezone(); ?>)
                                                </small>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label for="image_file">Image (Optional)</label>
                                        <div class="custom-file">
                                            <input type="file" name="image_file" class="custom-file-input" id="image_file" accept=".jpg,.jpeg,.png">
                                            <label class="custom-file-label" for="image_file">Choose image</label>
                                        </div>
                                        <small class="form-text text-muted">Accepted formats: JPG, PNG. Max size: 20MB</small>
                                    </div>
                                    <div class="form-group">
                                        <img id="image-preview" class="img-fluid mb-3" style="display: none; max-height: 200px;">
                                    </div>
                                    <div class="form-group">
                                        <label for="video_file">Video (Optional)</label>
                                        <div class="custom-file">
                                            <input type="file" name="video_file" class="custom-file-input" id="video_file" accept=".mp4,.webm,.mov">
                                            <label class="custom-file-label" for="video_file">Choose video</label>
                                        </div>
                                        <small class="form-text text-muted">Accepted formats: MP4, WebM, MOV. Max size: 20MB</small>
                                    </div>
                                    <div class="form-group">
                                        <label for="recipient_type">Recipients</label>
                                        <select name="recipient_type" id="recipient_type" class="form-control">
                                            <option value="all" <?php echo (isset($recipient_type) && $recipient_type == 'all') ? 'selected' : ''; ?>>All Users</option>
                                            <option value="in_queue" <?php echo (isset($recipient_type) && $recipient_type == 'in_queue') ? 'selected' : ''; ?>>Users in Queue</option>
                                            <option value="with_tokens" <?php echo (isset($recipient_type) && $recipient_type == 'with_tokens') ? 'selected' : ''; ?>>Users with Tokens</option>
                                            <option value="custom" <?php echo (isset($recipient_type) && $recipient_type == 'custom') ? 'selected' : ''; ?>>Custom Selection</option>
                                        </select>
                                    </div>
                                    <div id="custom_recipients" class="form-group" style="display: none;">
                                        <label for="selected_users">Select Users</label>
                                        <select name="selected_users[]" id="selected_users" class="form-control select2" multiple>
                                            <?php foreach ($all_users as $user): ?>
                                                <option value="<?php echo $user->id; ?>"><?php echo $user->name; ?> (<?php echo $user->email; ?>)</option>
                                            <?php endforeach; ?>
                                        </select>
                                    </div>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-paper-plane"></i> Send Announcement
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="mb-0">Recent Announcements</h5>
                            </div>
                            <div class="card-body p-0">
                                <?php if (empty($recent_announcements)): ?>
                                    <div class="text-center py-5">
                                        <p class="mb-0">No announcements have been sent yet.</p>
                                    </div>
                                <?php else: ?>
                                    <div class="list-group list-group-flush">
                                        <?php foreach ($recent_announcements as $announcement): ?>
                                            <div class="list-group-item">
                                                <div class="d-flex justify-content-between align-items-start">
                                                    <div class="flex-grow-1">
                                                        <h6 class="mb-1"><?php echo $announcement->title; ?></h6>
                                                        <div class="mb-2">
                                                            <?php echo get_status_badge($announcement->status ?? $announcement->calculated_status); ?>
                                                            <?php if (!empty($announcement->priority) && $announcement->priority !== 'normal'): ?>
                                                                <span class="badge badge-<?php echo $announcement->priority === 'urgent' ? 'danger' : 'info'; ?> ml-1">
                                                                    <?php echo ucfirst($announcement->priority); ?>
                                                                </span>
                                                            <?php endif; ?>
                                                            <?php if (!empty($announcement->is_pinned)): ?>
                                                                <span class="badge badge-warning ml-1">
                                                                    <i class="fas fa-thumbtack"></i> Pinned
                                                                </span>
                                                            <?php endif; ?>
                                                        </div>
                                                    </div>
                                                    <small class="text-muted"><?php echo format_date($announcement->created_at); ?></small>
                                                </div>
                                                <p class="mb-1"><?php echo $announcement->message; ?></p>

                                                <!-- Scheduling Information -->
                                                <?php if (!empty($announcement->scheduled_publish_at) || !empty($announcement->expires_at)): ?>
                                                <div class="scheduling-info mb-2 p-2 bg-light rounded">
                                                    <small class="text-muted">
                                                        <i class="fas fa-clock"></i>
                                                        <?php if (!empty($announcement->scheduled_publish_at)): ?>
                                                            <strong>Scheduled:</strong> <?php echo format_admin_datetime($announcement->scheduled_publish_at); ?>
                                                        <?php endif; ?>
                                                        <?php if (!empty($announcement->expires_at)): ?>
                                                            <?php if (!empty($announcement->scheduled_publish_at)): ?> | <?php endif; ?>
                                                            <strong>Expires:</strong> <?php echo format_admin_datetime($announcement->expires_at); ?>
                                                        <?php endif; ?>
                                                    </small>
                                                </div>
                                                <?php endif; ?>

                                                <?php if (!empty($announcement->image_file)): ?>
                                                <div class="mt-2 mb-2">
                                                    <?php
                                                    $image_path = "../uploads/announcements/{$announcement->image_file}";
                                                    $full_image_path = dirname(__DIR__) . '/uploads/announcements/' . $announcement->image_file;
                                                    ?>
                                                    <p class="small text-muted mb-1">Image: <?php echo $announcement->image_file; ?></p>
                                                    <img src="<?php echo $image_path; ?>" class="img-fluid rounded" style="max-height: 200px;" alt="Announcement image">
                                                </div>
                                                <?php endif; ?>

                                                <?php if (!empty($announcement->video_url)): ?>
                                                <div class="mt-2 mb-2">
                                                    <p class="small text-muted mb-1">Video URL: <?php echo $announcement->video_url; ?></p>
                                                    <?php echo generate_video_embed($announcement->video_url, $announcement->title); ?>
                                                </div>
                                                <?php endif; ?>

                                                <?php if (!empty($announcement->video_file)): ?>
                                                <div class="mt-2 mb-2">
                                                    <?php
                                                    $video_path = "../uploads/announcements/{$announcement->video_file}";
                                                    $full_video_path = dirname(__DIR__) . '/uploads/announcements/' . $announcement->video_file;
                                                    ?>
                                                    <p class="small text-muted mb-1">Video File: <?php echo $announcement->video_file; ?></p>
                                                    <video controls class="img-fluid rounded" style="max-height: 200px;">
                                                        <source src="<?php echo $video_path; ?>" type="video/mp4">
                                                        Your browser does not support the video tag.
                                                    </video>
                                                </div>
                                                <?php endif; ?>
                                                <small>
                                                    Sent by: <?php echo $announcement->admin_name; ?> |
                                                    Recipients: <?php echo $announcement->recipients_count; ?> |
                                                    Type: <?php
                                                        switch ($announcement->recipient_type) {
                                                            case 'all':
                                                                echo 'All Users';
                                                                break;
                                                            case 'in_queue':
                                                                echo 'Users in Queue';
                                                                break;
                                                            case 'with_tokens':
                                                                echo 'Users with Tokens';
                                                                break;
                                                            case 'custom':
                                                                echo 'Custom Selection';
                                                                break;
                                                            default:
                                                                echo $announcement->recipient_type;
                                                        }
                                                    ?>
                                                </small>
                                                <div class="mt-2">
                                                    <a href="edit_announcement.php?id=<?php echo $announcement->id; ?>" class="btn btn-sm btn-outline-primary">
                                                        <i class="fas fa-edit"></i> Edit
                                                    </a>
                                                    <button type="button" class="btn btn-sm btn-outline-danger delete-announcement"
                                                            data-id="<?php echo $announcement->id; ?>"
                                                            data-title="<?php echo htmlspecialchars($announcement->title); ?>">
                                                        <i class="fas fa-trash"></i> Delete
                                                    </button>
                                                </div>
                                            </div>
                                        <?php endforeach; ?>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.5.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/popper.js@1.16.1/dist/umd/popper.min.js"></script>
    <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
    <script src="../assets/js/admin.js"></script>
    <script>
        $(document).ready(function() {
            // Initialize Select2
            $('.select2').select2({
                theme: 'bootstrap4',
                placeholder: 'Select users',
                allowClear: true
            });

            // Show/hide custom recipients based on selection
            $('#recipient_type').change(function() {
                if ($(this).val() === 'custom') {
                    $('#custom_recipients').show();
                } else {
                    $('#custom_recipients').hide();
                }
            });

            // Trigger change event on page load
            $('#recipient_type').trigger('change');

            // File input preview for image
            $('#image_file').on('change', function() {
                var fileName = $(this).val().split('\\').pop();
                $(this).next('.custom-file-label').html(fileName);

                // Preview image if it's an image file
                if (this.files && this.files[0]) {
                    var reader = new FileReader();
                    reader.onload = function(e) {
                        $('#image-preview').attr('src', e.target.result).show();
                    }
                    reader.readAsDataURL(this.files[0]);
                }
            });

            // File input label for video
            $('#video_file').on('change', function() {
                var fileName = $(this).val().split('\\').pop();
                $(this).next('.custom-file-label').html(fileName);
            });

            // Scheduling form interactions
            $('#publish_immediately').on('change', function() {
                if ($(this).is(':checked')) {
                    $('#scheduling-fields').hide();
                    $('#scheduled_publish_at').prop('required', false);
                } else {
                    $('#scheduling-fields').show();
                    $('#scheduled_publish_at').prop('required', true);
                }
            });

            // Set minimum datetime for scheduling fields
            var now = new Date();
            var year = now.getFullYear();
            var month = String(now.getMonth() + 1).padStart(2, '0');
            var day = String(now.getDate()).padStart(2, '0');
            var hours = String(now.getHours()).padStart(2, '0');
            var minutes = String(now.getMinutes()).padStart(2, '0');
            var currentDateTime = year + '-' + month + '-' + day + 'T' + hours + ':' + minutes;

            $('#scheduled_publish_at').attr('min', currentDateTime);
            $('#expires_at').attr('min', currentDateTime);

            // Update expiration minimum when publication time changes
            $('#scheduled_publish_at').on('change', function() {
                var publishTime = $(this).val();
                if (publishTime) {
                    $('#expires_at').attr('min', publishTime);
                }
            });

            // Video URL validation and preview
            $('#video_url').on('input', function() {
                var url = $(this).val().trim();
                var previewContainer = $('#video-preview');

                if (url === '') {
                    previewContainer.hide().empty();
                    return;
                }

                // Validate video URL
                var youtubeRegex = /(?:youtube\.com\/watch\?v=|youtu\.be\/|youtube\.com\/embed\/)([a-zA-Z0-9_-]{11})/;
                var vimeoRegex = /vimeo\.com\/(\d+)/;

                var youtubeMatch = url.match(youtubeRegex);
                var vimeoMatch = url.match(vimeoRegex);

                if (youtubeMatch) {
                    var videoId = youtubeMatch[1];
                    var embedUrl = 'https://www.youtube.com/embed/' + videoId;
                    showVideoPreview(embedUrl, 'YouTube Video');
                } else if (vimeoMatch) {
                    var videoId = vimeoMatch[1];
                    var embedUrl = 'https://player.vimeo.com/video/' + videoId;
                    showVideoPreview(embedUrl, 'Vimeo Video');
                } else if (url !== '') {
                    previewContainer.html('<div class="alert alert-warning"><i class="fas fa-exclamation-triangle"></i> Invalid video URL. Please provide a valid YouTube or Vimeo URL.</div>').show();
                }
            });

            function showVideoPreview(embedUrl, title) {
                var previewHtml = `
                    <div class="video-preview-card">
                        <div class="embed-responsive embed-responsive-16by9">
                            <iframe class="embed-responsive-item"
                                    src="${embedUrl}"
                                    title="${title}"
                                    frameborder="0"
                                    allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                                    allowfullscreen>
                            </iframe>
                        </div>
                        <small class="text-muted mt-2 d-block">${title} Preview</small>
                    </div>
                `;
                $('#video-preview').html(previewHtml).show();
            }

            // Link detection and preview in message
            var linkDetectionTimeout;
            $('#message').on('input', function() {
                clearTimeout(linkDetectionTimeout);
                linkDetectionTimeout = setTimeout(function() {
                    detectAndPreviewLinks();
                }, 1000); // Wait 1 second after user stops typing
            });

            function detectAndPreviewLinks() {
                var message = $('#message').val();
                var urlRegex = /https?:\/\/[^\s<>"']+/gi;
                var urls = message.match(urlRegex);

                if (urls && urls.length > 0) {
                    $('#link-preview-container').show();
                    $('#link-previews').html('<div class="text-muted"><i class="fas fa-spinner fa-spin"></i> Generating link previews...</div>');

                    // In a real implementation, you would make AJAX calls to fetch link previews
                    // For now, we'll show a placeholder
                    setTimeout(function() {
                        var previewsHtml = '';
                        urls.forEach(function(url, index) {
                            previewsHtml += `
                                <div class="link-preview-item mb-2 p-2 border rounded">
                                    <small class="text-muted">Link ${index + 1}:</small>
                                    <a href="${url}" target="_blank" class="d-block">${url}</a>
                                    <small class="text-info">Preview will be generated when announcement is sent</small>
                                </div>
                            `;
                        });
                        $('#link-previews').html(previewsHtml);
                    }, 1000);
                } else {
                    $('#link-preview-container').hide();
                }
            }

            // Delete announcement confirmation
            $('.delete-announcement').on('click', function() {
                var id = $(this).data('id');
                var title = $(this).data('title');

                // Create the modal HTML
                var modalHtml = `
                    <div class="modal fade" id="deleteModal" tabindex="-1" role="dialog" aria-labelledby="deleteModalLabel" aria-hidden="true">
                        <div class="modal-dialog" role="document">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h5 class="modal-title" id="deleteModalLabel">Delete Announcement</h5>
                                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                        <span aria-hidden="true">&times;</span>
                                    </button>
                                </div>
                                <div class="modal-body">
                                    <p>Are you sure you want to delete the announcement: <strong>${title}</strong>?</p>
                                    <div class="form-group">
                                        <div class="custom-control custom-checkbox">
                                            <input type="checkbox" class="custom-control-input" id="deleteNotifications">
                                            <label class="custom-control-label" for="deleteNotifications">Also delete related notifications</label>
                                            <small class="form-text text-muted">This will delete all notifications that were sent with this announcement</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="modal-footer">
                                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                                    <button type="button" class="btn btn-danger" id="confirmDelete">Delete</button>
                                </div>
                            </div>
                        </div>
                    </div>
                `;

                // Append modal to body
                $('body').append(modalHtml);

                // Show the modal
                $('#deleteModal').modal('show');

                // Handle delete confirmation
                $('#confirmDelete').on('click', function() {
                    var deleteNotifications = $('#deleteNotifications').is(':checked') ? 1 : 0;
                    window.location.href = 'delete_announcement.php?id=' + id + '&delete_notifications=' + deleteNotifications;
                });

                // Remove modal from DOM when hidden
                $('#deleteModal').on('hidden.bs.modal', function() {
                    $(this).remove();
                });
            });
        });
    </script>
</body>
</html>
