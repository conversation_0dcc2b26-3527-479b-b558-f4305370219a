<?php
/**
 * Email Functions
 *
 * This file contains functions for sending emails from the application.
 * Note: This is a simplified version that doesn't use PHPMailer.
 * In a production environment, you would want to use a proper email library.
 */

/**
 * Send an email using PHP's mail function
 *
 * @param string $to Recipient email address
 * @param string $subject Email subject
 * @param string $body Email body (HTML)
 * @param string $alt_body Plain text alternative body (not used in this simplified version)
 * @param array $attachments Optional array of attachments (not used in this simplified version)
 * @return bool True if email sent successfully, false otherwise
 */
function send_email($to, $subject, $body, $alt_body = '', $attachments = []) {
    // In a development environment, we'll just log the email instead of sending it
    error_log("Email would be sent to: $to");
    error_log("Subject: $subject");
    error_log("Body: $body");

    // For development purposes, always return true
    // In production, you would use a proper email library like PHPMailer
    return true;

    /*
    // This is how you would send an email using PHP's mail function
    // Uncomment this code if you want to actually send emails

    // Headers
    $headers = "MIME-Version: 1.0" . "\r\n";
    $headers .= "Content-type:text/html;charset=UTF-8" . "\r\n";
    $headers .= "From: " . SITE_NAME . " <" . SITE_EMAIL . ">" . "\r\n";

    // Send email
    return mail($to, $subject, $body, $headers);
    */
}

/**
 * Send verification email to a new user
 *
 * @param object $user User object
 * @param string $token Verification token
 * @param object $db Database connection
 * @return bool True if email sent successfully, false otherwise
 */
function send_verification_email($user, $token, $db) {
    // Generate verification URL
    $verification_url = SITE_URL . '/verify.php?email=' . urlencode($user->email) . '&token=' . urlencode($token);

    // Email subject
    $subject = 'Verify Your Email Address - ' . SITE_NAME;

    // Email body
    $body = '
    <html>
    <head>
        <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background-color: #4a89dc; color: white; padding: 10px 20px; text-align: center; }
            .content { padding: 20px; background-color: #f9f9f9; }
            .button { display: inline-block; padding: 10px 20px; background-color: #4a89dc; color: white; text-decoration: none; border-radius: 5px; }
            .footer { text-align: center; margin-top: 20px; font-size: 12px; color: #777; }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>' . SITE_NAME . '</h1>
            </div>
            <div class="content">
                <p>Hello ' . htmlspecialchars($user->name) . ',</p>
                <p>Thank you for registering with ' . SITE_NAME . '. To complete your registration, please verify your email address by clicking the button below:</p>
                <p style="text-align: center;">
                    <a href="' . $verification_url . '" class="button">Verify Email Address</a>
                </p>
                <p>If the button above doesn\'t work, you can also copy and paste the following link into your browser:</p>
                <p>' . $verification_url . '</p>
                <p>This verification link will expire in ' . (VERIFICATION_EXPIRY / 3600) . ' hours.</p>
                <p>If you did not create an account, please ignore this email.</p>
            </div>
            <div class="footer">
                <p>&copy; ' . date('Y') . ' ' . SITE_NAME . '. All rights reserved.</p>
            </div>
        </div>
    </body>
    </html>';

    // Plain text alternative
    $alt_body = "Hello " . $user->name . ",\n\n" .
                "Thank you for registering with " . SITE_NAME . ". To complete your registration, please verify your email address by visiting the following link:\n\n" .
                $verification_url . "\n\n" .
                "This verification link will expire in " . (VERIFICATION_EXPIRY / 3600) . " hours.\n\n" .
                "If you did not create an account, please ignore this email.\n\n" .
                "Regards,\n" . SITE_NAME;

    // Send the email
    return send_email($user->email, $subject, $body, $alt_body);
}

/**
 * Send password reset email
 *
 * @param object $user User object
 * @param string $token Reset token
 * @return bool True if email sent successfully, false otherwise
 */
function send_password_reset_email($user, $token) {
    // Generate reset URL
    $reset_url = SITE_URL . '/reset_password.php?email=' . urlencode($user->email) . '&token=' . urlencode($token);

    // Email subject
    $subject = 'Reset Your Password - ' . SITE_NAME;

    // Email body
    $body = '
    <html>
    <head>
        <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background-color: #4a89dc; color: white; padding: 10px 20px; text-align: center; }
            .content { padding: 20px; background-color: #f9f9f9; }
            .button { display: inline-block; padding: 10px 20px; background-color: #4a89dc; color: white; text-decoration: none; border-radius: 5px; }
            .footer { text-align: center; margin-top: 20px; font-size: 12px; color: #777; }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>' . SITE_NAME . '</h1>
            </div>
            <div class="content">
                <p>Hello ' . htmlspecialchars($user->name) . ',</p>
                <p>We received a request to reset your password. Click the button below to create a new password:</p>
                <p style="text-align: center;">
                    <a href="' . $reset_url . '" class="button">Reset Password</a>
                </p>
                <p>If the button above doesn\'t work, you can also copy and paste the following link into your browser:</p>
                <p>' . $reset_url . '</p>
                <p>This password reset link will expire in ' . (RESET_TOKEN_EXPIRY / 3600) . ' hours.</p>
                <p>If you did not request a password reset, please ignore this email or contact support if you have concerns.</p>
            </div>
            <div class="footer">
                <p>&copy; ' . date('Y') . ' ' . SITE_NAME . '. All rights reserved.</p>
            </div>
        </div>
    </body>
    </html>';

    // Plain text alternative
    $alt_body = "Hello " . $user->name . ",\n\n" .
                "We received a request to reset your password. Please visit the following link to create a new password:\n\n" .
                $reset_url . "\n\n" .
                "This password reset link will expire in " . (RESET_TOKEN_EXPIRY / 3600) . " hours.\n\n" .
                "If you did not request a password reset, please ignore this email or contact support if you have concerns.\n\n" .
                "Regards,\n" . SITE_NAME;

    // Send the email
    return send_email($user->email, $subject, $body, $alt_body);
}

/**
 * Generate a random token
 *
 * @param int $length Length of the token
 * @return string Random token
 */
function generate_token($length = 32) {
    return bin2hex(random_bytes($length / 2));
}
?>
