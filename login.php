<?php
// Define the root path to make includes work from any directory
define('ROOT_PATH', __DIR__ . DIRECTORY_SEPARATOR);

// Include configuration
require_once ROOT_PATH . 'config/config.php';
require_once ROOT_PATH . 'includes/functions.php';
require_once ROOT_PATH . 'database/db_connect.php';

// Start session
start_session();

// Check if user is already logged in
if (is_logged_in()) {
    redirect('dashboard.php');
}

// Initialize variables
$email = $password = '';
$email_err = $password_err = '';

// Process form data when form is submitted
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    // Create database instance
    $database = new Database();
    $db = $database->getConnection();

    // Validate email
    if (empty(trim($_POST['email']))) {
        $email_err = 'Please enter your email.';
    } else {
        $email = sanitize($_POST['email']);
    }

    // Validate password
    if (empty(trim($_POST['password']))) {
        $password_err = 'Please enter your password.';
    } else {
        $password = trim($_POST['password']);
    }

    // Check input errors before processing
    if (empty($email_err) && empty($password_err)) {
        // Prepare a select statement
        $query = "SELECT id, name, email, password, role, status, email_verified, verification_token FROM users WHERE email = :email";
        $stmt = $db->prepare($query);

        // Bind parameters and execute
        $stmt->bindParam(':email', $email);
        $stmt->execute();

        // Check if email exists
        if ($stmt->rowCount() == 1) {
            // Fetch user data
            $user = $stmt->fetch(PDO::FETCH_OBJ);

            // Check if account is pending verification
            if ($user->status == 'pending') {
                $password_err = 'Your email address has not been verified. Please check your email for the verification link or <a href="resend_verification.php">request a new verification email</a>.';
            }
            // Check if account is inactive for other reasons
            elseif ($user->status != 'active') {
                $password_err = 'Your account is not active. Please contact support.';
            } else {
                // Verify password
                if (password_verify($password, $user->password)) {
                    // Password is correct, start a new session
                    start_session();

                    // Store data in session variables
                    $_SESSION['user_id'] = $user->id;
                    $_SESSION['user_name'] = $user->name;
                    $_SESSION['user_email'] = $user->email;
                    $_SESSION['user_role'] = $user->role;

                    // Create login notification
                    create_notification($user->id, 'Login Successful', 'You have successfully logged in.', 'system', $database);

                    // Redirect user to dashboard
                    redirect('dashboard.php');
                } else {
                    // Password is not valid
                    $password_err = 'The password you entered is not valid.';
                }
            }
        } else {
            // Email doesn't exist
            $email_err = 'No account found with that email.';
        }
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login - <?php echo SITE_NAME; ?></title>

    <?php
    // Include SEO functions
    require_once 'includes/seo_helpers.php';

    // Set meta data for current page
    $meta = [
        'title' => 'Login - ' . SITE_NAME,
        'description' => 'Log in to your P2P Donate account to make pledges, receive donations, and manage your profile.',
        'canonical' => SITE_URL . '/login.php',
        'type' => 'website'
    ];

    // Output meta tags
    echo generate_meta_tags($meta);

    // Output favicon links
    echo generate_favicon_links();

    // Include analytics
    include_once 'includes/analytics.php';
    ?>

    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.1/css/all.min.css">
    <link rel="stylesheet" href="assets/css/style.css">
    <style>
        .card {
            border: none;
            border-radius: 10px;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .card-header {
            background: linear-gradient(135deg, var(--primary-color), #4299e1);
            padding: 1.5rem;
            border-bottom: none;
        }

        .card-body {
            padding: 1.5rem;
        }

        .form-control {
            border-radius: 5px;
            padding: 0.75rem 1rem;
            border: 1px solid #e2e8f0;
            background-color: #f8fafc;
        }

        .form-control:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.2rem rgba(66, 153, 225, 0.25);
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--primary-color), #4299e1);
            border: none;
            border-radius: 5px;
            padding: 0.75rem 1rem;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 7px 14px rgba(0, 0, 0, 0.1);
        }

        .btn-outline-primary {
            border-color: var(--primary-color);
            color: var(--primary-color);
            border-radius: 5px;
            transition: all 0.3s ease;
        }

        .btn-outline-primary:hover {
            background-color: var(--primary-color);
            color: white;
        }

        /* Facebook-style register button */
        .btn-success {
            background: linear-gradient(135deg, #42b72a, #36a420);
            border: none;
            border-radius: 5px;
            padding: 0.75rem 1rem;
            font-weight: 600;
            transition: all 0.3s ease;
            font-size: 1.05rem;
        }

        .btn-success:hover {
            background: linear-gradient(135deg, #36a420, #2b9217);
            transform: translateY(-2px);
            box-shadow: 0 7px 14px rgba(0, 0, 0, 0.1);
        }

        /* Add a divider between login and register */
        .divider {
            display: flex;
            align-items: center;
            text-align: center;
            margin: 1.5rem 0;
        }

        .divider::before,
        .divider::after {
            content: '';
            flex: 1;
            border-bottom: 1px solid #e2e8f0;
        }

        .divider span {
            padding: 0 1rem;
            color: #a0aec0;
            font-size: 0.875rem;
        }

        label {
            font-weight: 600;
            color: #4a5568;
        }

        .text-primary {
            color: var(--primary-color) !important;
        }

        .bg-light {
            background-color: #f7fafc !important;
        }
    </style>
</head>
<body class="bg-light">
    <div class="container">
        <div class="row justify-content-center mt-5">
            <div class="col-md-6">
                <div class="card shadow">
                    <div class="card-header bg-primary text-white text-center">
                        <h4><i class="fas fa-user-circle mr-2"></i><?php echo SITE_NAME; ?> - Login</h4>
                        <p class="mb-0 mt-2 text-white-50">Welcome back! Please login to your account</p>
                    </div>
                    <div class="card-body">
                        <?php echo flash_message('login_message'); ?>

                        <form action="<?php echo htmlspecialchars($_SERVER["PHP_SELF"]); ?>" method="post">
                            <div class="form-group">
                                <label><i class="fas fa-envelope text-primary mr-2"></i>Email</label>
                                <div class="input-group">
                                    <div class="input-group-prepend">
                                        <span class="input-group-text bg-light border-right-0"><i class="fas fa-at"></i></span>
                                    </div>
                                    <input type="email" name="email" class="form-control border-left-0 <?php echo (!empty($email_err)) ? 'is-invalid' : ''; ?>" value="<?php echo $email; ?>" placeholder="Enter your email">
                                </div>
                                <span class="invalid-feedback"><?php echo $email_err; ?></span>
                            </div>
                            <div class="form-group">
                                <label><i class="fas fa-lock text-primary mr-2"></i>Password</label>
                                <div class="input-group">
                                    <div class="input-group-prepend">
                                        <span class="input-group-text bg-light border-right-0"><i class="fas fa-key"></i></span>
                                    </div>
                                    <input type="password" name="password" id="password" class="form-control border-left-0 <?php echo (!empty($password_err)) ? 'is-invalid' : ''; ?>" placeholder="Enter your password">
                                    <div class="input-group-append">
                                        <span class="input-group-text bg-light border-left-0" onclick="togglePassword()">
                                            <i class="fas fa-eye" id="toggleIcon"></i>
                                        </span>
                                    </div>
                                </div>
                                <span class="invalid-feedback"><?php echo $password_err; ?></span>
                            </div>
                            <div class="form-group">
                                <button type="submit" class="btn btn-primary btn-block">
                                    <i class="fas fa-sign-in-alt mr-2"></i>Login
                                </button>
                            </div>
                            <div class="mt-4">
                                <div class="divider">
                                    <span>OR</span>
                                </div>
                                <div class="form-group">
                                    <a href="register.php" class="btn btn-success btn-block">
                                        <i class="fas fa-user-plus mr-2"></i>Create New Account
                                    </a>
                                </div>
                                <div class="text-center mt-4">
                                    <a href="forgot_password.php" class="text-primary">Forgot Password?</a>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.5.1.slim.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/popper.js@1.16.1/dist/umd/popper.min.js"></script>
    <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>

    <script>
        // Function to toggle password visibility
        function togglePassword() {
            const passwordField = document.getElementById('password');
            const toggleIcon = document.getElementById('toggleIcon');

            if (passwordField.type === 'password') {
                passwordField.type = 'text';
                toggleIcon.classList.remove('fa-eye');
                toggleIcon.classList.add('fa-eye-slash');
            } else {
                passwordField.type = 'password';
                toggleIcon.classList.remove('fa-eye-slash');
                toggleIcon.classList.add('fa-eye');
            }
        }

        // Add hover effect to the password toggle
        document.addEventListener('DOMContentLoaded', function() {
            const toggleElement = document.querySelector('.input-group-append .input-group-text');
            if (toggleElement) {
                toggleElement.style.cursor = 'pointer';
                toggleElement.addEventListener('mouseover', function() {
                    this.style.backgroundColor = '#e2e8f0';
                });
                toggleElement.addEventListener('mouseout', function() {
                    this.style.backgroundColor = '#f8fafc';
                });
            }
        });
    </script>
</body>
</html>
