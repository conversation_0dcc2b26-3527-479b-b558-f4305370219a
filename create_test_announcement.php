<?php
// Test script to create an announcement with video embedding
require_once 'config/config.php';
require_once 'includes/functions.php';
require_once 'includes/announcement_functions.php';
require_once 'database/db_connect.php';

// Start session
start_session();

// Check if user is logged in and is admin
if (!is_logged_in() || !is_admin()) {
    redirect('login.php');
}

$database = new Database();
$db = $database->getConnection();
$user_id = $_SESSION['user_id'];

$message = '';
$error = '';

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    try {
        $db->beginTransaction();
        
        // Test announcement data
        $title = 'Test Video Announcement';
        $message_text = 'This is a test announcement with a YouTube video to verify the video embedding functionality is working correctly.';
        $video_url = 'https://www.youtube.com/watch?v=dQw4w9WgXcQ';
        $priority = 'normal';
        $is_pinned = 0;
        $recipient_type = 'all';
        
        // Validate video URL
        $video_validation = validate_video_url($video_url);
        if (!$video_validation) {
            throw new Exception('Invalid video URL');
        }
        
        // Process link previews
        $content_data = process_announcement_content($message_text);
        $link_preview_data = null;
        if (!empty($content_data['previews'])) {
            $link_preview_data = json_encode($content_data['previews']);
        }
        
        // Get all active users
        $query = "SELECT id FROM users WHERE status = 'active'";
        $stmt = $db->prepare($query);
        $stmt->execute();
        $recipients = [];
        while ($user = $stmt->fetch(PDO::FETCH_OBJ)) {
            $recipients[] = $user->id;
        }
        
        // Create notifications for each recipient
        $count = 0;
        foreach ($recipients as $recipient_id) {
            create_notification($recipient_id, $title, $message_text, 'system', $db);
            $count++;
        }
        
        // Log the announcement
        $query = "INSERT INTO admin_announcements (admin_id, title, message, image_file, video_file, video_url, priority, link_preview_data, is_pinned, recipient_type, recipients_count)
                  VALUES (:admin_id, :title, :message, :image_file, :video_file, :video_url, :priority, :link_preview_data, :is_pinned, :recipient_type, :recipients_count)";
        $stmt = $db->prepare($query);
        $stmt->bindParam(':admin_id', $user_id);
        $stmt->bindParam(':title', $title);
        $stmt->bindParam(':message', $message_text);
        $stmt->bindValue(':image_file', null);
        $stmt->bindValue(':video_file', null);
        $stmt->bindParam(':video_url', $video_url);
        $stmt->bindParam(':priority', $priority);
        $stmt->bindParam(':link_preview_data', $link_preview_data);
        $stmt->bindParam(':is_pinned', $is_pinned);
        $stmt->bindParam(':recipient_type', $recipient_type);
        $stmt->bindParam(':recipients_count', $count);
        $stmt->execute();
        
        $db->commit();
        
        $message = "Test announcement created successfully! Sent to $count users.";
        
    } catch (Exception $e) {
        $db->rollBack();
        $error = 'Error creating test announcement: ' . $e->getMessage();
    }
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Create Test Announcement - P2P Donate</title>
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.1/css/all.min.css">
</head>
<body>
    <div class="container mt-4">
        <div class="row">
            <div class="col-md-8 mx-auto">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">Create Test Video Announcement</h5>
                    </div>
                    <div class="card-body">
                        <?php if ($message): ?>
                            <div class="alert alert-success">
                                <i class="fas fa-check"></i> <?php echo $message; ?>
                            </div>
                        <?php endif; ?>
                        
                        <?php if ($error): ?>
                            <div class="alert alert-danger">
                                <i class="fas fa-times"></i> <?php echo $error; ?>
                            </div>
                        <?php endif; ?>
                        
                        <p>This will create a test announcement with the following details:</p>
                        <ul>
                            <li><strong>Title:</strong> Test Video Announcement</li>
                            <li><strong>Message:</strong> This is a test announcement with a YouTube video...</li>
                            <li><strong>Video URL:</strong> https://www.youtube.com/watch?v=dQw4w9WgXcQ</li>
                            <li><strong>Priority:</strong> Normal</li>
                            <li><strong>Recipients:</strong> All active users</li>
                        </ul>
                        
                        <form method="post">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-plus"></i> Create Test Announcement
                            </button>
                        </form>
                        
                        <div class="mt-3">
                            <a href="debug_video_embedding.php" class="btn btn-info">
                                <i class="fas fa-bug"></i> Debug Video Embedding
                            </a>
                            <a href="dashboard.php" class="btn btn-secondary">
                                <i class="fas fa-tachometer-alt"></i> View Dashboard
                            </a>
                            <a href="admin/announcements.php" class="btn btn-success">
                                <i class="fas fa-cog"></i> Admin Panel
                            </a>
                        </div>
                    </div>
                </div>
                
                <!-- Preview of what the announcement will look like -->
                <div class="card mt-4">
                    <div class="card-header">
                        <h5 class="mb-0">Preview</h5>
                    </div>
                    <div class="card-body">
                        <h6>Test Video Announcement</h6>
                        <p>This is a test announcement with a YouTube video to verify the video embedding functionality is working correctly.</p>
                        
                        <div class="mt-3">
                            <h6>Video Embed Preview:</h6>
                            <?php
                            $test_video_url = 'https://www.youtube.com/watch?v=dQw4w9WgXcQ';
                            echo generate_video_embed($test_video_url, 'Test Video');
                            ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
