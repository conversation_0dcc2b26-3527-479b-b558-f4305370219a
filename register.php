<?php
// Include configuration
require_once 'config/config.php';
require_once 'includes/functions.php';
require_once 'database/db_connect.php';
require_once 'includes/email_functions.php';

// Start session
start_session();

// Create database instance for global use
$db_global = new Database();

// Check if user is already logged in
if (is_logged_in()) {
    redirect('dashboard.php');
}

// Initialize variables
$name = $email = $password = $confirm_password = $mobile_number = $mobile_name = $referral_code = '';
$name_err = $email_err = $password_err = $confirm_password_err = $mobile_number_err = $mobile_name_err = $referral_code_err = $terms_err = '';

// Check if referral code is provided in URL
if (isset($_GET['ref']) && !empty($_GET['ref'])) {
    $referral_code = sanitize($_GET['ref']);

    // Verify referral code exists
    $referrer = get_referrer_by_code($referral_code, $db_global);
    if (!$referrer) {
        $referral_code = '';
        $referral_code_err = 'Invalid referral code.';
    }
}

// Process form data when form is submitted
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    // Create database instance
    $database = new Database();
    $db = $database->getConnection();

    // Validate name
    if (empty(trim($_POST['name']))) {
        $name_err = 'Please enter your name.';
    } else {
        $name = sanitize($_POST['name']);
    }

    // Validate email
    if (empty(trim($_POST['email']))) {
        $email_err = 'Please enter your email.';
    } else {
        // Prepare a select statement
        $email = sanitize($_POST['email']);
        $query = "SELECT id FROM users WHERE email = :email";
        $stmt = $db->prepare($query);

        // Bind parameters and execute
        $stmt->bindParam(':email', $email);
        $stmt->execute();

        // Check if email already exists
        if ($stmt->rowCount() > 0) {
            $email_err = 'This email is already taken.';
        }
    }

    // Validate password
    if (empty(trim($_POST['password']))) {
        $password_err = 'Please enter a password.';
    } elseif (strlen(trim($_POST['password'])) < 6) {
        $password_err = 'Password must have at least 6 characters.';
    } else {
        $password = trim($_POST['password']);
    }

    // Validate confirm password
    if (empty(trim($_POST['confirm_password']))) {
        $confirm_password_err = 'Please confirm password.';
    } else {
        $confirm_password = trim($_POST['confirm_password']);
        if ($password != $confirm_password) {
            $confirm_password_err = 'Password did not match.';
        }
    }

    // Validate mobile number (required)
    if (empty(trim($_POST['mobile_number']))) {
        $mobile_number_err = 'Please enter your mobile money number.';
    } else {
        $mobile_number = sanitize($_POST['mobile_number']);
    }

    // Validate mobile name (required)
    if (empty(trim($_POST['mobile_name']))) {
        $mobile_name_err = 'Please enter your mobile money name.';
    } else {
        $mobile_name = sanitize($_POST['mobile_name']);
    }

    // Validate referral code (optional)
    $referred_by = null;
    if (!empty(trim($_POST['referral_code']))) {
        $referral_code = sanitize($_POST['referral_code']);

        // Check if referral code exists
        $referrer = get_referrer_by_code($referral_code, $db);

        if (!$referrer) {
            $referral_code_err = 'Invalid referral code.';
        } else {
            $referred_by = $referrer->id;
        }
    }

    // Validate terms agreement (required)
    if (!isset($_POST['terms_agreement'])) {
        $terms_err = 'You must agree to the Terms of Service and Privacy Policy to register.';
    }

    // Check input errors before inserting into database
    if (empty($name_err) && empty($email_err) && empty($password_err) && empty($confirm_password_err) &&
        empty($mobile_number_err) && empty($mobile_name_err) && empty($referral_code_err) && empty($terms_err)) {
        // Start transaction
        $db->beginTransaction();

        try {
            // Generate unique referral code for new user
            $new_referral_code = generate_unique_referral_code($db);

            // Generate verification token and expiry date
            $verification_token = generate_token();
            $verification_expires = date('Y-m-d H:i:s', time() + VERIFICATION_EXPIRY);

            // Set status to 'pending' until email is verified
            $status = 'pending';

            // Prepare an insert statement
            $query = "INSERT INTO users (name, email, password, mobile_number, mobile_name, referral_code, referred_by,
                      status, verification_token, verification_expires)
                      VALUES (:name, :email, :password, :mobile_number, :mobile_name, :referral_code, :referred_by,
                      :status, :verification_token, :verification_expires)";
            $stmt = $db->prepare($query);

            // Hash the password
            $hashed_password = password_hash($password, PASSWORD_DEFAULT);

            // Bind parameters
            $stmt->bindParam(':name', $name);
            $stmt->bindParam(':email', $email);
            $stmt->bindParam(':password', $hashed_password);
            $stmt->bindParam(':mobile_number', $mobile_number);
            $stmt->bindParam(':mobile_name', $mobile_name);
            $stmt->bindParam(':referral_code', $new_referral_code);
            $stmt->bindParam(':referred_by', $referred_by);
            $stmt->bindParam(':status', $status);
            $stmt->bindParam(':verification_token', $verification_token);
            $stmt->bindParam(':verification_expires', $verification_expires);

            // Execute the statement
            $stmt->execute();
            $new_user_id = $db->lastInsertId();

            // If user was referred, create referral record
            if ($referred_by) {
                create_referral($referred_by, $new_user_id, $db);

                // Create notification for referrer
                create_notification($referred_by, 'New Referral', 'You have a new referral: ' . $name . '. You will receive 10 bonus tokens when they make their first token purchase.', 'system', $db);
            }

            // Get the new user object for email sending
            $query = "SELECT * FROM users WHERE id = :id";
            $stmt = $db->prepare($query);
            $stmt->bindParam(':id', $new_user_id);
            $stmt->execute();
            $new_user = $stmt->fetch(PDO::FETCH_OBJ);

            // Send verification email
            $email_sent = send_verification_email($new_user, $verification_token, $db);

            // Commit transaction
            $db->commit();

            // Set flash message
            if ($email_sent) {
                flash_message('login_message', 'Registration successful! Please check your email to verify your account.', 'alert alert-success');
            } else {
                flash_message('login_message', 'Registration successful! However, we could not send the verification email. Please contact support.', 'alert alert-warning');
            }

            // Redirect to login page
            redirect('login.php');
        } catch (Exception $e) {
            // Rollback transaction
            $db->rollBack();

            echo "Something went wrong. Please try again later. Error: " . $e->getMessage();
        }
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Register - <?php echo SITE_NAME; ?></title>

    <?php
    // Include SEO functions
    require_once 'includes/seo_helpers.php';

    // Set meta data for current page
    $meta = [
        'title' => 'Register - ' . SITE_NAME,
        'description' => 'Create a new account on P2P Donate. Join our peer-to-peer donation platform and start making and receiving pledges in Ghana.',
        'canonical' => SITE_URL . '/register.php',
        'type' => 'website'
    ];

    // Output meta tags
    echo generate_meta_tags($meta);

    // Output favicon links
    echo generate_favicon_links();

    // Include analytics
    include_once 'includes/analytics.php';
    ?>

    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.1/css/all.min.css">
    <link rel="stylesheet" href="assets/css/style.css">
    <style>
        .card {
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        .btn-success {
            background-color: #28a745;
        }

        .divider {
            display: flex;
            align-items: center;
            text-align: center;
            margin: 1rem 0;
        }

        .divider::before,
        .divider::after {
            content: '';
            flex: 1;
            border-bottom: 1px solid #dee2e6;
        }

        .divider span {
            padding: 0 1rem;
            color: #6c757d;
        }
    </style>
</head>
<body class="bg-light">
    <div class="container">
        <div class="row justify-content-center mt-5">
            <div class="col-md-6">
                <div class="card shadow">
                    <div class="card-header bg-primary text-white text-center">
                        <h4><i class="fas fa-user-plus mr-2"></i><?php echo SITE_NAME; ?> - Register</h4>
                    </div>
                    <div class="card-body">
                        <form action="<?php echo htmlspecialchars($_SERVER["PHP_SELF"]); ?>" method="post">
                            <div class="form-group">
                                <label><i class="fas fa-user mr-2"></i>Full Name</label>
                                <input type="text" name="name" class="form-control <?php echo (!empty($name_err)) ? 'is-invalid' : ''; ?>" value="<?php echo $name; ?>" placeholder="Enter your full name">
                                <span class="invalid-feedback"><?php echo $name_err; ?></span>
                            </div>
                            <div class="form-group">
                                <label><i class="fas fa-envelope mr-2"></i>Email</label>
                                <input type="email" name="email" class="form-control <?php echo (!empty($email_err)) ? 'is-invalid' : ''; ?>" value="<?php echo $email; ?>" placeholder="Enter your email address">
                                <span class="invalid-feedback"><?php echo $email_err; ?></span>
                            </div>
                            <div class="form-group">
                                <label><i class="fas fa-lock mr-2"></i>Password</label>
                                <input type="password" name="password" id="password" class="form-control <?php echo (!empty($password_err)) ? 'is-invalid' : ''; ?>" placeholder="Create a password">
                                <span class="invalid-feedback"><?php echo $password_err; ?></span>
                                <small class="form-text text-muted">Password must be at least 6 characters long</small>
                            </div>
                            <div class="form-group">
                                <label><i class="fas fa-lock mr-2"></i>Confirm Password</label>
                                <input type="password" name="confirm_password" id="confirm_password" class="form-control <?php echo (!empty($confirm_password_err)) ? 'is-invalid' : ''; ?>" placeholder="Confirm your password">
                                <span class="invalid-feedback"><?php echo $confirm_password_err; ?></span>
                            </div>
                            <div class="form-group">
                                <label><i class="fas fa-mobile-alt mr-2"></i>Mobile Money Number</label>
                                <input type="text" name="mobile_number" class="form-control <?php echo (!empty($mobile_number_err)) ? 'is-invalid' : ''; ?>" value="<?php echo $mobile_number; ?>" placeholder="Enter your mobile money number">
                                <span class="invalid-feedback"><?php echo $mobile_number_err; ?></span>
                            </div>
                            <div class="form-group">
                                <label><i class="fas fa-user-tag mr-2"></i>Mobile Money Name</label>
                                <input type="text" name="mobile_name" class="form-control <?php echo (!empty($mobile_name_err)) ? 'is-invalid' : ''; ?>" value="<?php echo $mobile_name; ?>" placeholder="Enter the name on your mobile money account">
                                <span class="invalid-feedback"><?php echo $mobile_name_err; ?></span>
                            </div>
                            <div class="form-group">
                                <label><i class="fas fa-users mr-2"></i>Referral Code (Optional)</label>
                                <input type="text" name="referral_code" class="form-control <?php echo (!empty($referral_code_err)) ? 'is-invalid' : ''; ?>" value="<?php echo $referral_code; ?>" placeholder="Enter referral code (if any)">
                                <span class="invalid-feedback"><?php echo $referral_code_err; ?></span>
                                <small class="form-text text-muted">If you were referred by someone, enter their referral code here.</small>
                            </div>
                            <div class="form-group">
                                <div class="custom-control custom-checkbox">
                                    <input type="checkbox" class="custom-control-input <?php echo (!empty($terms_err)) ? 'is-invalid' : ''; ?>" id="terms_agreement" name="terms_agreement">
                                    <label class="custom-control-label" for="terms_agreement">
                                        I agree to the <a href="terms.php" target="_blank">Terms of Service</a> and <a href="privacy.php" target="_blank">Privacy Policy</a>
                                    </label>
                                    <div class="invalid-feedback"><?php echo $terms_err; ?></div>
                                </div>
                            </div>
                            <div class="form-group">
                                <button type="submit" class="btn btn-success btn-block">
                                    <i class="fas fa-user-plus mr-2"></i>Create Account
                                </button>
                            </div>
                            <div class="divider">
                                <span>OR</span>
                            </div>
                            <div class="form-group">
                                <a href="login.php" class="btn btn-primary btn-block">
                                    <i class="fas fa-sign-in-alt mr-2"></i>Login
                                </a>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.5.1.slim.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/popper.js@1.16.1/dist/umd/popper.min.js"></script>
    <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>

    <script>
        // Simple function to toggle password visibility
        function togglePassword(fieldId, iconId) {
            const passwordField = document.getElementById(fieldId);
            const toggleIcon = document.getElementById(iconId);

            if (passwordField.type === 'password') {
                passwordField.type = 'text';
                toggleIcon.classList.remove('fa-eye');
                toggleIcon.classList.add('fa-eye-slash');
            } else {
                passwordField.type = 'password';
                toggleIcon.classList.remove('fa-eye-slash');
                toggleIcon.classList.add('fa-eye');
            }
        }
    </script>
</body>
</html>
