<?php
// Include configuration
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../database/db_connect.php';

// Create database instance
$database = new Database();
$db = $database->getConnection();

echo "Creating support_tickets table...\n";

// Check if the table already exists
$query = "SHOW TABLES LIKE 'support_tickets'";
$stmt = $db->prepare($query);
$stmt->execute();
$table_exists = $stmt->rowCount() > 0;

if ($table_exists) {
    echo "support_tickets table already exists.\n";
} else {
    // Create the support_tickets table
    $query = "CREATE TABLE IF NOT EXISTS support_tickets (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        ticket_number VARCHAR(20) NOT NULL UNIQUE,
        category VARCHAR(50) NOT NULL,
        subject VARCHAR(100) NOT NULL,
        message TEXT NOT NULL,
        attachment VARCHAR(255),
        status ENUM('open', 'in_progress', 'closed') DEFAULT 'open',
        admin_response TEXT,
        admin_id INT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
        FOREIGN KEY (admin_id) REFERENCES users(id) ON DELETE SET NULL
    )";

    $stmt = $db->prepare($query);

    if ($stmt->execute()) {
        echo "support_tickets table created successfully.\n";
    } else {
        echo "Error creating support_tickets table: " . print_r($stmt->errorInfo(), true) . "\n";
    }
}

// Check if support_ticket_replies table exists
$query = "SHOW TABLES LIKE 'support_ticket_replies'";
$stmt = $db->prepare($query);
$stmt->execute();
$replies_table_exists = $stmt->rowCount() > 0;

if ($replies_table_exists) {
    echo "support_ticket_replies table already exists.\n";
} else {
    // Create the support_ticket_replies table
    $query = "CREATE TABLE IF NOT EXISTS support_ticket_replies (
        id INT AUTO_INCREMENT PRIMARY KEY,
        ticket_id INT NOT NULL,
        user_id INT NOT NULL,
        message TEXT NOT NULL,
        attachment VARCHAR(255),
        is_admin BOOLEAN DEFAULT FALSE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (ticket_id) REFERENCES support_tickets(id) ON DELETE CASCADE,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
    )";

    $stmt = $db->prepare($query);

    if ($stmt->execute()) {
        echo "support_ticket_replies table created successfully.\n";
    } else {
        echo "Error creating support_ticket_replies table: " . print_r($stmt->errorInfo(), true) . "\n";
    }
}

// Check if we need to add support_tickets type to notifications table
$query = "SHOW COLUMNS FROM notifications LIKE 'type'";
$stmt = $db->prepare($query);
$stmt->execute();
$column = $stmt->fetch(PDO::FETCH_ASSOC);

if ($column) {
    $type_definition = $column['Type'];

    // Check if 'support' is already in the enum
    if (strpos($type_definition, "'support'") === false) {
        echo "Adding 'support' to notifications type enum...\n";

        // Extract current enum values
        preg_match("/^enum\((.*)\)$/", $type_definition, $matches);
        $values = $matches[1];

        // Add 'support' to the enum
        $new_values = $values . ",'support'";

        // Alter the table
        $query = "ALTER TABLE notifications MODIFY COLUMN type ENUM($new_values) NOT NULL";
        $stmt = $db->prepare($query);

        if ($stmt->execute()) {
            echo "Added 'support' to notifications type enum successfully.\n";
        } else {
            echo "Error adding 'support' to notifications type enum: " . print_r($stmt->errorInfo(), true) . "\n";
        }
    } else {
        echo "'support' already exists in notifications type enum.\n";
    }
}

echo "Database update completed.\n";
?>
