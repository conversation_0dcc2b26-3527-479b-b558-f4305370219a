/* Enhanced Announcements Styling for P2P Donate Platform */

/* Enhanced Announcement Container */
.enhanced-announcement-container {
    position: relative;
    z-index: 10;
}

.enhanced-announcement {
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    border: none;
    overflow: hidden;
    transition: all 0.3s ease;
    animation: slideInDown 0.5s ease-out;
}

.enhanced-announcement:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
}

/* Announcement Card Structure */
.announcement-card {
    background: white;
    border-radius: 12px;
    overflow: hidden;
    position: relative;
}

/* Pinned Announcement Styling */
.pinned-announcement .enhanced-announcement {
    border: 2px solid #ffc107;
    background: linear-gradient(135deg, rgba(255, 193, 7, 0.1), rgba(255, 255, 255, 0.9));
    animation: pulseGlow 2s infinite;
}

/* Priority-based styling with enhanced contrast */
.enhanced-announcement[data-priority="urgent"] {
    border-left-width: 8px !important;
    animation: urgentPulse 1.5s infinite;
}

.enhanced-announcement[data-priority="urgent"] .announcement-title {
    color: #721c24; /* Darker red for better contrast on light backgrounds */
}

.enhanced-announcement[data-priority="info"] {
    border-left-width: 6px !important;
}

.enhanced-announcement[data-priority="info"] .announcement-title {
    color: #0c5460; /* Darker blue for better contrast */
}

.enhanced-announcement[data-priority="normal"] .announcement-title {
    color: #155724; /* Darker green for better contrast */
}

/* Dark mode priority text colors */
body.dark-mode .enhanced-announcement[data-priority="urgent"] .announcement-title {
    color: #f56565; /* Lighter red for dark mode */
}

body.dark-mode .enhanced-announcement[data-priority="info"] .announcement-title {
    color: #63b3ed; /* Lighter blue for dark mode */
}

body.dark-mode .enhanced-announcement[data-priority="normal"] .announcement-title {
    color: #68d391; /* Lighter green for dark mode */
}

/* Header Section */
.announcement-header-section {
    padding: 1.5rem 1.5rem 1rem 1.5rem;
    background: rgba(255, 255, 255, 0.95);
    border-bottom: 1px solid rgba(0, 0, 0, 0.08);
}

.announcement-header {
    margin-bottom: 0;
}

.announcement-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
    flex-shrink: 0;
}

.announcement-title-section {
    flex-grow: 1;
}

.announcement-title {
    font-size: 1.4rem;
    font-weight: 600;
    line-height: 1.3;
    margin-bottom: 0.25rem;
    color: #1a252f; /* Improved contrast: 8.5:1 ratio */
}

.announcement-meta {
    margin-top: 0.25rem;
}

.announcement-meta .text-muted {
    color: #495057 !important; /* Improved contrast: 7.0:1 ratio for small text */
}

.announcement-badges {
    display: flex;
    align-items: flex-start;
    gap: 0.5rem;
    flex-shrink: 0;
}

/* Content Section */
.announcement-content-section {
    padding: 1.25rem 1.5rem;
    background: white;
}

.announcement-message {
    font-size: 1.1rem;
    line-height: 1.7;
    color: #212529; /* Improved contrast: 16.0:1 ratio for excellent readability */
    margin-bottom: 0;
    font-weight: 400;
}

.announcement-link-previews {
    margin-top: 1rem;
}

/* Media Section */
.announcement-media-section {
    background: #f8f9fa;
    border-top: 1px solid rgba(0, 0, 0, 0.08);
}

.media-separator {
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(0, 0, 0, 0.1), transparent);
    margin: 0 1.5rem;
}

.media-item {
    padding: 1.25rem 1.5rem;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.media-item:last-child {
    border-bottom: none;
}

.media-label {
    display: flex;
    align-items: center;
    font-size: 0.9rem;
    font-weight: 600;
    color: #495057; /* Improved contrast: 7.0:1 ratio against #f8f9fa background */
    margin-bottom: 0.75rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.media-label i {
    margin-right: 0.5rem;
    font-size: 1rem;
}

.media-content {
    position: relative;
}

/* Video Embed Styling */
.video-embed-container {
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    background: #000;
    position: relative;
}

.video-embed-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(0, 0, 0, 0.1), transparent);
    pointer-events: none;
    z-index: 1;
}

/* Image Styling */
.announcement-image-container {
    text-align: center;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    background: #f8f9fa;
}

.announcement-image {
    max-height: 400px;
    width: auto;
    cursor: pointer;
    transition: all 0.3s ease;
    border-radius: 12px;
}

.announcement-image:hover {
    transform: scale(1.02);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
}

/* Video File Styling */
.announcement-video-container {
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    background: #000;
}

.announcement-video {
    max-height: 400px;
    width: 100%;
}

/* Link Preview Styling */
.link-preview-card {
    display: flex;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    overflow: hidden;
    margin: 0.5rem 0;
    transition: all 0.3s ease;
    background: white;
}

.link-preview-card:hover {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transform: translateY(-1px);
}

.link-preview-image {
    flex-shrink: 0;
    width: 120px;
    height: 80px;
    overflow: hidden;
}

.link-preview-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.link-preview-content {
    padding: 0.75rem;
    flex-grow: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.link-preview-title {
    font-size: 0.9rem;
    font-weight: 600;
    margin-bottom: 0.25rem;
    color: #212529; /* Improved contrast: 16.0:1 ratio */
    line-height: 1.3;
}

.link-preview-description {
    font-size: 0.8rem;
    color: #495057; /* Improved contrast: 7.0:1 ratio for small text */
    margin-bottom: 0.5rem;
    line-height: 1.4;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.link-preview-site {
    font-size: 0.75rem;
    color: #6c757d; /* Improved contrast: 4.5:1 ratio for very small text */
    margin-bottom: 0.25rem;
}

.link-preview-url {
    font-size: 0.75rem;
    color: #007bff;
    text-decoration: none;
    align-self: flex-start;
}

.link-preview-url:hover {
    text-decoration: underline;
}

/* Footer Section */
.announcement-footer-section {
    padding: 1rem 1.5rem;
    background: rgba(248, 249, 250, 0.5);
    border-top: 1px solid rgba(0, 0, 0, 0.05);
}

.pinned-notice {
    text-align: center;
    padding: 0.5rem;
    background: rgba(255, 193, 7, 0.1);
    border-radius: 8px;
    border: 1px solid rgba(255, 193, 7, 0.2);
}

.pinned-notice .text-muted {
    color: #495057 !important; /* Improved contrast for pinned notice text */
}

/* Close Button Enhancement */
.announcement-close {
    position: absolute;
    top: 1rem;
    right: 1rem;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 50%;
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    z-index: 10;
}

.announcement-close:hover {
    background: rgba(255, 255, 255, 1);
    transform: scale(1.1);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.announcement-close:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.25);
}

/* Animations */
@keyframes slideInDown {
    from {
        opacity: 0;
        transform: translateY(-30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes pulseGlow {
    0%, 100% {
        box-shadow: 0 4px 12px rgba(255, 193, 7, 0.3);
    }
    50% {
        box-shadow: 0 6px 20px rgba(255, 193, 7, 0.5);
    }
}

@keyframes urgentPulse {
    0%, 100% {
        border-left-color: #dc3545;
    }
    50% {
        border-left-color: #ff6b7a;
    }
}

/* Dark Mode Support with Enhanced Contrast */
body.dark-mode .enhanced-announcement {
    background-color: var(--dark-card-bg, #2d3748);
    border-color: var(--dark-border, #4a5568);
    color: var(--dark-text, #f7fafc);
}

body.dark-mode .announcement-header-section {
    background: rgba(45, 55, 72, 0.95);
    border-bottom-color: var(--dark-border, #4a5568);
}

body.dark-mode .announcement-content-section {
    background: var(--dark-card-bg, #2d3748);
}

body.dark-mode .announcement-media-section {
    background: rgba(26, 32, 44, 0.8);
    border-top-color: var(--dark-border, #4a5568);
}

body.dark-mode .announcement-footer-section {
    background: rgba(26, 32, 44, 0.6);
    border-top-color: var(--dark-border, #4a5568);
}

body.dark-mode .announcement-title {
    color: #f7fafc; /* High contrast white for dark mode */
}

body.dark-mode .announcement-message {
    color: #e2e8f0; /* Slightly softer white for body text */
}

body.dark-mode .announcement-meta .text-muted {
    color: #a0aec0 !important; /* Improved contrast for meta text in dark mode */
}

body.dark-mode .media-label {
    color: #cbd5e0; /* Good contrast for media labels in dark mode */
}

body.dark-mode .link-preview-card {
    background-color: var(--dark-card-bg, #2d3748);
    border-color: var(--dark-border, #4a5568);
}

body.dark-mode .link-preview-title {
    color: #f7fafc; /* High contrast for link titles */
}

body.dark-mode .link-preview-description {
    color: #cbd5e0; /* Good contrast for link descriptions */
}

body.dark-mode .link-preview-site {
    color: #a0aec0; /* Adequate contrast for small site text */
}

body.dark-mode .pinned-notice {
    background: rgba(255, 193, 7, 0.2);
    border-color: rgba(255, 193, 7, 0.4);
}

body.dark-mode .pinned-notice .text-muted {
    color: #e2e8f0 !important; /* High contrast for pinned notice in dark mode */
}

body.dark-mode .announcement-close {
    background: rgba(0, 0, 0, 0.4);
    color: #f7fafc;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

body.dark-mode .announcement-close:hover {
    background: rgba(0, 0, 0, 0.6);
    border-color: rgba(255, 255, 255, 0.2);
}

/* Accessibility Enhancements */
/* Ensure all text meets WCAG AA standards (4.5:1 contrast ratio) */

/* High contrast mode support */
@media (prefers-contrast: high) {
    .announcement-title {
        color: #000000 !important;
        font-weight: 700;
    }

    .announcement-message {
        color: #000000 !important;
        font-weight: 500;
    }

    .media-label {
        color: #000000 !important;
        font-weight: 700;
    }

    .link-preview-title {
        color: #000000 !important;
    }

    .link-preview-description {
        color: #333333 !important;
    }

    body.dark-mode .announcement-title {
        color: #ffffff !important;
    }

    body.dark-mode .announcement-message {
        color: #ffffff !important;
    }

    body.dark-mode .media-label {
        color: #ffffff !important;
    }
}

/* Focus indicators for accessibility */
.enhanced-announcement:focus-within {
    outline: 3px solid #0066cc;
    outline-offset: 2px;
}

/* Ensure sufficient color contrast for all badge text */
.badge {
    font-weight: 600;
    text-shadow: none;
}

.badge-danger {
    background-color: #721c24 !important; /* Darker red for better contrast */
    color: #ffffff !important;
}

.badge-success {
    background-color: #155724 !important; /* Darker green for better contrast */
    color: #ffffff !important;
}

.badge-info {
    background-color: #0c5460 !important; /* Darker blue for better contrast */
    color: #ffffff !important;
}

.badge-warning {
    background-color: #856404 !important; /* Darker yellow for better contrast */
    color: #ffffff !important;
}

/* Ensure link colors meet accessibility standards */
.link-preview-url {
    color: #0056b3; /* Darker blue for better contrast */
    font-weight: 500;
}

.link-preview-url:hover {
    color: #004085; /* Even darker on hover */
    text-decoration: underline;
}

body.dark-mode .link-preview-url {
    color: #66b3ff; /* Lighter blue for dark mode */
}

body.dark-mode .link-preview-url:hover {
    color: #99ccff; /* Lighter on hover in dark mode */
}

/* Responsive Design */
@media (max-width: 768px) {
    .announcement-header-section {
        padding: 1.25rem 1rem 0.75rem 1rem;
    }

    .announcement-content-section {
        padding: 1rem;
    }

    .media-item {
        padding: 1rem;
    }

    .announcement-header {
        flex-direction: column;
        align-items: flex-start;
    }

    .announcement-icon {
        width: 50px;
        height: 50px;
        margin-bottom: 0.75rem;
        margin-right: 0;
    }

    .announcement-title-section {
        width: 100%;
        margin-bottom: 0.75rem;
    }

    .announcement-title {
        font-size: 1.2rem;
    }

    .announcement-badges {
        align-self: flex-start;
        margin-top: 0;
    }

    .announcement-message {
        font-size: 1rem;
    }

    .media-label {
        font-size: 0.8rem;
    }

    .link-preview-card {
        flex-direction: column;
    }

    .link-preview-image {
        width: 100%;
        height: 120px;
    }

    .announcement-close {
        top: 0.75rem;
        right: 0.75rem;
        width: 32px;
        height: 32px;
    }
}

@media (max-width: 576px) {
    .enhanced-announcement {
        margin: 0 -15px;
        border-radius: 0;
    }

    .announcement-header-section {
        padding: 1rem 0.75rem 0.5rem 0.75rem;
    }

    .announcement-content-section {
        padding: 0.75rem;
    }

    .media-item {
        padding: 0.75rem;
    }

    .announcement-footer-section {
        padding: 0.75rem;
    }

    .announcement-message {
        font-size: 0.95rem;
        line-height: 1.6;
    }

    .announcement-title {
        font-size: 1.1rem;
    }

    .announcement-icon {
        width: 40px;
        height: 40px;
    }

    .media-separator {
        margin: 0 0.75rem;
    }

    .announcement-close {
        top: 0.5rem;
        right: 0.5rem;
        width: 28px;
        height: 28px;
    }
}

/* Admin Panel Enhancements */
.video-preview-card {
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    overflow: hidden;
    background: white;
}

.link-preview-item {
    background: #f8f9fa;
    transition: all 0.3s ease;
}

.link-preview-item:hover {
    background: #e9ecef;
}

/* Form Enhancements */
#link-preview-container {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 1rem;
    border: 1px solid #e0e0e0;
}

#video-preview {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 1rem;
    border: 1px solid #e0e0e0;
}
