<?php
// Test page for announcement scheduling system
require_once 'config/config.php';
require_once 'includes/functions.php';
require_once 'includes/scheduling_functions.php';
require_once 'database/db_connect.php';

// Start session
start_session();

// Check if user is logged in and is admin
if (!is_logged_in() || !is_admin()) {
    redirect('login.php');
}

$database = new Database();
$db = $database->getConnection();

// Handle test actions
$message = '';
$error = '';

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $action = $_POST['action'] ?? '';
    
    switch ($action) {
        case 'create_scheduled':
            // Create a test scheduled announcement
            $future_time = date('Y-m-d H:i:s', strtotime('+5 minutes'));
            $expire_time = date('Y-m-d H:i:s', strtotime('+1 hour'));
            
            $scheduled_publish_utc = convert_to_utc($future_time);
            $expires_at_utc = convert_to_utc($expire_time);
            
            $query = "INSERT INTO admin_announcements (admin_id, title, message, recipient_type, recipients_count, scheduled_publish_at, expires_at, status, priority)
                      VALUES (:admin_id, :title, :message, :recipient_type, 0, :scheduled_publish_at, :expires_at, 'scheduled', 'normal')";
            $stmt = $db->prepare($query);
            $stmt->bindParam(':admin_id', $_SESSION['user_id']);
            $stmt->bindValue(':title', 'Test Scheduled Announcement');
            $stmt->bindValue(':message', 'This is a test announcement scheduled for 5 minutes from now and will expire in 1 hour.');
            $stmt->bindValue(':recipient_type', 'all');
            $stmt->bindParam(':scheduled_publish_at', $scheduled_publish_utc);
            $stmt->bindParam(':expires_at', $expires_at_utc);
            
            if ($stmt->execute()) {
                $message = "Test scheduled announcement created successfully! Will be published at: " . $future_time;
            } else {
                $error = "Failed to create scheduled announcement.";
            }
            break;
            
        case 'update_statuses':
            // Manually trigger status updates
            $results = update_announcement_statuses($db);
            $message = "Status update completed. Published: {$results['published']}, Expired: {$results['expired']}";
            if (!empty($results['errors'])) {
                $error = "Errors: " . implode(', ', $results['errors']);
            }
            break;
            
        case 'test_timezone':
            // Test timezone conversions
            $test_time = '2024-12-15 14:30:00';
            $utc_time = convert_to_utc($test_time);
            $back_to_local = convert_from_utc($utc_time);
            $message = "Timezone test: Local: {$test_time} → UTC: {$utc_time} → Back: {$back_to_local}";
            break;
    }
}

// Get current announcements by status
$scheduled_announcements = get_announcements_by_status($db, 'scheduled', 10);
$published_announcements = get_announcements_by_status($db, 'published', 10);
$expired_announcements = get_announcements_by_status($db, 'expired', 10);
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Announcement Scheduling System Test - P2P Donate</title>
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.1/css/all.min.css">
    <style>
        .status-scheduled { border-left: 4px solid #ffc107; }
        .status-published { border-left: 4px solid #28a745; }
        .status-expired { border-left: 4px solid #6c757d; }
        .timezone-info { background: #e9ecef; padding: 1rem; border-radius: 8px; margin: 1rem 0; }
    </style>
</head>
<body>
    <div class="container mt-4">
        <div class="row">
            <div class="col-12">
                <h1 class="mb-4">Announcement Scheduling System Test</h1>
                
                <?php if ($message): ?>
                    <div class="alert alert-success">
                        <i class="fas fa-check"></i> <?php echo $message; ?>
                    </div>
                <?php endif; ?>
                
                <?php if ($error): ?>
                    <div class="alert alert-danger">
                        <i class="fas fa-times"></i> <?php echo $error; ?>
                    </div>
                <?php endif; ?>
                
                <!-- Current Time Information -->
                <div class="timezone-info">
                    <h5><i class="fas fa-clock"></i> Current Time Information</h5>
                    <div class="row">
                        <div class="col-md-6">
                            <strong>Platform Time (<?php echo get_platform_timezone(); ?>):</strong><br>
                            <?php echo get_current_platform_time(); ?>
                        </div>
                        <div class="col-md-6">
                            <strong>UTC Time:</strong><br>
                            <?php echo get_current_utc(); ?>
                        </div>
                    </div>
                </div>
                
                <!-- Test Actions -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">Test Actions</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4">
                                <form method="post" class="mb-3">
                                    <input type="hidden" name="action" value="create_scheduled">
                                    <button type="submit" class="btn btn-primary btn-block">
                                        <i class="fas fa-plus"></i> Create Test Scheduled Announcement
                                    </button>
                                    <small class="text-muted">Creates an announcement scheduled for 5 minutes from now</small>
                                </form>
                            </div>
                            <div class="col-md-4">
                                <form method="post" class="mb-3">
                                    <input type="hidden" name="action" value="update_statuses">
                                    <button type="submit" class="btn btn-warning btn-block">
                                        <i class="fas fa-sync"></i> Update Statuses Manually
                                    </button>
                                    <small class="text-muted">Manually trigger status updates (normally done by cron)</small>
                                </form>
                            </div>
                            <div class="col-md-4">
                                <form method="post" class="mb-3">
                                    <input type="hidden" name="action" value="test_timezone">
                                    <button type="submit" class="btn btn-info btn-block">
                                        <i class="fas fa-globe"></i> Test Timezone Conversion
                                    </button>
                                    <small class="text-muted">Test timezone conversion functions</small>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Announcements by Status -->
                <div class="row">
                    <!-- Scheduled Announcements -->
                    <div class="col-md-4">
                        <div class="card mb-4">
                            <div class="card-header bg-warning text-dark">
                                <h6 class="mb-0">
                                    <i class="fas fa-clock"></i> Scheduled Announcements
                                    <span class="badge badge-dark ml-2"><?php echo count($scheduled_announcements); ?></span>
                                </h6>
                            </div>
                            <div class="card-body p-0">
                                <?php if (empty($scheduled_announcements)): ?>
                                    <div class="p-3 text-center text-muted">
                                        No scheduled announcements
                                    </div>
                                <?php else: ?>
                                    <?php foreach ($scheduled_announcements as $announcement): ?>
                                        <div class="border-bottom p-3 status-scheduled">
                                            <h6 class="mb-1"><?php echo htmlspecialchars($announcement['title']); ?></h6>
                                            <p class="mb-2 small"><?php echo htmlspecialchars(substr($announcement['message'], 0, 100)) . '...'; ?></p>
                                            <small class="text-muted">
                                                <strong>Scheduled:</strong> <?php echo format_admin_datetime($announcement['scheduled_publish_at']); ?><br>
                                                <?php if (!empty($announcement['expires_at'])): ?>
                                                    <strong>Expires:</strong> <?php echo format_admin_datetime($announcement['expires_at']); ?>
                                                <?php endif; ?>
                                            </small>
                                        </div>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Published Announcements -->
                    <div class="col-md-4">
                        <div class="card mb-4">
                            <div class="card-header bg-success text-white">
                                <h6 class="mb-0">
                                    <i class="fas fa-check-circle"></i> Published Announcements
                                    <span class="badge badge-light text-dark ml-2"><?php echo count($published_announcements); ?></span>
                                </h6>
                            </div>
                            <div class="card-body p-0">
                                <?php if (empty($published_announcements)): ?>
                                    <div class="p-3 text-center text-muted">
                                        No published announcements
                                    </div>
                                <?php else: ?>
                                    <?php foreach ($published_announcements as $announcement): ?>
                                        <div class="border-bottom p-3 status-published">
                                            <h6 class="mb-1"><?php echo htmlspecialchars($announcement['title']); ?></h6>
                                            <p class="mb-2 small"><?php echo htmlspecialchars(substr($announcement['message'], 0, 100)) . '...'; ?></p>
                                            <small class="text-muted">
                                                <strong>Published:</strong> <?php echo format_date($announcement['created_at']); ?><br>
                                                <?php if (!empty($announcement['expires_at'])): ?>
                                                    <strong>Expires:</strong> <?php echo format_admin_datetime($announcement['expires_at']); ?>
                                                <?php endif; ?>
                                            </small>
                                        </div>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Expired Announcements -->
                    <div class="col-md-4">
                        <div class="card mb-4">
                            <div class="card-header bg-secondary text-white">
                                <h6 class="mb-0">
                                    <i class="fas fa-times-circle"></i> Expired Announcements
                                    <span class="badge badge-light text-dark ml-2"><?php echo count($expired_announcements); ?></span>
                                </h6>
                            </div>
                            <div class="card-body p-0">
                                <?php if (empty($expired_announcements)): ?>
                                    <div class="p-3 text-center text-muted">
                                        No expired announcements
                                    </div>
                                <?php else: ?>
                                    <?php foreach ($expired_announcements as $announcement): ?>
                                        <div class="border-bottom p-3 status-expired">
                                            <h6 class="mb-1"><?php echo htmlspecialchars($announcement['title']); ?></h6>
                                            <p class="mb-2 small"><?php echo htmlspecialchars(substr($announcement['message'], 0, 100)) . '...'; ?></p>
                                            <small class="text-muted">
                                                <strong>Expired:</strong> <?php echo format_admin_datetime($announcement['expires_at']); ?>
                                            </small>
                                        </div>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- System Information -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">System Information</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>Scheduling Functions Status</h6>
                                <ul class="list-unstyled">
                                    <li><i class="fas fa-check text-success"></i> get_platform_timezone(): <?php echo get_platform_timezone(); ?></li>
                                    <li><i class="fas fa-check text-success"></i> convert_to_utc(): Available</li>
                                    <li><i class="fas fa-check text-success"></i> convert_from_utc(): Available</li>
                                    <li><i class="fas fa-check text-success"></i> update_announcement_statuses(): Available</li>
                                    <li><i class="fas fa-check text-success"></i> get_active_announcements(): Available</li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <h6>Database Schema</h6>
                                <ul class="list-unstyled">
                                    <li><i class="fas fa-check text-success"></i> scheduled_publish_at column: Added</li>
                                    <li><i class="fas fa-check text-success"></i> expires_at column: Added</li>
                                    <li><i class="fas fa-check text-success"></i> status column: Added</li>
                                    <li><i class="fas fa-check text-success"></i> Database indexes: Optimized</li>
                                </ul>
                            </div>
                        </div>
                        
                        <div class="mt-3">
                            <h6>Cron Job Setup</h6>
                            <p>To enable automatic status updates, add this cron job:</p>
                            <code>* * * * * /usr/bin/php <?php echo __DIR__; ?>/cron/update_announcement_statuses.php >> /var/log/announcement_cron.log 2>&1</code>
                        </div>
                    </div>
                </div>
                
                <div class="mt-4">
                    <a href="admin/announcements.php" class="btn btn-primary">
                        <i class="fas fa-cog"></i> Admin Announcements
                    </a>
                    <a href="dashboard.php" class="btn btn-success">
                        <i class="fas fa-tachometer-alt"></i> User Dashboard
                    </a>
                    <button class="btn btn-info" onclick="location.reload()">
                        <i class="fas fa-sync"></i> Refresh
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.5.1.min.js"></script>
    <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>
    <script>
        // Auto-refresh every 30 seconds to show real-time updates
        setTimeout(function() {
            location.reload();
        }, 30000);
    </script>
</body>
</html>
