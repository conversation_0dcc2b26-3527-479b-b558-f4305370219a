<?php
/**
 * Database migration script to add scheduling columns to admin_announcements table
 * Adds: scheduled_publish_at, expires_at, status columns for announcement scheduling
 */

// Include database connection
require_once dirname(dirname(__DIR__)) . '/config/config.php';
require_once dirname(dirname(__DIR__)) . '/database/db_connect.php';

echo "=== Adding Scheduling Columns to admin_announcements Table ===\n\n";

try {
    $database = new Database();
    $db = $database->getConnection();

    // Check if scheduled_publish_at column exists
    $query = "SHOW COLUMNS FROM admin_announcements LIKE 'scheduled_publish_at'";
    $stmt = $db->prepare($query);
    $stmt->execute();
    $scheduled_publish_column_exists = $stmt->rowCount() > 0;

    if (!$scheduled_publish_column_exists) {
        echo "Adding scheduled_publish_at column to admin_announcements table...\n";
        $query = "ALTER TABLE admin_announcements ADD COLUMN scheduled_publish_at DATETIME NULL";
        $stmt = $db->prepare($query);

        if ($stmt->execute()) {
            echo "✓ scheduled_publish_at column added successfully.\n";
        } else {
            echo "✗ Error adding scheduled_publish_at column: " . print_r($stmt->errorInfo(), true) . "\n";
        }
    } else {
        echo "✓ scheduled_publish_at column already exists.\n";
    }

    // Check if expires_at column exists
    $query = "SHOW COLUMNS FROM admin_announcements LIKE 'expires_at'";
    $stmt = $db->prepare($query);
    $stmt->execute();
    $expires_at_column_exists = $stmt->rowCount() > 0;

    if (!$expires_at_column_exists) {
        echo "Adding expires_at column to admin_announcements table...\n";
        $query = "ALTER TABLE admin_announcements ADD COLUMN expires_at DATETIME NULL";
        $stmt = $db->prepare($query);

        if ($stmt->execute()) {
            echo "✓ expires_at column added successfully.\n";
        } else {
            echo "✗ Error adding expires_at column: " . print_r($stmt->errorInfo(), true) . "\n";
        }
    } else {
        echo "✓ expires_at column already exists.\n";
    }

    // Check if status column exists
    $query = "SHOW COLUMNS FROM admin_announcements LIKE 'status'";
    $stmt = $db->prepare($query);
    $stmt->execute();
    $status_column_exists = $stmt->rowCount() > 0;

    if (!$status_column_exists) {
        echo "Adding status column to admin_announcements table...\n";
        $query = "ALTER TABLE admin_announcements ADD COLUMN status ENUM('draft', 'scheduled', 'published', 'expired') DEFAULT 'published'";
        $stmt = $db->prepare($query);

        if ($stmt->execute()) {
            echo "✓ status column added successfully.\n";
        } else {
            echo "✗ Error adding status column: " . print_r($stmt->errorInfo(), true) . "\n";
        }
    } else {
        echo "✓ status column already exists.\n";
    }

    // Update existing announcements to have 'published' status
    echo "\nUpdating existing announcements to 'published' status...\n";
    $query = "UPDATE admin_announcements SET status = 'published' WHERE status IS NULL OR status = ''";
    $stmt = $db->prepare($query);

    if ($stmt->execute()) {
        $affected_rows = $stmt->rowCount();
        echo "✓ Updated {$affected_rows} existing announcements to 'published' status.\n";
    } else {
        echo "✗ Error updating existing announcements: " . print_r($stmt->errorInfo(), true) . "\n";
    }

    // Add index for better query performance
    echo "\nAdding database indexes for scheduling queries...\n";

    try {
        $query = "CREATE INDEX idx_announcement_scheduling ON admin_announcements (status, scheduled_publish_at, expires_at)";
        $stmt = $db->prepare($query);
        $stmt->execute();
        echo "✓ Scheduling index added successfully.\n";
    } catch (PDOException $e) {
        if (strpos($e->getMessage(), 'Duplicate key name') !== false) {
            echo "✓ Scheduling index already exists.\n";
        } else {
            echo "✗ Error adding scheduling index: " . $e->getMessage() . "\n";
        }
    }

    try {
        $query = "CREATE INDEX idx_announcement_publish_time ON admin_announcements (scheduled_publish_at)";
        $stmt = $db->prepare($query);
        $stmt->execute();
        echo "✓ Publish time index added successfully.\n";
    } catch (PDOException $e) {
        if (strpos($e->getMessage(), 'Duplicate key name') !== false) {
            echo "✓ Publish time index already exists.\n";
        } else {
            echo "✗ Error adding publish time index: " . $e->getMessage() . "\n";
        }
    }

    try {
        $query = "CREATE INDEX idx_announcement_expiry_time ON admin_announcements (expires_at)";
        $stmt = $db->prepare($query);
        $stmt->execute();
        echo "✓ Expiry time index added successfully.\n";
    } catch (PDOException $e) {
        if (strpos($e->getMessage(), 'Duplicate key name') !== false) {
            echo "✓ Expiry time index already exists.\n";
        } else {
            echo "✗ Error adding expiry time index: " . $e->getMessage() . "\n";
        }
    }

    // Display final table structure
    echo "\n=== Final Table Structure ===\n";
    $query = "SHOW COLUMNS FROM admin_announcements";
    $stmt = $db->prepare($query);
    $stmt->execute();
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);

    foreach ($columns as $column) {
        echo "- {$column['Field']}: {$column['Type']}";
        if ($column['Null'] === 'NO') echo " NOT NULL";
        if (!empty($column['Default'])) echo " DEFAULT '{$column['Default']}'";
        if (!empty($column['Extra'])) echo " {$column['Extra']}";
        echo "\n";
    }

    echo "\n=== Migration Completed Successfully! ===\n";
    echo "The admin_announcements table now supports:\n";
    echo "✓ Scheduled publication (scheduled_publish_at)\n";
    echo "✓ Automatic expiration (expires_at)\n";
    echo "✓ Status tracking (draft, scheduled, published, expired)\n";
    echo "✓ Optimized database indexes for performance\n";
    echo "✓ Backward compatibility with existing announcements\n\n";

} catch (Exception $e) {
    echo "✗ Migration failed: " . $e->getMessage() . "\n";
    exit(1);
}
?>
