<?php
/**
 * Migration script to convert legacy users to amount-aware system
 * This script updates users who are in the queue but don't have amount tracking
 */

require_once 'includes/config.php';

echo "=== P2P Donate: Migrating to Amount-Aware System ===\n\n";

try {
    // Find users in queue without amount tracking
    $query = "SELECT id, name, pledges_to_receive 
              FROM users 
              WHERE pledges_to_receive > 0 
              AND (amount_to_receive IS NULL OR amount_to_receive = 0 OR original_pledge_amount IS NULL OR original_pledge_amount = 0)";
    $stmt = $db->prepare($query);
    $stmt->execute();
    $legacy_users = $stmt->fetchAll(PDO::FETCH_OBJ);
    
    if (empty($legacy_users)) {
        echo "✓ No legacy users found. All users are already using the amount-aware system.\n";
        exit(0);
    }
    
    echo "Found " . count($legacy_users) . " legacy users to migrate:\n\n";
    
    $migrated_count = 0;
    $default_pledge_amount = 20; // Default to GHS 20 for legacy users
    $double_amount = $default_pledge_amount * 2;
    
    foreach ($legacy_users as $user) {
        echo "Migrating user: {$user->name} (ID: {$user->id})\n";
        echo "  Current pledges_to_receive: {$user->pledges_to_receive}\n";
        
        // Update user with amount-aware fields
        $query = "UPDATE users SET 
                  amount_to_receive = :amount_to_receive,
                  original_pledge_amount = :original_amount
                  WHERE id = :user_id";
        $stmt = $db->prepare($query);
        $stmt->bindParam(':amount_to_receive', $double_amount);
        $stmt->bindParam(':original_amount', $default_pledge_amount);
        $stmt->bindParam(':user_id', $user->id);
        
        if ($stmt->execute()) {
            echo "  ✓ Migrated to amount-aware system: will receive GHS {$double_amount} (double of GHS {$default_pledge_amount})\n";
            $migrated_count++;
        } else {
            echo "  ✗ Failed to migrate user\n";
        }
        
        echo "\n";
    }
    
    echo "=== Migration Complete ===\n";
    echo "Successfully migrated {$migrated_count} out of " . count($legacy_users) . " users.\n";
    echo "All users are now using the amount-aware queue system.\n";
    
} catch (Exception $e) {
    echo "Error during migration: " . $e->getMessage() . "\n";
    exit(1);
}
?>
