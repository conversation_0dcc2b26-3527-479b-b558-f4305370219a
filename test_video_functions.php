<?php
// CLI test script for video embedding functions
require_once 'includes/announcement_functions.php';

echo "=== Video Embedding Functions Test ===\n\n";

// Test 1: Function availability
echo "1. Testing function availability:\n";
echo "   validate_video_url(): " . (function_exists('validate_video_url') ? "✓ Available" : "✗ Missing") . "\n";
echo "   generate_video_embed(): " . (function_exists('generate_video_embed') ? "✓ Available" : "✗ Missing") . "\n";
echo "   get_priority_styling(): " . (function_exists('get_priority_styling') ? "✓ Available" : "✗ Missing") . "\n\n";

// Test 2: Video URL validation
echo "2. Testing video URL validation:\n";

$test_urls = [
    'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
    'https://youtu.be/dQw4w9WgXcQ',
    'https://www.youtube.com/embed/dQw4w9WgXcQ',
    'https://vimeo.com/148751763',
    'https://player.vimeo.com/video/148751763',
    'https://invalid-url.com/video',
    ''
];

foreach ($test_urls as $url) {
    $result = validate_video_url($url);
    if ($result) {
        echo "   ✓ Valid: $url -> {$result['platform']} ({$result['video_id']})\n";
    } else {
        echo "   ✗ Invalid: " . ($url ?: '(empty)') . "\n";
    }
}

echo "\n";

// Test 3: Video embed generation
echo "3. Testing video embed generation:\n";

$youtube_url = 'https://www.youtube.com/watch?v=dQw4w9WgXcQ';
$vimeo_url = 'https://vimeo.com/148751763';

$youtube_embed = generate_video_embed($youtube_url, 'Test YouTube Video');
$vimeo_embed = generate_video_embed($vimeo_url, 'Test Vimeo Video');

echo "   YouTube embed: " . ($youtube_embed ? "✓ Generated (" . strlen($youtube_embed) . " chars)" : "✗ Failed") . "\n";
echo "   Vimeo embed: " . ($vimeo_embed ? "✓ Generated (" . strlen($vimeo_embed) . " chars)" : "✗ Failed") . "\n\n";

// Test 4: Priority styling
echo "4. Testing priority styling:\n";

$priorities = ['urgent', 'normal', 'info'];
foreach ($priorities as $priority) {
    $styling = get_priority_styling($priority);
    echo "   $priority: badge={$styling['badge_class']}, icon={$styling['icon']}, color={$styling['color']}\n";
}

echo "\n";

// Test 5: Sample embed output
echo "5. Sample YouTube embed output:\n";
echo "---\n";
echo $youtube_embed;
echo "\n---\n\n";

echo "=== Test Complete ===\n";
echo "All functions are working correctly!\n";
?>
