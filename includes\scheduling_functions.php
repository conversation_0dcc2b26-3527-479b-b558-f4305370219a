<?php
/**
 * Announcement Scheduling Functions for P2P Donate Platform
 * Handles scheduling, timezone management, and status updates
 */

/**
 * Get the default timezone for the platform (Ghana timezone)
 * @return string Timezone identifier
 */
function get_platform_timezone() {
    return 'Africa/Accra'; // Ghana timezone (GMT+0)
}

/**
 * Convert user input datetime to UTC for database storage
 * @param string $datetime_string User input datetime
 * @param string $user_timezone User's timezone (defaults to platform timezone)
 * @return string UTC datetime string for database
 */
function convert_to_utc($datetime_string, $user_timezone = null) {
    if (empty($datetime_string)) {
        return null;
    }
    
    if ($user_timezone === null) {
        $user_timezone = get_platform_timezone();
    }
    
    try {
        $user_tz = new DateTimeZone($user_timezone);
        $utc_tz = new DateTimeZone('UTC');
        
        $datetime = new DateTime($datetime_string, $user_tz);
        $datetime->setTimezone($utc_tz);
        
        return $datetime->format('Y-m-d H:i:s');
    } catch (Exception $e) {
        error_log("Timezone conversion error: " . $e->getMessage());
        return null;
    }
}

/**
 * Convert UTC datetime from database to user timezone for display
 * @param string $utc_datetime UTC datetime from database
 * @param string $user_timezone User's timezone (defaults to platform timezone)
 * @return string Formatted datetime string for display
 */
function convert_from_utc($utc_datetime, $user_timezone = null) {
    if (empty($utc_datetime)) {
        return '';
    }
    
    if ($user_timezone === null) {
        $user_timezone = get_platform_timezone();
    }
    
    try {
        $utc_tz = new DateTimeZone('UTC');
        $user_tz = new DateTimeZone($user_timezone);
        
        $datetime = new DateTime($utc_datetime, $utc_tz);
        $datetime->setTimezone($user_tz);
        
        return $datetime->format('Y-m-d H:i:s');
    } catch (Exception $e) {
        error_log("Timezone conversion error: " . $e->getMessage());
        return $utc_datetime;
    }
}

/**
 * Get current UTC datetime
 * @return string Current UTC datetime
 */
function get_current_utc() {
    return gmdate('Y-m-d H:i:s');
}

/**
 * Get current platform timezone datetime
 * @return string Current platform timezone datetime
 */
function get_current_platform_time() {
    try {
        $tz = new DateTimeZone(get_platform_timezone());
        $datetime = new DateTime('now', $tz);
        return $datetime->format('Y-m-d H:i:s');
    } catch (Exception $e) {
        error_log("Platform time error: " . $e->getMessage());
        return date('Y-m-d H:i:s');
    }
}

/**
 * Validate scheduling dates
 * @param string $scheduled_publish_at Publication datetime
 * @param string $expires_at Expiration datetime
 * @return array Validation result with success status and error messages
 */
function validate_scheduling_dates($scheduled_publish_at, $expires_at) {
    $errors = [];
    $current_time = get_current_utc();
    
    // Validate publication date
    if (!empty($scheduled_publish_at)) {
        $publish_utc = convert_to_utc($scheduled_publish_at);
        if ($publish_utc === null) {
            $errors[] = "Invalid publication date format.";
        }
    }
    
    // Validate expiration date
    if (!empty($expires_at)) {
        $expires_utc = convert_to_utc($expires_at);
        if ($expires_utc === null) {
            $errors[] = "Invalid expiration date format.";
        }
    }
    
    // Check if expiration is after publication
    if (!empty($scheduled_publish_at) && !empty($expires_at)) {
        $publish_utc = convert_to_utc($scheduled_publish_at);
        $expires_utc = convert_to_utc($expires_at);
        
        if ($publish_utc && $expires_utc && $expires_utc <= $publish_utc) {
            $errors[] = "Expiration date must be after publication date.";
        }
    }
    
    return [
        'success' => empty($errors),
        'errors' => $errors
    ];
}

/**
 * Determine announcement status based on scheduling
 * @param string $scheduled_publish_at Publication datetime (UTC)
 * @param string $expires_at Expiration datetime (UTC)
 * @return string Status (draft, scheduled, published, expired)
 */
function determine_announcement_status($scheduled_publish_at, $expires_at) {
    $current_utc = get_current_utc();
    
    // If no publication date set, it's a draft
    if (empty($scheduled_publish_at)) {
        return 'draft';
    }
    
    // If publication date is in the future, it's scheduled
    if ($scheduled_publish_at > $current_utc) {
        return 'scheduled';
    }
    
    // If expiration date is set and passed, it's expired
    if (!empty($expires_at) && $expires_at <= $current_utc) {
        return 'expired';
    }
    
    // Otherwise, it's published
    return 'published';
}

/**
 * Update announcement statuses based on current time
 * @param PDO $db Database connection
 * @return array Results of status updates
 */
function update_announcement_statuses($db) {
    $current_utc = get_current_utc();
    $results = [
        'published' => 0,
        'expired' => 0,
        'errors' => []
    ];
    
    try {
        // Update scheduled announcements that should now be published
        $query = "UPDATE admin_announcements 
                  SET status = 'published' 
                  WHERE status = 'scheduled' 
                  AND scheduled_publish_at IS NOT NULL 
                  AND scheduled_publish_at <= :current_time";
        $stmt = $db->prepare($query);
        $stmt->bindParam(':current_time', $current_utc);
        $stmt->execute();
        $results['published'] = $stmt->rowCount();
        
        // Update published announcements that should now be expired
        $query = "UPDATE admin_announcements 
                  SET status = 'expired' 
                  WHERE status = 'published' 
                  AND expires_at IS NOT NULL 
                  AND expires_at <= :current_time";
        $stmt = $db->prepare($query);
        $stmt->bindParam(':current_time', $current_utc);
        $stmt->execute();
        $results['expired'] = $stmt->rowCount();
        
    } catch (Exception $e) {
        $results['errors'][] = "Status update error: " . $e->getMessage();
        error_log("Announcement status update error: " . $e->getMessage());
    }
    
    return $results;
}

/**
 * Get announcements by status with optional filtering
 * @param PDO $db Database connection
 * @param string $status Status filter (optional)
 * @param int $limit Number of results to return (optional)
 * @return array Array of announcements
 */
function get_announcements_by_status($db, $status = null, $limit = null) {
    $query = "SELECT *, 
                     CASE 
                         WHEN scheduled_publish_at IS NULL THEN 'draft'
                         WHEN scheduled_publish_at > UTC_TIMESTAMP() THEN 'scheduled'
                         WHEN expires_at IS NOT NULL AND expires_at <= UTC_TIMESTAMP() THEN 'expired'
                         ELSE 'published'
                     END as calculated_status
              FROM admin_announcements";
    
    $params = [];
    
    if ($status !== null) {
        $query .= " WHERE status = :status";
        $params[':status'] = $status;
    }
    
    $query .= " ORDER BY created_at DESC";
    
    if ($limit !== null) {
        $query .= " LIMIT :limit";
        $params[':limit'] = $limit;
    }
    
    try {
        $stmt = $db->prepare($query);
        
        foreach ($params as $param => $value) {
            if ($param === ':limit') {
                $stmt->bindValue($param, $value, PDO::PARAM_INT);
            } else {
                $stmt->bindValue($param, $value);
            }
        }
        
        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (Exception $e) {
        error_log("Get announcements error: " . $e->getMessage());
        return [];
    }
}

/**
 * Get currently active announcements for dashboard display
 * @param PDO $db Database connection
 * @param int $user_id User ID for notification filtering
 * @return array Array of active announcements
 */
function get_active_announcements($db, $user_id) {
    $current_utc = get_current_utc();
    
    $query = "SELECT a.*
              FROM admin_announcements a
              INNER JOIN notifications n ON n.title = a.title AND n.message = a.message
              WHERE n.user_id = :user_id 
              AND n.type = 'system'
              AND a.status = 'published'
              AND (a.scheduled_publish_at IS NULL OR a.scheduled_publish_at <= :current_time)
              AND (a.expires_at IS NULL OR a.expires_at > :current_time)
              ORDER BY a.is_pinned DESC, 
                       a.priority = 'urgent' DESC, 
                       a.priority = 'normal' DESC, 
                       a.created_at DESC 
              LIMIT 1";
    
    try {
        $stmt = $db->prepare($query);
        $stmt->bindParam(':user_id', $user_id);
        $stmt->bindParam(':current_time', $current_utc);
        $stmt->execute();
        return $stmt->fetch(PDO::FETCH_OBJ);
    } catch (Exception $e) {
        error_log("Get active announcements error: " . $e->getMessage());
        return null;
    }
}

/**
 * Format datetime for display in admin interface
 * @param string $datetime Datetime string
 * @param bool $include_timezone Whether to include timezone info
 * @return string Formatted datetime
 */
function format_admin_datetime($datetime, $include_timezone = true) {
    if (empty($datetime)) {
        return 'Not set';
    }
    
    $display_time = convert_from_utc($datetime);
    $formatted = date('M j, Y \a\t g:i A', strtotime($display_time));
    
    if ($include_timezone) {
        $tz_name = get_platform_timezone();
        $formatted .= " ({$tz_name})";
    }
    
    return $formatted;
}

/**
 * Get status badge HTML for admin interface
 * @param string $status Announcement status
 * @return string HTML for status badge
 */
function get_status_badge($status) {
    $badges = [
        'draft' => '<span class="badge badge-secondary"><i class="fas fa-edit"></i> Draft</span>',
        'scheduled' => '<span class="badge badge-warning"><i class="fas fa-clock"></i> Scheduled</span>',
        'published' => '<span class="badge badge-success"><i class="fas fa-check-circle"></i> Published</span>',
        'expired' => '<span class="badge badge-dark"><i class="fas fa-times-circle"></i> Expired</span>'
    ];
    
    return $badges[$status] ?? '<span class="badge badge-light">Unknown</span>';
}

/**
 * Check if announcement can be edited
 * @param array $announcement Announcement data
 * @return bool Whether announcement can be edited
 */
function can_edit_announcement($announcement) {
    return in_array($announcement['status'], ['draft', 'scheduled']);
}

/**
 * Check if announcement can be published immediately
 * @param array $announcement Announcement data
 * @return bool Whether announcement can be published immediately
 */
function can_publish_immediately($announcement) {
    return in_array($announcement['status'], ['draft', 'scheduled']);
}
?>
