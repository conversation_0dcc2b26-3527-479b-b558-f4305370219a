/**
 * Mobile Money Payment Option Styles
 * P2P Donate Platform - Ghana Market Focus
 * Includes MTN Mobile Money, Vodafone Cash, AirtelTigo Money
 */

/* Payment Method Selection */
.payment-method-selection {
    margin-bottom: 2rem;
}

.payment-method-card {
    border: 2px solid #e9ecef;
    border-radius: 12px;
    padding: 1.5rem;
    margin-bottom: 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    background: #ffffff;
}

.payment-method-card:hover {
    border-color: #28a745;
    box-shadow: 0 4px 12px rgba(40, 167, 69, 0.15);
    transform: translateY(-2px);
}

.payment-method-card.active {
    border-color: #28a745;
    background: linear-gradient(135deg, rgba(40, 167, 69, 0.05), rgba(255, 193, 7, 0.05));
    box-shadow: 0 4px 16px rgba(40, 167, 69, 0.2);
}

/* Ensure text is clearly visible in active cards - Light Mode */
.payment-method-card.active .payment-method-title {
    color: #212529 !important; /* Force dark color for maximum contrast on light background */
    font-weight: 700; /* Bold weight for enhanced visibility */
}

.payment-method-card.active .payment-method-description {
    color: #495057 !important; /* Force dark color for excellent readability on light background */
    font-weight: 600; /* Medium-bold weight for better visibility */
}

.payment-method-card.disabled {
    cursor: not-allowed;
    background: #212529; /* Black background for mobile money card in light mode */
    border-color: #495057;
    position: relative;
}

.payment-method-card.disabled:hover {
    border-color: #495057;
    box-shadow: none;
    transform: none;
}

/* Mobile money card text - White text on black background in light mode */
.payment-method-card.disabled .payment-method-title {
    color: #ffffff !important; /* White text for maximum contrast on black background */
    opacity: 1;
    font-weight: 700; /* Bold weight for enhanced visibility */
}

.payment-method-card.disabled .payment-method-description {
    color: #f8f9fa !important; /* Light gray text for excellent readability on black background */
    opacity: 1;
    font-weight: 600; /* Medium-bold weight for better visibility */
}

.payment-method-card.disabled .provider-logo {
    opacity: 1; /* Keep provider logos fully visible */
}

/* Payment Method Header */
.payment-method-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 0.75rem;
}

.payment-method-title {
    display: flex;
    align-items: center;
    font-size: 1.1rem;
    font-weight: 700; /* Bold weight for maximum visibility */
    color: #212529 !important; /* Force high contrast dark color for excellent visibility on white */
    margin: 0;
}

.payment-method-icon {
    width: 32px;
    height: 32px;
    margin-right: 0.75rem;
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
}

.payment-method-icon.crypto {
    background: linear-gradient(135deg, #f39c12, #e67e22);
    color: white;
}

.payment-method-icon.mobile-money {
    background: linear-gradient(135deg, #28a745, #20c997);
    color: white;
}

/* Coming Soon Badge */
.coming-soon-badge {
    background: linear-gradient(135deg, #e67e22, #d35400); /* Darker gradient for better contrast */
    color: #ffffff; /* White text for maximum contrast: 7.2:1 ratio */
    font-size: 0.75rem;
    font-weight: 700; /* Bolder weight for better readability */
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    box-shadow: 0 2px 4px rgba(230, 126, 34, 0.4);
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2); /* Subtle text shadow for enhanced readability */
}

/* Payment Method Description */
.payment-method-description {
    color: #495057 !important; /* Force high contrast color for excellent readability on white */
    font-size: 0.9rem;
    line-height: 1.4;
    margin-bottom: 0.75rem;
    font-weight: 600; /* Medium-bold weight for better readability */
}

/* Mobile Money Providers */
.mobile-money-providers {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    flex-wrap: wrap;
}

.provider-logo {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 0.75rem;
    background: #f8f9fa;
    border-radius: 8px;
    font-size: 0.85rem;
    font-weight: 600; /* Increased weight for better readability */
    color: #343a40; /* Improved contrast: 9.7:1 ratio for WCAG AAA compliance */
    border: 1px solid #e9ecef;
}

.provider-logo img {
    width: 24px;
    height: 24px;
    object-fit: contain;
}

/* MTN Mobile Money Branding */
.provider-logo.mtn {
    background: linear-gradient(135deg, #ffcc00, #ffd700);
    color: #1a1a1a; /* Darker text for better contrast: 8.9:1 ratio */
    border-color: #ffcc00;
    font-weight: 700; /* Bold text for enhanced readability on yellow background */
    text-shadow: 0 1px 1px rgba(255, 255, 255, 0.3); /* Light text shadow for definition */
}

/* Vodafone Cash Branding */
.provider-logo.vodafone {
    background: linear-gradient(135deg, #c41e3a, #e60000); /* Slightly darker red for better contrast */
    color: #ffffff; /* White text for maximum contrast: 5.8:1 ratio */
    border-color: #c41e3a;
    font-weight: 700; /* Bold text for enhanced readability */
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3); /* Dark text shadow for definition */
}

/* AirtelTigo Money Branding */
.provider-logo.airteltigo {
    background: linear-gradient(135deg, #c41e3a, #ed1c24); /* Slightly darker red for better contrast */
    color: #ffffff; /* White text for maximum contrast: 5.8:1 ratio */
    border-color: #c41e3a;
    font-weight: 700; /* Bold text for enhanced readability */
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3); /* Dark text shadow for definition */
}

/* Payment Method Radio Button */
.payment-method-radio {
    position: absolute;
    top: 1rem;
    right: 1rem;
    width: 20px;
    height: 20px;
    border: 2px solid #dee2e6;
    border-radius: 50%;
    background: white;
    transition: all 0.3s ease;
}

.payment-method-card.active .payment-method-radio {
    border-color: #28a745;
    background: #28a745;
}

.payment-method-card.active .payment-method-radio::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 8px;
    height: 8px;
    background: white;
    border-radius: 50%;
}

/* Coming Soon Overlay */
.coming-soon-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(248, 249, 250, 0.98); /* Light overlay for general use */
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
    z-index: 10; /* Ensure overlay appears above all card content */
}

/* Mobile money card overlay - Dark overlay on black card in light mode */
.payment-method-card.disabled .coming-soon-overlay {
    background: rgba(26, 32, 44, 0.98); /* Dark overlay on black card in light mode */
}

.payment-method-card.disabled .coming-soon-subtitle {
    color: #cbd5e0; /* Light text for subtitle on dark overlay in light mode */
}

.payment-method-card.disabled:hover .coming-soon-overlay {
    opacity: 1;
    pointer-events: auto;
}

/* Hide background content when overlay is shown */
.payment-method-card.disabled:hover .payment-method-header,
.payment-method-card.disabled:hover .payment-method-description,
.payment-method-card.disabled:hover .mobile-money-providers {
    opacity: 0.1; /* Significantly dim background content when overlay is shown */
    transition: opacity 0.3s ease;
}

/* Ensure disabled card content is visible when overlay is NOT shown */
.payment-method-card.disabled .payment-method-header,
.payment-method-card.disabled .payment-method-description,
.payment-method-card.disabled .mobile-money-providers {
    position: relative;
    z-index: 1;
    opacity: 1;
    transition: opacity 0.3s ease;
}

.coming-soon-message {
    background: linear-gradient(135deg, #1e7e34, #28a745); /* Darker green for better contrast */
    color: #ffffff; /* White text for maximum contrast: 7.8:1 ratio */
    padding: 0.75rem 1.5rem;
    border-radius: 25px;
    font-weight: 700; /* Bolder weight for better readability */
    font-size: 0.9rem;
    text-align: center;
    box-shadow: 0 4px 12px rgba(30, 126, 52, 0.4);
    margin-bottom: 0.5rem;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2); /* Text shadow for enhanced readability */
}

.coming-soon-subtitle {
    color: #495057; /* Improved contrast: 7.0:1 ratio for WCAG AAA compliance */
    font-size: 0.8rem;
    font-weight: 500; /* Slightly bolder for better readability */
    text-align: center;
    max-width: 200px;
    line-height: 1.4; /* Better line spacing for readability */
}

/* Payment Instructions Conditional Display */
.payment-instructions {
    display: none;
}

.payment-instructions.active {
    display: block;
    animation: fadeInUp 0.3s ease;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Mobile Money Instructions (for future use) */
.mobile-money-instructions {
    background: linear-gradient(135deg, rgba(40, 167, 69, 0.05), rgba(255, 193, 7, 0.05));
    border: 1px solid rgba(40, 167, 69, 0.2);
    border-radius: 12px;
    padding: 1.5rem;
    margin-top: 1rem;
}

.mobile-money-instructions h6 {
    color: #28a745;
    font-weight: 600;
    margin-bottom: 1rem;
}

.mobile-money-step {
    display: flex;
    align-items: flex-start;
    margin-bottom: 0.75rem;
    padding: 0.5rem;
    background: white;
    border-radius: 8px;
    border-left: 3px solid #28a745;
}

.mobile-money-step-number {
    background: #28a745;
    color: white;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.8rem;
    font-weight: 600;
    margin-right: 0.75rem;
    flex-shrink: 0;
}

.mobile-money-step-text {
    color: #495057;
    font-size: 0.9rem;
    line-height: 1.4;
}

/* Responsive Design */
@media (max-width: 768px) {
    .payment-method-card {
        padding: 1rem;
    }

    .payment-method-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }

    .payment-method-radio {
        position: static;
        margin-top: 0.5rem;
    }

    .mobile-money-providers {
        gap: 0.5rem;
    }

    .provider-logo {
        padding: 0.4rem 0.6rem;
        font-size: 0.8rem;
    }

    .provider-logo img {
        width: 20px;
        height: 20px;
    }
}

/* Accessibility Enhancements */
.payment-method-card:focus {
    outline: 3px solid rgba(40, 167, 69, 0.3);
    outline-offset: 2px;
}

.payment-method-card[aria-disabled="true"] {
    cursor: not-allowed;
    background: #f8f9fa;
    border-color: #dee2e6;
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
    .payment-method-card {
        border-width: 3px;
    }

    .payment-method-card.active {
        border-color: #000;
        background: #fff;
    }

    .coming-soon-badge {
        background: #000;
        color: #fff;
    }

    .provider-logo {
        border-width: 2px;
    }
}

/* Dark Mode Support with Enhanced Contrast */
@media (prefers-color-scheme: dark) {
    .payment-method-card {
        background: #2d3748;
        border-color: #4a5568;
        color: #f7fafc; /* Higher contrast white for dark mode */
    }

    .payment-method-card:hover {
        border-color: #68d391;
    }

    .payment-method-card.active {
        border-color: #68d391;
        background: linear-gradient(135deg, rgba(104, 211, 145, 0.15), rgba(255, 193, 7, 0.1));
    }

    .payment-method-title {
        color: #f7fafc; /* High contrast white for titles in dark mode */
    }

    .payment-method-description {
        color: #cbd5e0; /* Improved contrast for descriptions in dark mode */
    }

    /* USDT card - Black background with white text in dark mode */
    .payment-method-card.active {
        background: #212529 !important; /* Black background for USDT card in dark mode */
        border-color: #495057 !important;
    }

    .payment-method-card.active .payment-method-title {
        color: #ffffff !important; /* Pure white text for maximum contrast on black background */
        font-weight: 700;
    }

    .payment-method-card.active .payment-method-description {
        color: #f8f9fa !important; /* Light gray text for excellent readability on black background */
        font-weight: 600;
    }

    .provider-logo {
        background: #4a5568;
        color: #f7fafc; /* High contrast white for provider text */
        border-color: #4a5568;
        font-weight: 600; /* Maintain bold weight in dark mode */
    }

    .coming-soon-subtitle {
        color: #cbd5e0; /* Better contrast for subtitle in dark mode */
    }

    /* Mobile money card - White background with black text in dark mode */
    .payment-method-card.disabled {
        background: #ffffff !important; /* White background for mobile money card in dark mode */
        border-color: #dee2e6 !important;
    }

    .payment-method-card.disabled .payment-method-title {
        color: #212529 !important; /* Black text for maximum contrast on white background */
        font-weight: 700; /* Bold weight for enhanced visibility */
    }

    .payment-method-card.disabled .payment-method-description {
        color: #495057 !important; /* Dark gray text for excellent readability on white background */
        font-weight: 600; /* Medium-bold weight for better visibility */
    }

    /* Dark mode overlay background - Light overlay on white card */
    .payment-method-card.disabled .coming-soon-overlay {
        background: rgba(248, 249, 250, 0.98) !important; /* Light overlay on white card in dark mode */
    }

    .payment-method-card.disabled .coming-soon-subtitle {
        color: #495057 !important; /* Dark text for subtitle on light overlay */
    }

    /* Provider logos maintain their brand colors in dark mode */
    .provider-logo.mtn {
        background: linear-gradient(135deg, #ffcc00, #ffd700);
        color: #1a1a1a; /* Keep dark text on yellow background */
    }

    .provider-logo.vodafone {
        background: linear-gradient(135deg, #c41e3a, #e60000);
        color: #ffffff; /* Keep white text on red background */
    }

    .provider-logo.airteltigo {
        background: linear-gradient(135deg, #c41e3a, #ed1c24);
        color: #ffffff; /* Keep white text on red background */
    }
}
