<?php
// Set page title
$page_title = 'Support';

// Include header
require_once 'includes/header.php';
require_once 'includes/email_functions.php';

// Initialize variables
$category = $subject = $message = '';
$category_err = $subject_err = $message_err = $file_err = '';
$success_message = '';
$error_message = '';

// Process form submission
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['submit_ticket'])) {
    // Validate category
    if (empty(trim($_POST['category']))) {
        $category_err = 'Please select a category.';
    } else {
        $category = sanitize($_POST['category']);
    }

    // Validate subject
    if (empty(trim($_POST['subject']))) {
        $subject_err = 'Please enter a subject.';
    } else {
        $subject = sanitize($_POST['subject']);
    }

    // Validate message
    if (empty(trim($_POST['message']))) {
        $message_err = 'Please enter your message.';
    } else {
        $message = sanitize($_POST['message']);
    }

    // Process file upload if provided
    $attachment = null;
    if (isset($_FILES['attachment']) && $_FILES['attachment']['error'] != UPLOAD_ERR_NO_FILE) {
        $upload_result = upload_file($_FILES['attachment'], 'uploads/support/');

        if (!$upload_result['success']) {
            $file_err = $upload_result['message'];
        } else {
            $attachment = $upload_result['filename'];
        }
    }

    // If no errors, process the form
    if (empty($category_err) && empty($subject_err) && empty($message_err) && empty($file_err)) {
        // Generate unique ticket number
        $ticket_number = 'TKT-' . strtoupper(substr(md5(uniqid(rand(), true)), 0, 8));

        // Start transaction
        $db->beginTransaction();

        try {
            // Insert support ticket
            $query = "INSERT INTO support_tickets (user_id, ticket_number, category, subject, message, attachment)
                      VALUES (:user_id, :ticket_number, :category, :subject, :message, :attachment)";
            $stmt = $db->prepare($query);

            $stmt->bindParam(':user_id', $user_id);
            $stmt->bindParam(':ticket_number', $ticket_number);
            $stmt->bindParam(':category', $category);
            $stmt->bindParam(':subject', $subject);
            $stmt->bindParam(':message', $message);
            $stmt->bindParam(':attachment', $attachment);

            $stmt->execute();

            // Create notification for user
            create_notification(
                $user_id,
                'Support Ticket Created',
                "Your support ticket #$ticket_number has been created successfully. We'll respond as soon as possible.",
                'support',
                $db
            );

            // Get admin users to notify
            $query = "SELECT id, email FROM users WHERE role = 'admin'";
            $stmt = $db->prepare($query);
            $stmt->execute();
            $admins = $stmt->fetchAll(PDO::FETCH_OBJ);

            // Send email notification to admins
            foreach ($admins as $admin) {
                // Create notification for admin
                create_notification(
                    $admin->id,
                    'New Support Ticket',
                    "A new support ticket #$ticket_number has been submitted by {$_SESSION['user_name']}.",
                    'support',
                    $db
                );

                // In a production environment, you would send an email to the admin
                // For now, we'll just log it
                error_log("Email would be sent to admin: {$admin->email}");
            }

            // Commit transaction
            $db->commit();

            // Set success message
            $success_message = "Your support request has been submitted successfully. Your reference number is: $ticket_number";

            // Clear form data
            $category = $subject = $message = '';

        } catch (Exception $e) {
            // Rollback transaction on error
            $db->rollBack();
            $error_message = 'An error occurred while submitting your ticket. Please try again.';
            error_log("Error creating support ticket: " . $e->getMessage());
        }
    }
}

// Get user's support tickets
$query = "SELECT * FROM support_tickets WHERE user_id = :user_id ORDER BY created_at DESC";
$stmt = $db->prepare($query);
$stmt->bindParam(':user_id', $user_id);
$stmt->execute();
$tickets = $stmt->fetchAll(PDO::FETCH_OBJ);
?>

<div class="container-fluid">
    <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
        <h1 class="h2">Support</h1>
        <div class="btn-toolbar mb-2 mb-md-0">
            <button type="button" class="btn btn-sm btn-primary" data-toggle="modal" data-target="#newTicketModal">
                <i class="fas fa-plus"></i> New Support Request
            </button>
        </div>
    </div>

    <?php if (!empty($success_message)): ?>
        <div class="alert alert-success"><?php echo $success_message; ?></div>
    <?php endif; ?>

    <?php if (!empty($error_message)): ?>
        <div class="alert alert-danger"><?php echo $error_message; ?></div>
    <?php endif; ?>

    <!-- My Support Requests Section -->
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="mb-0">My Support Requests</h5>
        </div>
        <div class="card-body">
            <?php if (empty($tickets)): ?>
                <div class="text-center py-5">
                    <p class="mb-0">You don't have any support requests yet.</p>
                    <button type="button" class="btn btn-primary mt-3" data-toggle="modal" data-target="#newTicketModal">
                        Create Your First Support Request
                    </button>
                </div>
            <?php else: ?>
                <div class="table-responsive">
                    <table class="table table-striped table-hover table-mobile-responsive">
                        <thead>
                            <tr>
                                <th>Ticket #</th>
                                <th>Category</th>
                                <th>Subject</th>
                                <th>Status</th>
                                <th>Created</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($tickets as $ticket): ?>
                                <tr>
                                    <td><?php echo $ticket->ticket_number; ?></td>
                                    <td><?php echo $ticket->category; ?></td>
                                    <td><?php echo $ticket->subject; ?></td>
                                    <td>
                                        <?php if ($ticket->status == 'open'): ?>
                                            <span class="badge badge-primary">Open</span>
                                        <?php elseif ($ticket->status == 'in_progress'): ?>
                                            <span class="badge badge-warning">In Progress</span>
                                        <?php else: ?>
                                            <span class="badge badge-success">Closed</span>
                                        <?php endif; ?>
                                    </td>
                                    <td><?php echo format_date($ticket->created_at); ?></td>
                                    <td>
                                        <a href="view_ticket.php?id=<?php echo $ticket->id; ?>" class="btn btn-sm btn-info">
                                            <i class="fas fa-eye"></i> View
                                        </a>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- New Ticket Modal -->
<div class="modal fade" id="newTicketModal" tabindex="-1" role="dialog" aria-labelledby="newTicketModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="newTicketModalLabel">Create New Support Request</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form action="<?php echo htmlspecialchars($_SERVER["PHP_SELF"]); ?>" method="post" enctype="multipart/form-data">
                <div class="modal-body">
                    <div class="form-group">
                        <label for="category">Issue Category</label>
                        <select name="category" id="category" class="form-control <?php echo (!empty($category_err)) ? 'is-invalid' : ''; ?>">
                            <option value="">-- Select Category --</option>
                            <option value="Account Issues" <?php echo ($category == 'Account Issues') ? 'selected' : ''; ?>>Account Issues</option>
                            <option value="Payment Problems" <?php echo ($category == 'Payment Problems') ? 'selected' : ''; ?>>Payment Problems</option>
                            <option value="Technical Support" <?php echo ($category == 'Technical Support') ? 'selected' : ''; ?>>Technical Support</option>
                            <option value="Pledge Issues" <?php echo ($category == 'Pledge Issues') ? 'selected' : ''; ?>>Pledge Issues</option>
                            <option value="Other" <?php echo ($category == 'Other') ? 'selected' : ''; ?>>Other</option>
                        </select>
                        <span class="invalid-feedback"><?php echo $category_err; ?></span>
                    </div>

                    <div class="form-group">
                        <label for="subject">Subject</label>
                        <input type="text" name="subject" id="subject" class="form-control <?php echo (!empty($subject_err)) ? 'is-invalid' : ''; ?>" value="<?php echo $subject; ?>">
                        <span class="invalid-feedback"><?php echo $subject_err; ?></span>
                    </div>

                    <div class="form-group">
                        <label for="message">Message</label>
                        <textarea name="message" id="message" class="form-control <?php echo (!empty($message_err)) ? 'is-invalid' : ''; ?>" rows="5"><?php echo $message; ?></textarea>
                        <span class="invalid-feedback"><?php echo $message_err; ?></span>
                    </div>

                    <div class="form-group">
                        <label for="attachment">Attachment (Optional)</label>
                        <div class="custom-file">
                            <input type="file" name="attachment" class="custom-file-input <?php echo (!empty($file_err)) ? 'is-invalid' : ''; ?>" id="attachment">
                            <label class="custom-file-label" for="attachment">Choose file</label>
                            <span class="invalid-feedback"><?php echo $file_err; ?></span>
                        </div>
                        <small class="form-text text-muted">Accepted formats: JPG, PNG, PDF. Max size: 20MB</small>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                    <button type="submit" name="submit_ticket" class="btn btn-primary">Submit Ticket</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
// Update file input label with selected filename
$(document).ready(function() {
    $('.custom-file-input').on('change', function() {
        var fileName = $(this).val().split('\\').pop();
        $(this).next('.custom-file-label').html(fileName);
    });
});
</script>

<?php
// Include footer
require_once 'includes/footer.php';
?>
