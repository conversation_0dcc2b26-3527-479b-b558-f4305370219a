<?php
/**
 * SEO Helper Functions
 *
 * This file contains functions to help with SEO optimization.
 */

/**
 * Generate meta tags for SEO
 *
 * @param array $meta Array of meta data
 * @return string HTML meta tags
 */
function generate_meta_tags($meta = []) {
    // Default values
    $defaults = [
        'title' => SITE_NAME,
        'description' => 'P2P Donate is a peer-to-peer donation platform that facilitates direct transfers between users in Ghana. Make and receive pledges securely.',
        'keywords' => 'P2P donate, peer to peer donations, Ghana donations, direct transfers, pledge system',
        'canonical' => SITE_URL . $_SERVER['REQUEST_URI'],
        'image' => SITE_URL . '/assets/p2p-donate-social.jpg',
        'type' => 'website',
    ];

    // Merge defaults with provided meta
    $meta = array_merge($defaults, $meta);

    // Start building meta tags
    $output = '';

    // Basic meta tags
    $output .= '<meta name="description" content="' . htmlspecialchars($meta['description']) . '">' . PHP_EOL;
    $output .= '<meta name="keywords" content="' . htmlspecialchars($meta['keywords']) . '">' . PHP_EOL;

    // Canonical URL
    $output .= '<link rel="canonical" href="' . htmlspecialchars($meta['canonical']) . '">' . PHP_EOL;

    // Open Graph tags
    $output .= '<meta property="og:title" content="' . htmlspecialchars($meta['title']) . '">' . PHP_EOL;
    $output .= '<meta property="og:description" content="' . htmlspecialchars($meta['description']) . '">' . PHP_EOL;
    $output .= '<meta property="og:image" content="' . htmlspecialchars($meta['image']) . '">' . PHP_EOL;
    $output .= '<meta property="og:url" content="' . htmlspecialchars($meta['canonical']) . '">' . PHP_EOL;
    $output .= '<meta property="og:type" content="' . htmlspecialchars($meta['type']) . '">' . PHP_EOL;
    $output .= '<meta property="og:site_name" content="' . htmlspecialchars(SITE_NAME) . '">' . PHP_EOL;

    // Twitter Card tags
    $output .= '<meta name="twitter:card" content="summary_large_image">' . PHP_EOL;
    $output .= '<meta name="twitter:title" content="' . htmlspecialchars($meta['title']) . '">' . PHP_EOL;
    $output .= '<meta name="twitter:description" content="' . htmlspecialchars($meta['description']) . '">' . PHP_EOL;
    $output .= '<meta name="twitter:image" content="' . htmlspecialchars($meta['image']) . '">' . PHP_EOL;

    return $output;
}

/**
 * Generate JSON-LD structured data
 *
 * @param string $type Type of structured data
 * @param array $data Data for structured data
 * @return string JSON-LD script tag
 */
function generate_structured_data($type = 'Organization', $data = []) {
    $output = '';

    // Default organization data
    if ($type == 'Organization') {
        $defaults = [
            '@context' => 'https://schema.org',
            '@type' => 'Organization',
            'name' => SITE_NAME,
            'url' => SITE_URL,
            'logo' => SITE_URL . '/assets/img/p2p_donate_logo.png',
            'description' => 'P2P Donate is a peer-to-peer donation platform that facilitates direct transfers between users in Ghana.',
            'contactPoint' => [
                '@type' => 'ContactPoint',
                'email' => SITE_EMAIL,
                'contactType' => 'customer service'
            ]
        ];

        // Merge defaults with provided data
        $data = array_merge($defaults, $data);
    }

    // Default website data
    elseif ($type == 'WebSite') {
        $defaults = [
            '@context' => 'https://schema.org',
            '@type' => 'WebSite',
            'name' => SITE_NAME,
            'url' => SITE_URL,
            'potentialAction' => [
                '@type' => 'SearchAction',
                'target' => SITE_URL . '/search?q={search_term_string}',
                'query-input' => 'required name=search_term_string'
            ]
        ];

        // Merge defaults with provided data
        $data = array_merge($defaults, $data);
    }

    // Generate JSON-LD script
    $output .= '<script type="application/ld+json">' . PHP_EOL;
    $output .= json_encode($data, JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES);
    $output .= PHP_EOL . '</script>' . PHP_EOL;

    return $output;
}

/**
 * Generate favicon links
 *
 * @return string HTML favicon links
 */
function generate_favicon_links() {
    $output = '';

    // Favicon links
    $output .= '<link rel="apple-touch-icon" sizes="180x180" href="' . SITE_URL . '/assets/apple-touch-icon.png">' . PHP_EOL;
    $output .= '<link rel="icon" type="image/png" sizes="32x32" href="' . SITE_URL . '/assets/favicon-32x32.png">' . PHP_EOL;
    $output .= '<link rel="icon" type="image/png" sizes="16x16" href="' . SITE_URL . '/assets/favicon-16x16.png">' . PHP_EOL;
    $output .= '<link rel="manifest" href="' . SITE_URL . '/assets/site.webmanifest">' . PHP_EOL;

    return $output;
}
?>
