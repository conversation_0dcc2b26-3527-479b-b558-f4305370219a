<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Enhanced Announcements Test - P2P Donate</title>
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.1/css/all.min.css">
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="stylesheet" href="assets/css/dark-mode.css">
    <link rel="stylesheet" href="assets/css/enhanced-announcements.css">
    <style>
        .test-container {
            margin-top: 20px;
            padding: 20px;
        }
        .mode-toggle {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
        }
    </style>
</head>
<body>
    <div class="mode-toggle">
        <button class="btn btn-primary" onclick="toggleDarkMode()">
            <i class="fas fa-moon" id="mode-icon"></i>
            Toggle Dark Mode
        </button>
    </div>

    <div class="container test-container">
        <div class="row">
            <div class="col-12">
                <h1 class="mb-4">Enhanced Announcements System Test</h1>
                <p class="lead">Testing all enhanced announcement features including video embedding, link previews, priority levels, and improved visibility.</p>
            </div>
        </div>

        <!-- Urgent Pinned Announcement with New Layout -->
        <div class="enhanced-announcement-container mb-4 pinned-announcement">
            <div class="announcement-card alert-danger alert-dismissible fade show enhanced-announcement"
                 role="alert"
                 data-priority="urgent"
                 style="border-left: 5px solid #dc3545;">

                <!-- Header Section: Title, Priority Badges, and Pin Status -->
                <div class="announcement-header-section">
                    <div class="announcement-header d-flex align-items-center">
                        <div class="announcement-icon mr-3">
                            <i class="fas fa-exclamation-triangle fa-2x" style="color: #dc3545;"></i>
                        </div>
                        <div class="flex-grow-1">
                            <div class="d-flex justify-content-between align-items-start">
                                <div class="announcement-title-section">
                                    <h4 class="announcement-title mb-1">Urgent System Maintenance Notice</h4>
                                    <div class="announcement-meta">
                                        <small class="text-muted">
                                            <i class="fas fa-clock"></i> Posted: Dec 10, 2024 at 3:45 PM
                                        </small>
                                    </div>
                                </div>
                                <div class="announcement-badges">
                                    <span class="badge badge-warning mr-2">
                                        <i class="fas fa-thumbtack"></i> Pinned
                                    </span>
                                    <span class="badge badge-danger">
                                        Urgent
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Content Section: Announcement Message/Description -->
                <div class="announcement-content-section">
                    <div class="announcement-message">
                        We will be performing critical system maintenance on Sunday, December 15th from 2:00 AM to 6:00 AM GMT. During this time, the platform will be temporarily unavailable. Please plan your activities accordingly.
                    </div>
                </div>

                <!-- Media Section: Videos and Images at the Bottom -->
                <div class="announcement-media-section">
                    <div class="media-separator"></div>

                    <div class="media-item video-embed-item">
                        <div class="media-label">
                            <i class="fab fa-youtube"></i> Featured Video
                        </div>
                        <div class="media-content">
                            <div class="video-embed-container">
                                <div class="embed-responsive embed-responsive-16by9">
                                    <iframe class="embed-responsive-item"
                                            src="https://www.youtube.com/embed/dQw4w9WgXcQ?rel=0&modestbranding=1&showinfo=0"
                                            title="System Maintenance Explanation"
                                            frameborder="0"
                                            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                                            allowfullscreen>
                                    </iframe>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Footer Section -->
                <div class="announcement-footer-section">
                    <div class="pinned-notice">
                        <small class="text-muted">
                            <i class="fas fa-thumbtack"></i> This announcement is pinned and will remain visible
                        </small>
                    </div>
                </div>

                <!-- Close Button -->
                <button type="button" class="close announcement-close" data-dismiss="alert" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
        </div>

        <!-- Normal Announcement with Link Previews -->
        <div class="enhanced-announcement-container mb-4">
            <div class="alert alert-success alert-dismissible fade show announcement-alert enhanced-announcement"
                 role="alert"
                 data-priority="normal"
                 style="border-left: 5px solid #28a745;">

                <div class="announcement-header d-flex align-items-center mb-2">
                    <div class="announcement-icon mr-3">
                        <i class="fas fa-bullhorn fa-2x" style="color: #28a745;"></i>
                    </div>
                    <div class="flex-grow-1">
                        <div class="d-flex justify-content-between align-items-center">
                            <h4 class="alert-heading mb-0">New Features Released!</h4>
                            <div class="announcement-badges">
                                <span class="badge badge-success">
                                    Normal
                                </span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="announcement-content">
                    <p class="announcement-message">We're excited to announce new features including enhanced video support and improved user experience. Check out our blog post for more details: https://blog.p2pdonate.com/new-features</p>

                    <!-- Link Previews -->
                    <div class="announcement-link-previews mb-3">
                        <div class="link-preview-card">
                            <div class="link-preview-image">
                                <img src="https://via.placeholder.com/120x80/28a745/ffffff?text=Blog" alt="Preview image" loading="lazy">
                            </div>
                            <div class="link-preview-content">
                                <h6 class="link-preview-title">New Features Released - P2P Donate Blog</h6>
                                <p class="link-preview-description">Discover the latest enhancements to our platform including video embedding, improved announcements, and better user experience features.</p>
                                <small class="link-preview-site">blog.p2pdonate.com</small>
                                <a href="https://blog.p2pdonate.com/new-features" target="_blank" rel="noopener noreferrer" class="link-preview-url">
                                    <i class="fas fa-external-link-alt"></i> Visit Link
                                </a>
                            </div>
                        </div>
                    </div>

                    <div class="announcement-footer d-flex justify-content-between align-items-center">
                        <small class="text-muted">
                            <i class="fas fa-clock"></i> Posted: Dec 12, 2024 at 10:30 AM
                        </small>
                    </div>
                </div>

                <button type="button" class="close announcement-close" data-dismiss="alert" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
        </div>

        <!-- Info Announcement with Image -->
        <div class="enhanced-announcement-container mb-4">
            <div class="alert alert-info alert-dismissible fade show announcement-alert enhanced-announcement"
                 role="alert"
                 data-priority="info"
                 style="border-left: 5px solid #17a2b8;">

                <div class="announcement-header d-flex align-items-center mb-2">
                    <div class="announcement-icon mr-3">
                        <i class="fas fa-info-circle fa-2x" style="color: #17a2b8;"></i>
                    </div>
                    <div class="flex-grow-1">
                        <div class="d-flex justify-content-between align-items-center">
                            <h4 class="alert-heading mb-0">Community Guidelines Update</h4>
                            <div class="announcement-badges">
                                <span class="badge badge-info">
                                    Info
                                </span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="announcement-content">
                    <p class="announcement-message">We've updated our community guidelines to ensure a better experience for all users. Please take a moment to review the changes.</p>

                    <!-- Image Display -->
                    <div class="announcement-media mb-3">
                        <div class="announcement-image-container">
                            <img src="https://via.placeholder.com/600x300/17a2b8/ffffff?text=Community+Guidelines"
                                 class="announcement-image img-fluid rounded shadow-sm"
                                 alt="Community Guidelines"
                                 onclick="openImageModal(this.src)">
                        </div>
                    </div>

                    <div class="announcement-footer d-flex justify-content-between align-items-center">
                        <small class="text-muted">
                            <i class="fas fa-clock"></i> Posted: Dec 8, 2024 at 2:15 PM
                        </small>
                    </div>
                </div>

                <button type="button" class="close announcement-close" data-dismiss="alert" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
        </div>

        <!-- Test Summary -->
        <div class="row mt-5">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">Enhanced Announcements Features Tested</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>✅ Features Implemented:</h6>
                                <ul>
                                    <li>Video URL embedding (YouTube/Vimeo)</li>
                                    <li>Link preview generation</li>
                                    <li>Priority levels (Urgent, Normal, Info)</li>
                                    <li>Pinned announcements</li>
                                    <li>Enhanced visual styling</li>
                                    <li>Animation effects</li>
                                    <li>Dark mode compatibility</li>
                                    <li>Responsive design</li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <h6>🎨 Visual Enhancements:</h6>
                                <ul>
                                    <li>Priority-based color coding</li>
                                    <li>Improved typography and spacing</li>
                                    <li>Hover effects and animations</li>
                                    <li>Better badge and icon styling</li>
                                    <li>Enhanced media display</li>
                                    <li>Professional link previews</li>
                                    <li>Smooth transitions</li>
                                    <li>Mobile-optimized layout</li>
                                </ul>
                            </div>
                        </div>
                        <div class="mt-3">
                            <a href="admin/announcements.php" class="btn btn-primary">
                                <i class="fas fa-cog"></i> Test Admin Panel
                            </a>
                            <a href="dashboard.php" class="btn btn-success">
                                <i class="fas fa-tachometer-alt"></i> View Dashboard
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Image Modal -->
    <div id="imageModal" class="image-modal" style="display: none; position: fixed; z-index: 1000; left: 0; top: 0; width: 100%; height: 100%; background-color: rgba(0,0,0,0.9);">
        <span class="image-modal-close" onclick="closeImageModal()" style="position: absolute; top: 15px; right: 35px; color: #f1f1f1; font-size: 40px; font-weight: bold; cursor: pointer;">&times;</span>
        <img class="image-modal-content" id="modalImage" style="margin: auto; display: block; width: 80%; max-width: 700px; margin-top: 50px;">
    </div>

    <script src="https://code.jquery.com/jquery-3.5.1.min.js"></script>
    <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>
    <script>
        function toggleDarkMode() {
            const body = document.body;
            const icon = document.getElementById('mode-icon');

            if (body.classList.contains('dark-mode')) {
                body.classList.remove('dark-mode');
                icon.classList.remove('fa-sun');
                icon.classList.add('fa-moon');
                localStorage.setItem('theme', 'light');
            } else {
                body.classList.add('dark-mode');
                icon.classList.remove('fa-moon');
                icon.classList.add('fa-sun');
                localStorage.setItem('theme', 'dark');
            }
        }

        function openImageModal(src) {
            document.getElementById('imageModal').style.display = 'block';
            document.getElementById('modalImage').src = src;
        }

        function closeImageModal() {
            document.getElementById('imageModal').style.display = 'none';
        }

        // Initialize dark mode based on saved preference
        document.addEventListener('DOMContentLoaded', function() {
            const savedTheme = localStorage.getItem('theme');
            const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;

            if (savedTheme === 'dark' || (!savedTheme && prefersDark)) {
                document.body.classList.add('dark-mode');
                document.getElementById('mode-icon').classList.remove('fa-moon');
                document.getElementById('mode-icon').classList.add('fa-sun');
            }
        });
    </script>
</body>
</html>
