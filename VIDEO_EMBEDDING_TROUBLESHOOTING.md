# Video Embedding Troubleshooting Guide

## Problem Fixed: Video Embedding in Enhanced Announcements System

### Issue Description
When an admin creates an announcement with a YouTube video URL in the admin panel (`admin/announcements.php`), the embedded video does not display on the user dashboard (`dashboard.php`) even though the announcement appears correctly.

### Root Cause Analysis
The issue was in the dashboard query that joins the `notifications` table with the `admin_announcements` table. The original query used unreliable JOIN conditions that could fail to match announcements with their enhanced data.

### Solution Implemented

#### 1. Fixed Dashboard Query
**File:** `dashboard.php`
**Lines:** 63-72

**Before:**
```sql
SELECT n.*, a.image_file, a.video_file, a.video_url, a.priority, a.link_preview_data, a.is_pinned
FROM notifications n
LEFT JOIN admin_announcements a ON n.title = a.title AND n.message = a.message
WHERE n.user_id = :user_id AND n.type = 'system'
```

**After:**
```sql
SELECT a.*
FROM admin_announcements a
INNER JOIN notifications n ON n.title = a.title AND n.message = a.message
WHERE n.user_id = :user_id AND n.type = 'system'
ORDER BY a.is_pinned DESC, a.priority = 'urgent' DESC, a.priority = 'normal' DESC, a.created_at DESC
```

#### 2. Enhanced Error Handling
**File:** `dashboard.php`
**Lines:** 127-134

Added error handling for video embedding:
```php
$video_embed = generate_video_embed($latest_announcement->video_url, $latest_announcement->title);
if ($video_embed) {
    echo $video_embed;
} else {
    echo '<div class="alert alert-warning">Unable to embed video: ' . htmlspecialchars($latest_announcement->video_url) . '</div>';
}
```

#### 3. Added Debug Mode
**File:** `dashboard.php`
**Lines:** 88-91

Added debug output to troubleshoot data retrieval:
```php
if (isset($_GET['debug'])) {
    echo '<div class="alert alert-info"><pre>' . print_r($latest_announcement, true) . '</pre></div>';
}
```

#### 4. Enhanced Admin Panel Display
**File:** `admin/announcements.php`
**Lines:** 378-383

Added video URL display in admin panel:
```php
<?php if (!empty($announcement->video_url)): ?>
<div class="mt-2 mb-2">
    <p class="small text-muted mb-1">Video URL: <?php echo $announcement->video_url; ?></p>
    <?php echo generate_video_embed($announcement->video_url, $announcement->title); ?>
</div>
<?php endif; ?>
```

### Testing Steps

#### 1. Database Verification
Run the database update script:
```bash
php admin/scripts/update_announcements_table.php
```

Expected output:
```
Video URL column added successfully.
Priority column added successfully.
Link preview data column added successfully.
Is pinned column added successfully.
```

#### 2. Function Testing
Access the debug script:
```
http://yoursite.com/debug_video_embedding.php
```

This will test:
- Function availability
- Video URL validation
- Embed generation
- Database structure
- Recent announcements

#### 3. Create Test Announcement
Access the test creation script:
```
http://yoursite.com/create_test_announcement.php
```

This will create a test announcement with a YouTube video.

#### 4. Verify Dashboard Display
Access the dashboard with debug mode:
```
http://yoursite.com/dashboard.php?debug=1
```

This will show the raw announcement data for troubleshooting.

### Expected Behavior

#### 1. Admin Panel
- Video URL field accepts YouTube and Vimeo URLs
- Real-time preview shows embedded video
- Recent announcements list shows video embeds
- Form validation prevents invalid URLs

#### 2. User Dashboard
- Latest announcement displays with embedded video
- Video shows in responsive iframe container
- Priority styling and badges work correctly
- Pinned announcements appear first

#### 3. Video Embedding
- YouTube URLs: `https://www.youtube.com/watch?v=VIDEO_ID`
- Vimeo URLs: `https://vimeo.com/VIDEO_ID`
- Responsive 16:9 aspect ratio
- Proper iframe attributes for security

### Common Issues and Solutions

#### Issue 1: Video Not Displaying
**Symptoms:** Announcement appears but no video
**Solution:** Check if `video_url` column exists and contains data
```sql
SELECT video_url FROM admin_announcements WHERE video_url IS NOT NULL;
```

#### Issue 2: Invalid Video URL Error
**Symptoms:** "Unable to embed video" message
**Solution:** Verify URL format and test with `validate_video_url()` function

#### Issue 3: CSS Not Loading
**Symptoms:** Video appears but styling is broken
**Solution:** Verify `enhanced-announcements.css` is included in header

#### Issue 4: No Announcements Showing
**Symptoms:** No announcements appear on dashboard
**Solution:** Check if user has received notifications
```sql
SELECT * FROM notifications WHERE user_id = USER_ID AND type = 'system';
```

### File Changes Summary

#### Modified Files:
1. `dashboard.php` - Fixed query and added error handling
2. `admin/announcements.php` - Enhanced admin display
3. `includes/announcement_functions.php` - Video embedding functions
4. `assets/css/enhanced-announcements.css` - Styling for video embeds

#### New Files:
1. `debug_video_embedding.php` - Comprehensive testing script
2. `create_test_announcement.php` - Test announcement creation
3. `VIDEO_EMBEDDING_TROUBLESHOOTING.md` - This guide

### Database Schema Changes

#### New Columns in `admin_announcements`:
```sql
ALTER TABLE admin_announcements ADD COLUMN video_url VARCHAR(500);
ALTER TABLE admin_announcements ADD COLUMN priority ENUM('urgent', 'normal', 'info') DEFAULT 'normal';
ALTER TABLE admin_announcements ADD COLUMN link_preview_data JSON;
ALTER TABLE admin_announcements ADD COLUMN is_pinned BOOLEAN DEFAULT FALSE;
```

### Security Considerations

#### 1. URL Validation
- Only YouTube and Vimeo URLs are accepted
- URLs are validated with regex patterns
- Output is properly escaped with `htmlspecialchars()`

#### 2. Iframe Security
- `frameborder="0"` for modern browsers
- Proper `allow` attributes for permissions
- `allowfullscreen` for user experience

#### 3. Input Sanitization
- All form inputs are sanitized
- Database queries use prepared statements
- XSS prevention in output

### Performance Considerations

#### 1. Database Queries
- Efficient JOIN operations
- Proper indexing on frequently queried columns
- Limited result sets with LIMIT clauses

#### 2. Video Embedding
- Lazy loading for video iframes
- Responsive design for mobile devices
- Minimal JavaScript for functionality

#### 3. CSS Optimization
- Efficient selectors
- Minimal animations
- Mobile-first responsive design

### Browser Compatibility

#### Supported Browsers:
- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+
- Mobile browsers (iOS Safari, Chrome Mobile)

#### Features Used:
- CSS Grid and Flexbox
- HTML5 video elements
- Bootstrap 4 components
- FontAwesome icons

### Maintenance Notes

#### Regular Tasks:
1. Monitor video embed performance
2. Update video platform support as needed
3. Review and update security measures
4. Test responsive design on new devices

#### Future Enhancements:
1. Support for additional video platforms
2. Video thumbnail generation
3. Video analytics integration
4. Automatic video transcription

### Support Information

For additional support or issues:
1. Check the debug script output
2. Review browser console for JavaScript errors
3. Verify database schema is up to date
4. Test with different video URLs
5. Check CSS file loading in browser developer tools

### Version Information
- Implementation Date: December 2024
- PHP Version: 7.4+
- MySQL Version: 5.7+
- Bootstrap Version: 4.5.2
- FontAwesome Version: 5.15.1
