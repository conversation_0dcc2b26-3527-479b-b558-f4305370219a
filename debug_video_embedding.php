<?php
// Debug script for video embedding functionality
require_once 'config/config.php';
require_once 'includes/functions.php';
require_once 'includes/announcement_functions.php';
require_once 'database/db_connect.php';

// Start session
start_session();

// Check if user is logged in
if (!is_logged_in()) {
    redirect('login.php');
}

$database = new Database();
$db = $database->getConnection();
$user_id = $_SESSION['user_id'];

// Test video URLs
$test_youtube_url = 'https://www.youtube.com/watch?v=dQw4w9WgXcQ';
$test_vimeo_url = 'https://vimeo.com/148751763';

echo "<!DOCTYPE html>";
echo "<html lang='en'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<meta name='viewport' content='width=device-width, initial-scale=1.0'>";
echo "<title>Video Embedding Debug - P2P Donate</title>";
echo "<link rel='stylesheet' href='https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css'>";
echo "<link rel='stylesheet' href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.1/css/all.min.css'>";
echo "<link rel='stylesheet' href='assets/css/enhanced-announcements.css'>";
echo "</head>";
echo "<body>";

echo "<div class='container mt-4'>";
echo "<h1>Video Embedding Debug</h1>";

// Test 1: Check if announcement functions are loaded
echo "<div class='card mb-4'>";
echo "<div class='card-header'><h5>Test 1: Function Availability</h5></div>";
echo "<div class='card-body'>";

if (function_exists('validate_video_url')) {
    echo "<p class='text-success'><i class='fas fa-check'></i> validate_video_url() function is available</p>";
} else {
    echo "<p class='text-danger'><i class='fas fa-times'></i> validate_video_url() function is NOT available</p>";
}

if (function_exists('generate_video_embed')) {
    echo "<p class='text-success'><i class='fas fa-check'></i> generate_video_embed() function is available</p>";
} else {
    echo "<p class='text-danger'><i class='fas fa-times'></i> generate_video_embed() function is NOT available</p>";
}

echo "</div></div>";

// Test 2: Validate video URLs
echo "<div class='card mb-4'>";
echo "<div class='card-header'><h5>Test 2: Video URL Validation</h5></div>";
echo "<div class='card-body'>";

$youtube_validation = validate_video_url($test_youtube_url);
$vimeo_validation = validate_video_url($test_vimeo_url);

echo "<h6>YouTube URL Test:</h6>";
echo "<p>URL: <code>$test_youtube_url</code></p>";
if ($youtube_validation) {
    echo "<p class='text-success'><i class='fas fa-check'></i> Valid YouTube URL detected</p>";
    echo "<pre>" . print_r($youtube_validation, true) . "</pre>";
} else {
    echo "<p class='text-danger'><i class='fas fa-times'></i> YouTube URL validation failed</p>";
}

echo "<h6>Vimeo URL Test:</h6>";
echo "<p>URL: <code>$test_vimeo_url</code></p>";
if ($vimeo_validation) {
    echo "<p class='text-success'><i class='fas fa-check'></i> Valid Vimeo URL detected</p>";
    echo "<pre>" . print_r($vimeo_validation, true) . "</pre>";
} else {
    echo "<p class='text-danger'><i class='fas fa-times'></i> Vimeo URL validation failed</p>";
}

echo "</div></div>";

// Test 3: Generate video embeds
echo "<div class='card mb-4'>";
echo "<div class='card-header'><h5>Test 3: Video Embed Generation</h5></div>";
echo "<div class='card-body'>";

echo "<h6>YouTube Embed:</h6>";
$youtube_embed = generate_video_embed($test_youtube_url, 'Test YouTube Video');
if ($youtube_embed) {
    echo "<p class='text-success'><i class='fas fa-check'></i> YouTube embed generated successfully</p>";
    echo $youtube_embed;
} else {
    echo "<p class='text-danger'><i class='fas fa-times'></i> YouTube embed generation failed</p>";
}

echo "<h6>Vimeo Embed:</h6>";
$vimeo_embed = generate_video_embed($test_vimeo_url, 'Test Vimeo Video');
if ($vimeo_embed) {
    echo "<p class='text-success'><i class='fas fa-check'></i> Vimeo embed generated successfully</p>";
    echo $vimeo_embed;
} else {
    echo "<p class='text-danger'><i class='fas fa-times'></i> Vimeo embed generation failed</p>";
}

echo "</div></div>";

// Test 4: Check database structure
echo "<div class='card mb-4'>";
echo "<div class='card-header'><h5>Test 4: Database Structure</h5></div>";
echo "<div class='card-body'>";

try {
    $query = "SHOW COLUMNS FROM admin_announcements";
    $stmt = $db->prepare($query);
    $stmt->execute();
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $required_columns = ['video_url', 'priority', 'link_preview_data', 'is_pinned'];
    $found_columns = array_column($columns, 'Field');
    
    foreach ($required_columns as $col) {
        if (in_array($col, $found_columns)) {
            echo "<p class='text-success'><i class='fas fa-check'></i> Column '$col' exists</p>";
        } else {
            echo "<p class='text-danger'><i class='fas fa-times'></i> Column '$col' is missing</p>";
        }
    }
    
    echo "<h6>All Columns:</h6>";
    echo "<ul>";
    foreach ($columns as $column) {
        echo "<li><strong>{$column['Field']}</strong> - {$column['Type']}</li>";
    }
    echo "</ul>";
    
} catch (Exception $e) {
    echo "<p class='text-danger'><i class='fas fa-times'></i> Database error: " . $e->getMessage() . "</p>";
}

echo "</div></div>";

// Test 5: Check recent announcements
echo "<div class='card mb-4'>";
echo "<div class='card-header'><h5>Test 5: Recent Announcements</h5></div>";
echo "<div class='card-body'>";

try {
    $query = "SELECT * FROM admin_announcements ORDER BY created_at DESC LIMIT 5";
    $stmt = $db->prepare($query);
    $stmt->execute();
    $announcements = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($announcements)) {
        echo "<p class='text-info'><i class='fas fa-info-circle'></i> No announcements found</p>";
    } else {
        echo "<p class='text-success'><i class='fas fa-check'></i> Found " . count($announcements) . " announcements</p>";
        
        foreach ($announcements as $announcement) {
            echo "<div class='border p-3 mb-2'>";
            echo "<h6>{$announcement['title']}</h6>";
            echo "<p>{$announcement['message']}</p>";
            
            if (!empty($announcement['video_url'])) {
                echo "<p class='text-success'><strong>Video URL:</strong> {$announcement['video_url']}</p>";
            } else {
                echo "<p class='text-muted'>No video URL</p>";
            }
            
            if (!empty($announcement['priority'])) {
                echo "<p><strong>Priority:</strong> {$announcement['priority']}</p>";
            }
            
            if (!empty($announcement['is_pinned'])) {
                echo "<p><strong>Pinned:</strong> " . ($announcement['is_pinned'] ? 'Yes' : 'No') . "</p>";
            }
            
            echo "<small class='text-muted'>Created: {$announcement['created_at']}</small>";
            echo "</div>";
        }
    }
    
} catch (Exception $e) {
    echo "<p class='text-danger'><i class='fas fa-times'></i> Database error: " . $e->getMessage() . "</p>";
}

echo "</div></div>";

// Test 6: Check user's latest announcement
echo "<div class='card mb-4'>";
echo "<div class='card-header'><h5>Test 6: User's Latest Announcement</h5></div>";
echo "<div class='card-body'>";

try {
    // Use the same query as dashboard
    $query = "SELECT a.*
              FROM admin_announcements a
              INNER JOIN notifications n ON n.title = a.title AND n.message = a.message
              WHERE n.user_id = :user_id AND n.type = 'system'
              ORDER BY a.is_pinned DESC, a.priority = 'urgent' DESC, a.priority = 'normal' DESC, a.created_at DESC 
              LIMIT 1";
    $stmt = $db->prepare($query);
    $stmt->bindParam(':user_id', $user_id);
    $stmt->execute();
    $latest_announcement = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($latest_announcement) {
        echo "<p class='text-success'><i class='fas fa-check'></i> Found latest announcement for user</p>";
        echo "<div class='border p-3'>";
        echo "<h6>{$latest_announcement['title']}</h6>";
        echo "<p>{$latest_announcement['message']}</p>";
        
        if (!empty($latest_announcement['video_url'])) {
            echo "<p class='text-success'><strong>Video URL:</strong> {$latest_announcement['video_url']}</p>";
            echo "<div class='mt-3'>";
            echo generate_video_embed($latest_announcement['video_url'], $latest_announcement['title']);
            echo "</div>";
        } else {
            echo "<p class='text-muted'>No video URL in this announcement</p>";
        }
        
        echo "</div>";
    } else {
        echo "<p class='text-info'><i class='fas fa-info-circle'></i> No announcements found for this user</p>";
    }
    
} catch (Exception $e) {
    echo "<p class='text-danger'><i class='fas fa-times'></i> Database error: " . $e->getMessage() . "</p>";
}

echo "</div></div>";

echo "<div class='mt-4'>";
echo "<a href='dashboard.php' class='btn btn-primary'>Back to Dashboard</a>";
echo "<a href='admin/announcements.php' class='btn btn-success ml-2'>Admin Announcements</a>";
echo "</div>";

echo "</div>"; // container
echo "</body>";
echo "</html>";
?>
