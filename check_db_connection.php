<?php
// Include configuration
require_once 'config/config.php';

echo "<h1>Database Connection Test</h1>";

// Display database configuration (without password)
echo "<h2>Database Configuration</h2>";
echo "<ul>";
echo "<li>Host: " . DB_HOST . "</li>";
echo "<li>User: " . DB_USER . "</li>";
echo "<li>Database: " . DB_NAME . "</li>";
echo "</ul>";

// Check if MySQL server is running
echo "<h2>MySQL Server Status</h2>";

$connection = @fsockopen(DB_HOST, 3306, $errno, $errstr, 5);
if (!$connection) {
    echo "<div style='background-color: #f8d7da; color: #721c24; padding: 15px; margin: 10px; border-radius: 5px; border: 1px solid #f5c6cb;'>";
    echo "<h3>MySQL Server Not Accessible</h3>";
    echo "<p>Could not connect to MySQL server at " . DB_HOST . ":3306</p>";
    echo "<p>Error: " . $errstr . " (Error code: " . $errno . ")</p>";
    echo "<p>Possible reasons:</p>";
    echo "<ol>";
    echo "<li>MySQL server is not running</li>";
    echo "<li>MySQL server is running on a different host or port</li>";
    echo "<li>Firewall is blocking the connection</li>";
    echo "</ol>";
    echo "<p>Recommendations:</p>";
    echo "<ol>";
    echo "<li>Start the MySQL server if it's not running</li>";
    echo "<li>Check if the host in config/config.php is correct</li>";
    echo "<li>Try using '127.0.0.1' instead of 'localhost' in config/config.php</li>";
    echo "</ol>";
    echo "</div>";
} else {
    echo "<div style='background-color: #d4edda; color: #155724; padding: 15px; margin: 10px; border-radius: 5px; border: 1px solid #c3e6cb;'>";
    echo "<h3>MySQL Server is Running</h3>";
    echo "<p>Successfully connected to MySQL server at " . DB_HOST . ":3306</p>";
    echo "</div>";
    fclose($connection);
    
    // Try to connect to the database
    try {
        $dsn = 'mysql:host=' . DB_HOST . ';dbname=' . DB_NAME;
        $options = array(
            PDO::ATTR_PERSISTENT => true,
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_TIMEOUT => 5 // 5 seconds timeout
        );
        
        $pdo = new PDO($dsn, DB_USER, DB_PASS, $options);
        
        echo "<div style='background-color: #d4edda; color: #155724; padding: 15px; margin: 10px; border-radius: 5px; border: 1px solid #c3e6cb;'>";
        echo "<h3>Database Connection Successful</h3>";
        echo "<p>Successfully connected to database '" . DB_NAME . "'</p>";
        
        // Check if the users table exists
        $stmt = $pdo->query("SHOW TABLES LIKE 'users'");
        if ($stmt->rowCount() > 0) {
            echo "<p>The 'users' table exists in the database.</p>";
            
            // Check if mobile_money_number and mobile_money_name columns exist
            $stmt = $pdo->query("SHOW COLUMNS FROM users LIKE 'mobile_money_number'");
            $mobile_money_number_exists = $stmt->rowCount() > 0;
            
            $stmt = $pdo->query("SHOW COLUMNS FROM users LIKE 'mobile_money_name'");
            $mobile_money_name_exists = $stmt->rowCount() > 0;
            
            if ($mobile_money_number_exists && $mobile_money_name_exists) {
                echo "<p>The 'mobile_money_number' and 'mobile_money_name' columns exist in the 'users' table.</p>";
            } else {
                echo "<p>One or both of the 'mobile_money_number' and 'mobile_money_name' columns do not exist in the 'users' table.</p>";
                echo "<p>You may need to run the script at admin/scripts/add_mobile_money_columns.php to add these columns.</p>";
            }
        } else {
            echo "<p>The 'users' table does not exist in the database. You may need to import the database schema.</p>";
        }
        
        echo "</div>";
    } catch (PDOException $e) {
        echo "<div style='background-color: #f8d7da; color: #721c24; padding: 15px; margin: 10px; border-radius: 5px; border: 1px solid #f5c6cb;'>";
        echo "<h3>Database Connection Failed</h3>";
        echo "<p>Error: " . $e->getMessage() . "</p>";
        echo "<p>Possible reasons:</p>";
        echo "<ol>";
        echo "<li>Database '" . DB_NAME . "' does not exist</li>";
        echo "<li>User '" . DB_USER . "' does not have access to the database</li>";
        echo "<li>Password is incorrect</li>";
        echo "</ol>";
        echo "<p>Recommendations:</p>";
        echo "<ol>";
        echo "<li>Create the database if it doesn't exist</li>";
        echo "<li>Check if the database credentials in config/config.php are correct</li>";
        echo "<li>Import the database schema from database/schema.sql</li>";
        echo "</ol>";
        echo "</div>";
    }
}
?>
