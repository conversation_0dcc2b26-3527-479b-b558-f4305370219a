<?php
/**
 * SEO Functions
 *
 * This file contains functions for generating SEO-related tags and elements
 * such as meta tags, Open Graph tags, Twitter Card tags, and favicon links.
 */

/**
 * Generate meta tags for SEO
 *
 * @param array $meta Array containing meta information
 * @return string HTML meta tags
 */
function generate_meta_tags($meta = []) {
    // Default values
    $defaults = [
        'title' => SITE_NAME,
        'description' => 'P2P Donate is a peer-to-peer donation platform where users can make and receive pledges in Ghana.',
        'keywords' => 'p2p, donate, donation, peer-to-peer, ghana, pledges',
        'canonical' => SITE_URL,
        'type' => 'website',
        'image' => SITE_URL . '/assets/p2p-donate-social.jpg',
        'twitter_card' => 'summary_large_image',
        'twitter_site' => '@p2pdonate',
        'twitter_creator' => '@p2pdonate'
    ];

    // Merge defaults with provided meta data
    $meta = array_merge($defaults, $meta);

    // Start building meta tags
    $output = '';

    // Basic meta tags
    $output .= '<meta name="description" content="' . htmlspecialchars($meta['description']) . '">' . PHP_EOL;
    $output .= '<meta name="keywords" content="' . htmlspecialchars($meta['keywords']) . '">' . PHP_EOL;

    // Canonical URL
    $output .= '<link rel="canonical" href="' . htmlspecialchars($meta['canonical']) . '">' . PHP_EOL;

    // Open Graph meta tags
    $output .= '<meta property="og:title" content="' . htmlspecialchars($meta['title']) . '">' . PHP_EOL;
    $output .= '<meta property="og:description" content="' . htmlspecialchars($meta['description']) . '">' . PHP_EOL;
    $output .= '<meta property="og:url" content="' . htmlspecialchars($meta['canonical']) . '">' . PHP_EOL;
    $output .= '<meta property="og:type" content="' . htmlspecialchars($meta['type']) . '">' . PHP_EOL;
    $output .= '<meta property="og:image" content="' . htmlspecialchars($meta['image']) . '">' . PHP_EOL;
    $output .= '<meta property="og:site_name" content="' . htmlspecialchars(SITE_NAME) . '">' . PHP_EOL;

    // Twitter Card meta tags
    $output .= '<meta name="twitter:card" content="' . htmlspecialchars($meta['twitter_card']) . '">' . PHP_EOL;
    $output .= '<meta name="twitter:site" content="' . htmlspecialchars($meta['twitter_site']) . '">' . PHP_EOL;
    $output .= '<meta name="twitter:creator" content="' . htmlspecialchars($meta['twitter_creator']) . '">' . PHP_EOL;
    $output .= '<meta name="twitter:title" content="' . htmlspecialchars($meta['title']) . '">' . PHP_EOL;
    $output .= '<meta name="twitter:description" content="' . htmlspecialchars($meta['description']) . '">' . PHP_EOL;
    $output .= '<meta name="twitter:image" content="' . htmlspecialchars($meta['image']) . '">' . PHP_EOL;

    return $output;
}

/**
 * Generate favicon links
 *
 * @return string HTML favicon link tags
 */
function generate_favicon_links() {
    $output = '';

    // Basic favicon
    $output .= '<link rel="icon" href="' . SITE_URL . '/assets/favicon.ico" type="image/x-icon">' . PHP_EOL;
    $output .= '<link rel="shortcut icon" href="' . SITE_URL . '/assets/favicon.ico" type="image/x-icon">' . PHP_EOL;

    // Apple Touch icons
    $output .= '<link rel="apple-touch-icon" sizes="180x180" href="' . SITE_URL . '/assets/apple-touch-icon.png">' . PHP_EOL;

    // Android/Chrome icons
    $output .= '<link rel="icon" type="image/png" sizes="32x32" href="' . SITE_URL . '/assets/favicon-32x32.png">' . PHP_EOL;
    $output .= '<link rel="icon" type="image/png" sizes="16x16" href="' . SITE_URL . '/assets/favicon-16x16.png">' . PHP_EOL;

    // Web App Manifest
    $output .= '<link rel="manifest" href="' . SITE_URL . '/assets/site.webmanifest">' . PHP_EOL;

    // Microsoft Tile
    $output .= '<meta name="msapplication-TileColor" content="#2b5797">' . PHP_EOL;
    $output .= '<meta name="msapplication-config" content="' . SITE_URL . '/assets/browserconfig.xml">' . PHP_EOL;

    // Theme Color
    $output .= '<meta name="theme-color" content="#ffffff">' . PHP_EOL;

    return $output;
}

/**
 * Generate structured data for a page
 *
 * @param array $data Array containing structured data information
 * @return string HTML script tag with JSON-LD structured data
 */
function generate_structured_data($data = []) {
    // Default values for organization
    $defaults = [
        '@context' => 'https://schema.org',
        '@type' => 'Organization',
        'name' => SITE_NAME,
        'url' => SITE_URL,
        'logo' => SITE_URL . '/assets/img/logo.png',
        'sameAs' => [
            'https://facebook.com/p2pdonate',
            'https://twitter.com/p2pdonate',
            'https://instagram.com/p2pdonate'
        ]
    ];

    // Merge defaults with provided data
    $data = array_merge($defaults, $data);

    // Generate JSON-LD script
    $output = '<script type="application/ld+json">' . PHP_EOL;
    $output .= json_encode($data, JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES);
    $output .= PHP_EOL . '</script>' . PHP_EOL;

    return $output;
}
?>
