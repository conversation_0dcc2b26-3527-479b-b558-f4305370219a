<?php
// Set page title
$page_title = 'Support Tickets';

// Include configuration
require_once '../config/config.php';
require_once '../includes/functions.php';
require_once '../database/db_connect.php';

// Start session
start_session();

// Check if user is logged in and is admin
if (!is_logged_in() || !is_admin()) {
    redirect('../login.php');
}

// Create database instance
$database = new Database();
$db = $database->getConnection();

// Get user ID
$user_id = $_SESSION['user_id'];

// Process ticket actions
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    if (isset($_POST['update_status'])) {
        $ticket_id = $_POST['ticket_id'];
        $status = $_POST['status'];

        $query = "UPDATE support_tickets SET status = :status, updated_at = NOW() WHERE id = :id";
        $stmt = $db->prepare($query);
        $stmt->bindParam(':status', $status);
        $stmt->bindParam(':id', $ticket_id);

        if ($stmt->execute()) {
            // Get ticket details for notification
            $query = "SELECT ticket_number, user_id FROM support_tickets WHERE id = :id";
            $stmt = $db->prepare($query);
            $stmt->bindParam(':id', $ticket_id);
            $stmt->execute();
            $ticket = $stmt->fetch(PDO::FETCH_OBJ);

            // Create notification for user
            create_notification(
                $ticket->user_id,
                'Support Ticket Updated',
                "Your support ticket #{$ticket->ticket_number} status has been updated to " . ucfirst($status) . ".",
                'support',
                $db
            );

            flash_message('admin_message', 'Ticket status updated successfully.', 'alert alert-success');
        } else {
            flash_message('admin_message', 'Failed to update ticket status.', 'alert alert-danger');
        }
    } elseif (isset($_POST['submit_response'])) {
        $ticket_id = $_POST['ticket_id'];
        $response = sanitize($_POST['response']);

        if (empty($response)) {
            flash_message('admin_message', 'Response cannot be empty.', 'alert alert-danger');
        } else {
            // Start transaction
            $db->beginTransaction();

            try {
                // Update ticket with admin response
                $query = "UPDATE support_tickets SET
                          admin_response = :response,
                          admin_id = :admin_id,
                          status = :status,
                          updated_at = NOW()
                          WHERE id = :id";
                $stmt = $db->prepare($query);
                $stmt->bindParam(':response', $response);
                $stmt->bindParam(':admin_id', $user_id);
                $status = 'in_progress';
                $stmt->bindParam(':status', $status);
                $stmt->bindParam(':id', $ticket_id);
                $stmt->execute();

                // Add admin reply to replies table
                $query = "INSERT INTO support_ticket_replies (ticket_id, user_id, message, is_admin)
                          VALUES (:ticket_id, :user_id, :message, 1)";
                $stmt = $db->prepare($query);
                $stmt->bindParam(':ticket_id', $ticket_id);
                $stmt->bindParam(':user_id', $user_id);
                $stmt->bindParam(':message', $response);
                $stmt->execute();

                // Get ticket details for notification
                $query = "SELECT ticket_number, user_id FROM support_tickets WHERE id = :id";
                $stmt = $db->prepare($query);
                $stmt->bindParam(':id', $ticket_id);
                $stmt->execute();
                $ticket = $stmt->fetch(PDO::FETCH_OBJ);

                // Create notification for user
                create_notification(
                    $ticket->user_id,
                    'Support Ticket Response',
                    "Your support ticket #{$ticket->ticket_number} has received a response from our support team.",
                    'support',
                    $db
                );

                // Commit transaction
                $db->commit();

                flash_message('admin_message', 'Response submitted successfully.', 'alert alert-success');
            } catch (Exception $e) {
                // Rollback transaction on error
                $db->rollBack();
                flash_message('admin_message', 'An error occurred: ' . $e->getMessage(), 'alert alert-danger');
            }
        }
    }

    // Redirect to avoid form resubmission
    redirect('support_tickets.php');
}

// Get support tickets with user information
$query = "SELECT t.*, u.name as user_name, u.email as user_email
          FROM support_tickets t
          JOIN users u ON t.user_id = u.id
          ORDER BY
            CASE
                WHEN t.status = 'open' THEN 1
                WHEN t.status = 'in_progress' THEN 2
                ELSE 3
            END,
            t.created_at DESC";
$stmt = $db->prepare($query);
$stmt->execute();
$tickets = $stmt->fetchAll(PDO::FETCH_OBJ);
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - <?php echo SITE_NAME; ?></title>
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.1/css/all.min.css">
    <link rel="stylesheet" href="../assets/css/admin.css">
    <link rel="stylesheet" href="../assets/css/admin-dark-mode.css">
</head>
<body>
    <?php include 'includes/admin_navbar.php'; ?>

    <div class="container-fluid">
        <div class="row">
            <?php include 'includes/admin_sidebar.php'; ?>

            <!-- Main Content -->
            <main role="main" class="col-md-9 ml-sm-auto col-lg-10 px-md-4 py-4">
    <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
        <h1 class="h2">Support Tickets</h1>
    </div>

    <?php echo flash_message('admin_message'); ?>

    <!-- Support Tickets Table -->
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="mb-0">All Support Tickets</h5>
        </div>
        <div class="card-body">
            <?php if (empty($tickets)): ?>
                <div class="text-center py-5">
                    <p class="mb-0">No support tickets found.</p>
                </div>
            <?php else: ?>
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th>Ticket #</th>
                                <th>User</th>
                                <th>Category</th>
                                <th>Subject</th>
                                <th>Status</th>
                                <th>Created</th>
                                <th>Updated</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($tickets as $ticket): ?>
                                <tr class="<?php echo ($ticket->status == 'open') ? 'table-warning' : ''; ?>">
                                    <td><?php echo $ticket->ticket_number; ?></td>
                                    <td>
                                        <?php echo $ticket->user_name; ?><br>
                                        <small><?php echo $ticket->user_email; ?></small>
                                    </td>
                                    <td><?php echo $ticket->category; ?></td>
                                    <td><?php echo $ticket->subject; ?></td>
                                    <td>
                                        <?php if ($ticket->status == 'open'): ?>
                                            <span class="badge badge-primary">Open</span>
                                        <?php elseif ($ticket->status == 'in_progress'): ?>
                                            <span class="badge badge-warning">In Progress</span>
                                        <?php else: ?>
                                            <span class="badge badge-success">Closed</span>
                                        <?php endif; ?>
                                    </td>
                                    <td><?php echo format_date($ticket->created_at); ?></td>
                                    <td><?php echo format_date($ticket->updated_at); ?></td>
                                    <td>
                                        <div class="btn-group">
                                            <a href="view_ticket.php?id=<?php echo $ticket->id; ?>" class="btn btn-sm btn-info">
                                                <i class="fas fa-eye"></i> View
                                            </a>
                                            <button type="button" class="btn btn-sm btn-secondary dropdown-toggle dropdown-toggle-split" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                                <span class="sr-only">Toggle Dropdown</span>
                                            </button>
                                            <div class="dropdown-menu dropdown-menu-right">
                                                <?php if ($ticket->status != 'open'): ?>
                                                    <form action="<?php echo htmlspecialchars($_SERVER["PHP_SELF"]); ?>" method="post">
                                                        <input type="hidden" name="ticket_id" value="<?php echo $ticket->id; ?>">
                                                        <input type="hidden" name="status" value="open">
                                                        <button type="submit" name="update_status" class="dropdown-item">Mark as Open</button>
                                                    </form>
                                                <?php endif; ?>

                                                <?php if ($ticket->status != 'in_progress'): ?>
                                                    <form action="<?php echo htmlspecialchars($_SERVER["PHP_SELF"]); ?>" method="post">
                                                        <input type="hidden" name="ticket_id" value="<?php echo $ticket->id; ?>">
                                                        <input type="hidden" name="status" value="in_progress">
                                                        <button type="submit" name="update_status" class="dropdown-item">Mark as In Progress</button>
                                                    </form>
                                                <?php endif; ?>

                                                <?php if ($ticket->status != 'closed'): ?>
                                                    <form action="<?php echo htmlspecialchars($_SERVER["PHP_SELF"]); ?>" method="post">
                                                        <input type="hidden" name="ticket_id" value="<?php echo $ticket->id; ?>">
                                                        <input type="hidden" name="status" value="closed">
                                                        <button type="submit" name="update_status" class="dropdown-item">Mark as Closed</button>
                                                    </form>
                                                <?php endif; ?>

                                                <div class="dropdown-divider"></div>

                                                <a href="#" class="dropdown-item" data-toggle="modal" data-target="#responseModal<?php echo $ticket->id; ?>">
                                                    <i class="fas fa-reply"></i> Quick Response
                                                </a>
                                            </div>
                                        </div>
                                    </td>
                                </tr>

                                <!-- Response Modal -->
                                <div class="modal fade" id="responseModal<?php echo $ticket->id; ?>" tabindex="-1" role="dialog" aria-labelledby="responseModalLabel<?php echo $ticket->id; ?>" aria-hidden="true">
                                    <div class="modal-dialog" role="document">
                                        <div class="modal-content">
                                            <div class="modal-header">
                                                <h5 class="modal-title" id="responseModalLabel<?php echo $ticket->id; ?>">
                                                    Respond to Ticket #<?php echo $ticket->ticket_number; ?>
                                                </h5>
                                                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                                    <span aria-hidden="true">&times;</span>
                                                </button>
                                            </div>
                                            <form action="<?php echo htmlspecialchars($_SERVER["PHP_SELF"]); ?>" method="post">
                                                <div class="modal-body">
                                                    <input type="hidden" name="ticket_id" value="<?php echo $ticket->id; ?>">

                                                    <div class="form-group">
                                                        <label for="response<?php echo $ticket->id; ?>">Response</label>
                                                        <textarea name="response" id="response<?php echo $ticket->id; ?>" class="form-control" rows="5" required></textarea>
                                                    </div>
                                                </div>
                                                <div class="modal-footer">
                                                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                                                    <button type="submit" name="submit_response" class="btn btn-primary">Send Response</button>
                                                </div>
                                            </form>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php endif; ?>
        </div>
    </div>
</main>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.5.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/popper.js@1.16.1/dist/umd/popper.min.js"></script>
    <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>
    <script src="../assets/js/admin.js"></script>
</body>
</html>
