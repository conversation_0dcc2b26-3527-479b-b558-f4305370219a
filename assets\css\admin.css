/* Global Styles */
:root {
    --primary-color: #007bff;
    --primary-hover: #0069d9;
    --secondary-color: #6c757d;
    --light-bg: #f8f9fa;
    --border-color: rgba(0, 0, 0, 0.125);
    --success-color: #28a745;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --info-color: #17a2b8;
    --shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    --transition: all 0.3s ease;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: var(--light-bg);
    font-size: 16px;
    line-height: 1.5;
    color: #333;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    margin-bottom: 1rem;
}

/* Mobile-first approach */
.container-fluid {
    padding-left: 15px;
    padding-right: 15px;
}

/* Navbar Styles */
.navbar {
    padding: 0.5rem 1rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.navbar-brand {
    font-weight: 700;
    font-size: 1.25rem;
}

.navbar-toggler {
    border: none;
    padding: 0.5rem;
}

.navbar-toggler:focus {
    outline: none;
    box-shadow: none;
}

/* Sidebar Styles */
.sidebar {
    position: fixed;
    top: 56px;
    bottom: 0;
    left: 0;
    z-index: 100;
    padding: 48px 0 0;
    box-shadow: inset -1px 0 0 var(--border-color);
    height: calc(100vh - 56px);
    overflow-y: auto;
    transition: var(--transition);
}

.sidebar .nav-link {
    font-weight: 500;
    color: #333;
    padding: 0.75rem 1rem;
    border-left: 3px solid transparent;
    display: flex;
    align-items: center;
    transition: var(--transition);
}

.sidebar .nav-link i {
    margin-right: 10px;
    width: 20px;
    text-align: center;
}

.sidebar .nav-link:hover {
    color: var(--primary-color);
    background-color: #f0f0f0;
    border-left: 3px solid var(--primary-color);
}

.sidebar .nav-link.active {
    color: var(--primary-color);
    background-color: #e9ecef;
    border-left: 3px solid var(--primary-color);
}

.sidebar .nav-link .badge {
    margin-left: auto;
}

/* Mobile Sidebar Toggle */
.sidebar-toggle {
    position: fixed;
    bottom: 20px;
    right: 20px;
    z-index: 1000;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background-color: var(--primary-color);
    color: white;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
    border: none;
    outline: none;
    display: none;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
    transition: transform 0.3s, background-color 0.3s;
}

.sidebar-toggle:hover, .sidebar-toggle:focus {
    background-color: var(--primary-hover);
    transform: scale(1.05);
    color: white;
}

.sidebar-toggle:active {
    transform: scale(0.95);
}

/* Main Content */
main {
    padding-top: 20px;
    transition: var(--transition);
}

/* Card Styles */
.card {
    margin-bottom: 20px;
    border: none;
    border-radius: 0.5rem;
    box-shadow: var(--shadow);
    overflow: hidden;
}

.card-header {
    font-weight: 600;
    background-color: var(--light-bg);
    border-bottom: 1px solid var(--border-color);
    padding: 0.75rem 1.25rem;
}

.card-body {
    padding: 1.25rem;
}

/* Dashboard Stats */
.stats-card {
    border-left: 4px solid var(--primary-color);
    transition: transform 0.3s;
}

.stats-card:hover {
    transform: translateY(-5px);
}

.stats-card.primary {
    border-left-color: var(--primary-color);
}

.stats-card.success {
    border-left-color: var(--success-color);
}

.stats-card.warning {
    border-left-color: var(--warning-color);
}

.stats-card.danger {
    border-left-color: var(--danger-color);
}

.stats-card .card-body {
    display: flex;
    align-items: center;
}

.stats-card .icon {
    font-size: 2rem;
    margin-right: 1rem;
    opacity: 0.7;
}

.stats-card .stats-info {
    flex-grow: 1;
}

.stats-card .stats-number {
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 0.25rem;
}

.stats-card .stats-text {
    color: var(--secondary-color);
    margin-bottom: 0;
}

/* Table Styles */
.table-responsive {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
}

.table {
    width: 100%;
    margin-bottom: 1rem;
    color: #212529;
}

.table th {
    font-weight: 600;
    background-color: var(--light-bg);
    white-space: nowrap;
}

.table td {
    vertical-align: middle;
}

/* Mobile-friendly tables */
@media (max-width: 767.98px) {
    .table-mobile-responsive {
        display: block;
        width: 100%;
    }

    .table-mobile-responsive thead {
        display: none;
    }

    .table-mobile-responsive tbody,
    .table-mobile-responsive tr {
        display: block;
        width: 100%;
    }

    .table-mobile-responsive td {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0.75rem;
        border-bottom: 1px solid #dee2e6;
    }

    .table-mobile-responsive td:before {
        content: attr(data-label);
        font-weight: 600;
        margin-right: 1rem;
    }

    .table-mobile-responsive tr {
        margin-bottom: 1rem;
        border: 1px solid #dee2e6;
        border-radius: 0.25rem;
    }
}

/* Form Styles */
.form-control {
    height: calc(1.5em + 0.75rem + 2px);
    padding: 0.375rem 0.75rem;
    font-size: 1rem;
    border: 1px solid #ced4da;
    border-radius: 0.25rem;
}

.form-control:focus {
    border-color: #80bdff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.form-group {
    margin-bottom: 1rem;
}

.custom-file-label {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

/* Button Styles */
.btn {
    font-weight: 500;
    padding: 0.375rem 0.75rem;
    border-radius: 0.25rem;
    transition: var(--transition);
}

.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-primary:hover {
    background-color: var(--primary-hover);
    border-color: var(--primary-hover);
}

.btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
}

/* Status Badges */
.badge {
    font-weight: 500;
    padding: 0.35em 0.65em;
    border-radius: 0.25rem;
}

.badge-pending {
    background-color: var(--warning-color);
    color: #212529;
}

.badge-matched {
    background-color: var(--info-color);
    color: #fff;
}

.badge-completed {
    background-color: var(--success-color);
    color: #fff;
}

.badge-cancelled {
    background-color: var(--danger-color);
    color: #fff;
}

/* Responsive Adjustments */
@media (max-width: 991.98px) {
    .sidebar {
        width: 250px;
        transform: translateX(-100%);
        z-index: 1050;
        box-shadow: 2px 0 5px rgba(0, 0, 0, 0.1);
    }

    .sidebar.show {
        transform: translateX(0);
    }

    main {
        margin-left: 0 !important;
        width: 100%;
    }

    .sidebar-toggle {
        display: flex !important;
        position: fixed;
        bottom: 20px;
        right: 20px;
        z-index: 1060;
    }

    .stats-card .icon {
        font-size: 1.5rem;
    }

    .stats-card .stats-number {
        font-size: 1.25rem;
    }

    /* Add overlay when sidebar is shown */
    body.sidebar-open::before {
        content: '';
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: rgba(0, 0, 0, 0.5);
        z-index: 1040;
    }
}

@media (max-width: 767.98px) {
    body {
        font-size: 14px;
    }

    .container-fluid {
        padding-left: 10px;
        padding-right: 10px;
    }

    .card-header {
        padding: 0.5rem 1rem;
    }

    .card-body {
        padding: 1rem;
    }

    .btn {
        padding: 0.25rem 0.5rem;
        font-size: 0.85rem;
    }

    h1.h2 {
        font-size: 1.5rem;
    }

    .form-control {
        font-size: 14px;
    }

    .stats-card {
        margin-bottom: 1rem;
    }

    /* Improve card readability on mobile */
    .card h2 {
        font-size: 1.5rem;
    }

    .card h6 {
        font-size: 0.8rem;
    }

    /* Improve icon sizing on mobile */
    .card .fa-2x {
        font-size: 1.5rem;
    }

    /* Improve table display on mobile */
    .table-mobile-responsive td {
        padding: 0.5rem;
        font-size: 0.85rem;
    }

    .table-mobile-responsive td:before {
        font-size: 0.8rem;
    }

    /* Improve badge display on mobile */
    .badge {
        font-size: 0.7rem;
        padding: 0.25em 0.5em;
    }

    /* Improve sidebar on mobile */
    .sidebar {
        top: 56px;
        padding-top: 20px;
    }

    .sidebar .nav-link {
        padding: 0.5rem 1rem;
        font-size: 0.9rem;
    }

    /* Improve main content padding */
    main {
        padding-top: 10px;
        padding-bottom: 60px; /* Add space for the sidebar toggle button */
    }
}

/* Extra small devices */
@media (max-width: 575.98px) {
    .card-body {
        padding: 0.75rem;
    }

    .card h2 {
        font-size: 1.25rem;
    }

    .card h6 {
        font-size: 0.75rem;
    }

    .card .fa-2x {
        font-size: 1.25rem;
    }

    .btn {
        padding: 0.2rem 0.4rem;
        font-size: 0.8rem;
    }

    .table-mobile-responsive td {
        padding: 0.4rem;
        font-size: 0.8rem;
    }
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 6px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}
