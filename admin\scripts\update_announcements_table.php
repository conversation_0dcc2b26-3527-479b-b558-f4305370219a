<?php
// Include configuration
require_once __DIR__ . '/../../config/config.php';
require_once __DIR__ . '/../../database/db_connect.php';

// Create database instance
$database = new Database();
$db = $database->getConnection();

echo "Updating admin_announcements table to add media support...\n";

// Check if the table exists
$query = "SHOW TABLES LIKE 'admin_announcements'";
$stmt = $db->prepare($query);
$stmt->execute();
$table_exists = $stmt->rowCount() > 0;

if (!$table_exists) {
    echo "Table admin_announcements does not exist. Creating it...\n";

    // Create the admin_announcements table with enhanced media support
    $query = "CREATE TABLE admin_announcements (
        id INT AUTO_INCREMENT PRIMARY KEY,
        admin_id INT NOT NULL,
        title VARCHAR(100) NOT NULL,
        message TEXT NOT NULL,
        image_file VARCHAR(255),
        video_file VARCHAR(255),
        video_url VARCHAR(500),
        priority ENUM('urgent', 'normal', 'info') DEFAULT 'normal',
        link_preview_data JSON,
        is_pinned BOOLEAN DEFAULT FALSE,
        recipient_type VARCHAR(20) NOT NULL,
        recipients_count INT NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (admin_id) REFERENCES users(id) ON DELETE CASCADE
    )";

    $stmt = $db->prepare($query);

    if ($stmt->execute()) {
        echo "Table created successfully with media support.\n";
    } else {
        echo "Error creating table: " . print_r($stmt->errorInfo(), true) . "\n";
    }
} else {
    // Check if image_file column exists
    $query = "SHOW COLUMNS FROM admin_announcements LIKE 'image_file'";
    $stmt = $db->prepare($query);
    $stmt->execute();
    $image_column_exists = $stmt->rowCount() > 0;

    if (!$image_column_exists) {
        echo "Adding image_file column to admin_announcements table...\n";
        $query = "ALTER TABLE admin_announcements ADD COLUMN image_file VARCHAR(255)";
        $stmt = $db->prepare($query);

        if ($stmt->execute()) {
            echo "Image column added successfully.\n";
        } else {
            echo "Error adding image column: " . print_r($stmt->errorInfo(), true) . "\n";
        }
    } else {
        echo "image_file column already exists.\n";
    }

    // Check if video_file column exists
    $query = "SHOW COLUMNS FROM admin_announcements LIKE 'video_file'";
    $stmt = $db->prepare($query);
    $stmt->execute();
    $video_column_exists = $stmt->rowCount() > 0;

    if (!$video_column_exists) {
        echo "Adding video_file column to admin_announcements table...\n";
        $query = "ALTER TABLE admin_announcements ADD COLUMN video_file VARCHAR(255)";
        $stmt = $db->prepare($query);

        if ($stmt->execute()) {
            echo "Video column added successfully.\n";
        } else {
            echo "Error adding video column: " . print_r($stmt->errorInfo(), true) . "\n";
        }
    } else {
        echo "video_file column already exists.\n";
    }

    // Check if video_url column exists
    $query = "SHOW COLUMNS FROM admin_announcements LIKE 'video_url'";
    $stmt = $db->prepare($query);
    $stmt->execute();
    $video_url_column_exists = $stmt->rowCount() > 0;

    if (!$video_url_column_exists) {
        echo "Adding video_url column to admin_announcements table...\n";
        $query = "ALTER TABLE admin_announcements ADD COLUMN video_url VARCHAR(500)";
        $stmt = $db->prepare($query);

        if ($stmt->execute()) {
            echo "Video URL column added successfully.\n";
        } else {
            echo "Error adding video URL column: " . print_r($stmt->errorInfo(), true) . "\n";
        }
    } else {
        echo "video_url column already exists.\n";
    }

    // Check if priority column exists
    $query = "SHOW COLUMNS FROM admin_announcements LIKE 'priority'";
    $stmt = $db->prepare($query);
    $stmt->execute();
    $priority_column_exists = $stmt->rowCount() > 0;

    if (!$priority_column_exists) {
        echo "Adding priority column to admin_announcements table...\n";
        $query = "ALTER TABLE admin_announcements ADD COLUMN priority ENUM('urgent', 'normal', 'info') DEFAULT 'normal'";
        $stmt = $db->prepare($query);

        if ($stmt->execute()) {
            echo "Priority column added successfully.\n";
        } else {
            echo "Error adding priority column: " . print_r($stmt->errorInfo(), true) . "\n";
        }
    } else {
        echo "priority column already exists.\n";
    }

    // Check if link_preview_data column exists
    $query = "SHOW COLUMNS FROM admin_announcements LIKE 'link_preview_data'";
    $stmt = $db->prepare($query);
    $stmt->execute();
    $link_preview_column_exists = $stmt->rowCount() > 0;

    if (!$link_preview_column_exists) {
        echo "Adding link_preview_data column to admin_announcements table...\n";
        $query = "ALTER TABLE admin_announcements ADD COLUMN link_preview_data JSON";
        $stmt = $db->prepare($query);

        if ($stmt->execute()) {
            echo "Link preview data column added successfully.\n";
        } else {
            echo "Error adding link preview data column: " . print_r($stmt->errorInfo(), true) . "\n";
        }
    } else {
        echo "link_preview_data column already exists.\n";
    }

    // Check if is_pinned column exists
    $query = "SHOW COLUMNS FROM admin_announcements LIKE 'is_pinned'";
    $stmt = $db->prepare($query);
    $stmt->execute();
    $is_pinned_column_exists = $stmt->rowCount() > 0;

    if (!$is_pinned_column_exists) {
        echo "Adding is_pinned column to admin_announcements table...\n";
        $query = "ALTER TABLE admin_announcements ADD COLUMN is_pinned BOOLEAN DEFAULT FALSE";
        $stmt = $db->prepare($query);

        if ($stmt->execute()) {
            echo "Is pinned column added successfully.\n";
        } else {
            echo "Error adding is pinned column: " . print_r($stmt->errorInfo(), true) . "\n";
        }
    } else {
        echo "is_pinned column already exists.\n";
    }
}

echo "Done!\n";
?>
