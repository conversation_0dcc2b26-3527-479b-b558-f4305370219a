<?php
/**
 * Blog Functions
 *
 * Functions for fetching and displaying blog posts from blog.p2pdonate.com
 * Includes error handling, caching, and fallback content
 */

/**
 * Fetch blog posts from blog.p2pdonate.com
 *
 * @param int $limit Number of posts to fetch
 * @param bool $use_cache Whether to use cached results
 * @return array Array of blog posts or fallback content
 */
function fetch_blog_posts($limit = 4, $use_cache = true) {
    $cache_file = __DIR__ . '/../cache/blog_posts.json';
    $cache_duration = 3600; // 1 hour cache

    // Check if cache exists and is valid
    if ($use_cache && file_exists($cache_file) && (time() - filemtime($cache_file)) < $cache_duration) {
        $cached_data = json_decode(file_get_contents($cache_file), true);
        if ($cached_data && is_array($cached_data)) {
            $posts = array_slice($cached_data, 0, $limit);
            return ensure_unique_images($posts);
        }
    }

    // Try to fetch from blog.p2pdonate.com
    $blog_posts = fetch_from_blog_api($limit);

    // If fetching fails, use fallback content
    if (empty($blog_posts)) {
        $blog_posts = get_fallback_blog_posts();
    }

    // Cache the results
    if (!empty($blog_posts)) {
        // Ensure cache directory exists
        $cache_dir = dirname($cache_file);
        if (!is_dir($cache_dir)) {
            mkdir($cache_dir, 0755, true);
        }
        file_put_contents($cache_file, json_encode($blog_posts));
    }

    $posts = array_slice($blog_posts, 0, $limit);
    return ensure_unique_images($posts);
}

/**
 * Attempt to fetch blog posts from blog.p2pdonate.com
 *
 * @param int $limit Number of posts to fetch
 * @return array Array of blog posts or empty array on failure
 */
function fetch_from_blog_api($limit = 4) {
    $blog_posts = [];

    // Try RSS feed first
    $rss_url = 'https://blog.p2pdonate.com/feed/';
    $blog_posts = fetch_from_rss($rss_url, $limit);

    // If RSS fails, try JSON API
    if (empty($blog_posts)) {
        $api_url = 'https://blog.p2pdonate.com/wp-json/wp/v2/posts?per_page=' . $limit;
        $blog_posts = fetch_from_json_api($api_url);
    }

    return $blog_posts;
}

/**
 * Fetch blog posts from RSS feed
 *
 * @param string $rss_url RSS feed URL
 * @param int $limit Number of posts to fetch
 * @return array Array of blog posts or empty array on failure
 */
function fetch_from_rss($rss_url, $limit = 4) {
    $blog_posts = [];

    try {
        // Set context options for the request
        $context = stream_context_create([
            'http' => [
                'timeout' => 10,
                'user_agent' => 'P2P Donate Blog Reader/1.0'
            ]
        ]);

        // Suppress warnings and try to load RSS
        $rss_content = @file_get_contents($rss_url, false, $context);

        if ($rss_content !== false) {
            // Suppress XML errors
            libxml_use_internal_errors(true);
            $xml = simplexml_load_string($rss_content);

            if ($xml !== false && isset($xml->channel->item)) {
                $count = 0;
                foreach ($xml->channel->item as $item) {
                    if ($count >= $limit) break;

                    $title = (string)$item->title;
                    $description = (string)$item->description;
                    $excerpt = strip_tags(substr($description, 0, 150)) . '...';

                    $blog_posts[] = [
                        'title' => $title,
                        'excerpt' => $excerpt,
                        'url' => (string)$item->link,
                        'date' => date('M j, Y', strtotime((string)$item->pubDate)),
                        'image' => extract_image_from_content($description, $title, $excerpt)
                    ];
                    $count++;
                }
            }
        }
    } catch (Exception $e) {
        // Log error if needed
        error_log('RSS fetch error: ' . $e->getMessage());
    }

    return $blog_posts;
}

/**
 * Fetch blog posts from JSON API (WordPress REST API)
 *
 * @param string $api_url API endpoint URL
 * @return array Array of blog posts or empty array on failure
 */
function fetch_from_json_api($api_url) {
    $blog_posts = [];

    try {
        $context = stream_context_create([
            'http' => [
                'timeout' => 10,
                'user_agent' => 'P2P Donate Blog Reader/1.0'
            ]
        ]);

        $json_content = @file_get_contents($api_url, false, $context);

        if ($json_content !== false) {
            $posts = json_decode($json_content, true);

            if (is_array($posts)) {
                foreach ($posts as $post) {
                    $title = $post['title']['rendered'] ?? 'Untitled';
                    $excerpt = strip_tags($post['excerpt']['rendered'] ?? '') ?:
                               strip_tags(substr($post['content']['rendered'] ?? '', 0, 150)) . '...';

                    $blog_posts[] = [
                        'title' => $title,
                        'excerpt' => $excerpt,
                        'url' => $post['link'] ?? '#',
                        'date' => date('M j, Y', strtotime($post['date'] ?? 'now')),
                        'image' => get_featured_image_from_post($post, $title, $excerpt)
                    ];
                }
            }
        }
    } catch (Exception $e) {
        error_log('JSON API fetch error: ' . $e->getMessage());
    }

    return $blog_posts;
}

/**
 * Extract image URL from content
 *
 * @param string $content HTML content
 * @param string $title Blog post title (optional)
 * @param string $excerpt Blog post excerpt (optional)
 * @return string Image URL or contextual blog image
 */
function extract_image_from_content($content, $title = '', $excerpt = '') {
    // Try to find img tag
    if (preg_match('/<img[^>]+src=["\']([^"\']+)["\'][^>]*>/i', $content, $matches)) {
        return $matches[1];
    }

    // Return a contextual blog image based on content
    return get_contextual_blog_image($title, $excerpt);
}

/**
 * Get featured image from WordPress post data
 *
 * @param array $post WordPress post data
 * @param string $title Blog post title (optional)
 * @param string $excerpt Blog post excerpt (optional)
 * @return string Image URL or contextual blog image
 */
function get_featured_image_from_post($post, $title = '', $excerpt = '') {
    // Check for featured media and try to fetch it
    if (isset($post['featured_media']) && $post['featured_media'] > 0) {
        $featured_image = fetch_featured_media($post['featured_media']);
        if ($featured_image) {
            return $featured_image;
        }
    }

    // Try to extract from content
    if (isset($post['content']['rendered'])) {
        return extract_image_from_content($post['content']['rendered'], $title, $excerpt);
    }

    // Return a contextual blog image based on title and excerpt
    return get_contextual_blog_image($title, $excerpt);
}

/**
 * Get fallback blog posts when external blog is unavailable
 *
 * @return array Array of fallback blog posts
 */
function get_fallback_blog_posts() {
    return [
        [
            'title' => 'Breaking Free from Betting Addiction: Your Journey to Financial Freedom',
            'excerpt' => 'Discover how P2P Donate is helping young Ghanaians overcome betting addiction and build sustainable wealth through our community-based pledge system...',
            'url' => '#',
            'date' => date('M j, Y', strtotime('-2 days')),
            'image' => 'assets/hero-image.png'
        ],
        [
            'title' => 'Success Story: From GHS 50 Daily Betting to Building a Small Business',
            'excerpt' => 'Meet Anthony from Accra, who transformed his life by choosing P2P Donate over sports betting. Learn how he used our platform to start his own business...',
            'url' => '#',
            'date' => date('M j, Y', strtotime('-5 days')),
            'image' => 'assets/testimonial-1.jpg'
        ],
        [
            'title' => 'Understanding Mobile Money Security in Ghana\'s P2P Economy',
            'excerpt' => 'A comprehensive guide to staying safe while using mobile money for peer-to-peer transactions. Learn best practices for secure donations...',
            'url' => '#',
            'date' => date('M j, Y', strtotime('-1 week')),
            'image' => 'assets/signup-image.jpg'
        ],
        [
            'title' => 'Financial Literacy for Young Ghanaians: Building Wealth Without Gambling',
            'excerpt' => 'Essential financial skills every young Ghanaian should know. Learn how to save, invest, and grow your money through legitimate means...',
            'url' => '#',
            'date' => date('M j, Y', strtotime('-10 days')),
            'image' => 'assets/testimonial-2.jpg'
        ]
    ];
}

/**
 * Format blog post date for display
 *
 * @param string $date Date string
 * @return string Formatted date
 */
function format_blog_date($date) {
    return date('M j, Y', strtotime($date));
}

/**
 * Truncate text to specified length
 *
 * @param string $text Text to truncate
 * @param int $length Maximum length
 * @param string $suffix Suffix to add if truncated
 * @return string Truncated text
 */
function truncate_text($text, $length = 150, $suffix = '...') {
    if (strlen($text) <= $length) {
        return $text;
    }

    return substr($text, 0, $length) . $suffix;
}

/**
 * Get a random blog image from available blog thumbnails
 *
 * @return string Random blog image path
 */
function get_random_blog_image() {
    $blog_images = [
        'assets/hero-image.png',
        'assets/testimonial-1.jpg',
        'assets/testimonial-2.jpg',
        'assets/testimonial-3.jpg',
        'assets/signup-image.jpg'
    ];

    // Return a random image from the array
    return $blog_images[array_rand($blog_images)];
}

/**
 * Get appropriate blog image based on content keywords
 *
 * @param string $title Blog post title
 * @param string $content Blog post content/excerpt
 * @return string Appropriate blog image path
 */
function get_contextual_blog_image($title = '', $content = '') {
    $text = strtolower($title . ' ' . $content);

    // Define keyword-based image mapping
    $image_keywords = [
        'assets/testimonial-1.jpg' => ['success', 'story', 'business', 'entrepreneur', 'achievement'],
        'assets/testimonial-2.jpg' => ['student', 'university', 'education', 'learning', 'school'],
        'assets/testimonial-3.jpg' => ['addiction', 'recovery', 'breaking', 'overcome', 'change'],
        'assets/signup-image.jpg' => ['mobile', 'money', 'security', 'payment', 'transaction', 'technology'],
        'assets/hero-image.png' => ['financial', 'freedom', 'wealth', 'literacy', 'future', 'donate']
    ];

    // Check for keyword matches
    foreach ($image_keywords as $image => $keywords) {
        foreach ($keywords as $keyword) {
            if (strpos($text, $keyword) !== false) {
                return $image;
            }
        }
    }

    // If no keywords match, return a random image
    return get_random_blog_image();
}

/**
 * Fetch featured media from WordPress API
 *
 * @param int $media_id WordPress media ID
 * @return string|false Featured image URL or false on failure
 */
function fetch_featured_media($media_id) {
    try {
        $api_url = "https://blog.p2pdonate.com/wp-json/wp/v2/media/{$media_id}";

        $context = stream_context_create([
            'http' => [
                'timeout' => 5,
                'user_agent' => 'P2P Donate Blog Reader/1.0'
            ]
        ]);

        $json_content = @file_get_contents($api_url, false, $context);

        if ($json_content !== false) {
            $media = json_decode($json_content, true);

            if (isset($media['source_url'])) {
                return $media['source_url'];
            }

            // Try medium size if available
            if (isset($media['media_details']['sizes']['medium']['source_url'])) {
                return $media['media_details']['sizes']['medium']['source_url'];
            }
        }
    } catch (Exception $e) {
        error_log('Featured media fetch error: ' . $e->getMessage());
    }

    return false;
}

/**
 * Validate if an image URL/path exists and is accessible
 *
 * @param string $image_path Image URL or local path
 * @return bool True if image exists and is accessible
 */
function validate_image_exists($image_path) {
    // For local paths
    if (strpos($image_path, 'assets/') === 0) {
        return file_exists($image_path);
    }

    // For remote URLs, we'll assume they're valid to avoid additional HTTP requests
    // In a production environment, you might want to implement proper URL validation
    return filter_var($image_path, FILTER_VALIDATE_URL) !== false;
}

/**
 * Ensure blog posts have unique images when possible
 *
 * @param array $posts Array of blog posts
 * @return array Array of blog posts with unique images
 */
function ensure_unique_images($posts) {
    if (empty($posts) || count($posts) <= 1) {
        return $posts;
    }

    $available_images = [
        'assets/hero-image.png',
        'assets/testimonial-1.jpg',
        'assets/testimonial-2.jpg',
        'assets/testimonial-3.jpg',
        'assets/signup-image.jpg'
    ];

    $used_images = [];

    foreach ($posts as $index => &$post) {
        // If this image has already been used, try to find an alternative
        if (in_array($post['image'], $used_images)) {
            // Find an unused image
            $unused_images = array_diff($available_images, $used_images);

            if (!empty($unused_images)) {
                // Use the first unused image
                $post['image'] = reset($unused_images);
            } else {
                // If all images are used, try contextual matching again
                $post['image'] = get_contextual_blog_image($post['title'], $post['excerpt']);
            }
        }

        $used_images[] = $post['image'];
    }

    return $posts;
}
?>
