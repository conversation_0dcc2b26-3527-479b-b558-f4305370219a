/*
* P2P Donate - Main Stylesheet
* A beautiful, responsive design for the P2P Donate landing page
*/

/* ===== Base Styles ===== */
:root {
    /* Primary Colors */
    --primary-color: #00A651; /* Green - represents growth, money, prosperity */
    --secondary-color: #FFC107; /* Gold/Yellow - represents wealth, optimism */
    --accent-color: #FF5722; /* Orange - represents energy, enthusiasm */

    /* Neutral Colors */
    --dark-color: #212121;
    --text-color: #333333;
    --light-text: #757575;
    --background-color: #FFFFFF;
    --light-background: #F5F5F5;

    /* Section Background Colors */
    --bg-light-green: #f0f7f2; /* Light green tint */
    --bg-light-gold: #fffaf0; /* Light gold/yellow tint */
    --bg-light-orange: #fff5f0; /* Light orange tint */
    --bg-gradient-green: linear-gradient(135deg, #f0f7ff 0%, #e4f2e4 100%);
    --bg-gradient-gold: linear-gradient(135deg, #fffaf0 0%, #fff8e1 100%);

    /* Fonts */
    --body-font: 'Poppins', sans-serif;
    --heading-font: 'Poppins', sans-serif;

    /* Spacing */
    --spacing-xs: 0.5rem;
    --spacing-sm: 1rem;
    --spacing-md: 2rem;
    --spacing-lg: 3rem;
    --spacing-xl: 5rem;

    /* Border Radius */
    --border-radius-sm: 4px;
    --border-radius-md: 8px;
    --border-radius-lg: 16px;

    /* Shadows */
    --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.1);
    --shadow-md: 0 4px 8px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 8px 16px rgba(0, 0, 0, 0.1);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    font-size: 16px;
    scroll-behavior: smooth;
}

body {
    font-family: var(--body-font);
    color: var(--text-color);
    line-height: 1.6;
    background-color: var(--background-color);
    overflow-x: hidden; /* Prevent horizontal scrolling */
    width: 100%;
    max-width: 100vw;
}

h1, h2, h3, h4, h5, h6 {
    font-family: var(--heading-font);
    font-weight: 700;
    line-height: 1.3;
    margin-bottom: var(--spacing-sm);
    color: var(--dark-color);
}

h1 {
    font-size: 3rem;
}

h2 {
    font-size: 2.5rem;
}

h3 {
    font-size: 2rem;
}

h4 {
    font-size: 1.5rem;
}

p {
    margin-bottom: var(--spacing-sm);
}

a {
    color: var(--primary-color);
    text-decoration: none;
    transition: all 0.3s ease;
}

a:hover {
    color: var(--accent-color);
}

img {
    max-width: 100%;
    height: auto;
}

.container {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--spacing-sm);
    box-sizing: border-box; /* Ensure padding is included in width calculation */
}

.section-title {
    text-align: center;
    margin-bottom: var(--spacing-md);
}

.section-subtitle {
    text-align: center;
    font-size: 1.2rem;
    color: var(--light-text);
    margin-bottom: var(--spacing-lg);
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
}

/* ===== Buttons ===== */
.btn {
    display: inline-block;
    padding: 0.75rem 1.5rem;
    border-radius: var(--border-radius-md);
    font-weight: 600;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    border: none;
}

.btn-primary {
    background-color: var(--primary-color);
    color: white;
}

.btn-primary:hover {
    background-color: #008c44;
    color: white;
}

.btn-secondary {
    background-color: transparent;
    color: var(--primary-color);
    border: 2px solid var(--primary-color);
}

.btn-secondary:hover {
    background-color: var(--primary-color);
    color: white;
}

.btn-large {
    padding: 1rem 2rem;
    font-size: 1.1rem;
}

.btn-sm {
    padding: 0.5rem 1rem;
    font-size: 0.9rem;
}

.btn-block {
    display: block;
    width: 100%;
}

/* ===== Header & Navigation ===== */
header {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    background-color: white;
    box-shadow: var(--shadow-sm);
    z-index: 1000;
    padding: 1rem 0;
    transition: transform 0.3s ease, background-color 0.3s ease, box-shadow 0.3s ease;
}

header.scrolled {
    background-color: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(5px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
}

nav {
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
    z-index: 1001;
}

.logo {
    display: flex;
    align-items: center;
}

.logo img {
    height: 60px; /* Increased from 40px for better visibility */
    max-width: 100%; /* Ensure logo doesn't overflow its container */
    object-fit: contain; /* Maintain aspect ratio */
}

.nav-links {
    display: flex;
    list-style: none;
}

.nav-links li {
    margin-left: var(--spacing-md);
}

.nav-links a {
    color: var(--text-color);
    font-weight: 500;
}

.nav-links a:hover {
    color: var(--primary-color);
}

.cta-buttons {
    display: flex;
    gap: 10px;
    align-items: center;
}

/* Mobile CTA container - hidden by default on all screen sizes */
.mobile-cta-container {
    display: none;
}

.mobile-menu-toggle {
    display: none;
    flex-direction: column;
    justify-content: space-between;
    cursor: pointer;
    width: 30px;
    height: 22px;
    position: relative;
    z-index: 1002; /* Higher than the menu to stay on top */
    margin-left: 15px; /* Add some spacing */
}

.mobile-menu-toggle span {
    width: 30px;
    height: 3px;
    background-color: var(--text-color);
    border-radius: 3px;
    transition: all 0.3s ease;
    display: block;
}

/* Mobile menu toggle animation */
.mobile-menu-toggle.active span {
    background-color: var(--primary-color);
}

.mobile-menu-toggle.active span:nth-child(1) {
    transform: rotate(45deg) translate(6px, 6px);
}

.mobile-menu-toggle.active span:nth-child(2) {
    opacity: 0;
    transform: translateX(-20px);
}

.mobile-menu-toggle.active span:nth-child(3) {
    transform: rotate(-45deg) translate(6px, -6px);
}

/* Ensure mobile menu toggle is hidden on tablet and desktop */
@media (min-width: 769px), (width: 820px) and (height: 1180px) {
    .mobile-menu-toggle {
        display: none !important;
    }

    .nav-links {
        display: flex !important;
        opacity: 1 !important;
        transform: none !important;
    }
}

/* ===== Hero Section ===== */
.hero {
    padding: calc(100px + var(--spacing-xl)) 0 var(--spacing-xl); /* Increased top padding to accommodate larger header */
    background: var(--bg-gradient-green);
    position: relative;
    overflow: hidden;
}

.hero::before {
    content: '';
    position: absolute;
    top: -10%;
    right: -5%;
    width: 300px;
    height: 300px;
    border-radius: 50%;
    background: radial-gradient(circle, rgba(0, 166, 81, 0.1) 0%, rgba(0, 166, 81, 0) 70%);
    z-index: 0;
}

.hero::after {
    content: '';
    position: absolute;
    bottom: -5%;
    left: -5%;
    width: 250px;
    height: 250px;
    border-radius: 50%;
    background: radial-gradient(circle, rgba(255, 193, 7, 0.1) 0%, rgba(255, 193, 7, 0) 70%);
    z-index: 0;
}

.hero .container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: relative;
    z-index: 1;
}

.hero-content {
    flex: 1;
    max-width: 600px;
    position: relative;
}

.hero-content::before {
    content: '';
    position: absolute;
    top: -30px;
    left: -40px;
    width: 80px;
    height: 80px;
    background-image: url('assets/pattern-dots.svg');
    background-repeat: no-repeat;
    background-size: contain;
    opacity: 0.5;
    z-index: -1;
}

.hero h1 {
    margin-bottom: var(--spacing-md);
    color: var(--dark-color);
    position: relative;
    display: inline-block;
}

.hero h1::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 0;
    width: 80px;
    height: 4px;
    background-color: var(--primary-color);
    border-radius: 2px;
}

/* Center the underline on mobile */
@media (max-width: 768px) {
    .hero h1::after {
        left: 50%;
        transform: translateX(-50%);
    }
}

.hero-subtitle {
    font-size: 1.2rem;
    margin-bottom: var(--spacing-lg);
    color: var(--light-text);
}

.hero-cta {
    display: flex;
    gap: var(--spacing-sm);
    flex-wrap: wrap; /* Allow buttons to wrap on smaller screens */
    align-items: center; /* Vertically center buttons */
}

.hero-cta .btn {
    text-align: center; /* Ensure text is centered in buttons */
}

.hero-image {
    flex: 1;
    text-align: right;
    position: relative;
}

.hero-image::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 10%;
    width: 90%;
    height: 90%;
    background-color: rgba(0, 166, 81, 0.1);
    border-radius: var(--border-radius-lg);
    transform: translateY(-50%) rotate(-3deg);
    z-index: -1;
}

.hero-image img {
    max-width: 90%;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-lg);
    transform: translateY(-5px);
    transition: transform 0.3s ease;
    height: auto; /* Ensure proper aspect ratio */
    object-fit: contain; /* Prevent image distortion */
}

.hero-image img:hover {
    transform: translateY(-10px);
}

/* ===== Section Dividers ===== */
.section-divider {
    height: 100px;
    width: 100%;
    overflow: hidden;
    position: relative;
    margin-top: -50px;
    z-index: 1;
}

.section-divider.wave-top {
    transform: rotate(180deg);
}

.section-divider svg {
    position: absolute;
    width: 100%;
    height: 100px;
}

/* ===== Statistics Section ===== */
.statistics {
    padding: var(--spacing-xl) 0;
    background-color: var(--bg-light-orange);
    position: relative;
    overflow: hidden;
}

.statistics::before {
    content: '';
    position: absolute;
    top: 20%;
    right: -100px;
    width: 300px;
    height: 300px;
    border-radius: 50%;
    background: radial-gradient(circle, rgba(255, 87, 34, 0.05) 0%, rgba(255, 87, 34, 0) 70%);
    z-index: 0;
}

.statistics::after {
    content: '';
    position: absolute;
    bottom: 10%;
    left: -100px;
    width: 200px;
    height: 200px;
    border-radius: 50%;
    background: radial-gradient(circle, rgba(0, 166, 81, 0.05) 0%, rgba(0, 166, 81, 0) 70%);
    z-index: 0;
}

.statistics .container {
    position: relative;
    z-index: 1;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-xl);
}

.stat-card {
    background-color: white;
    padding: var(--spacing-md);
    border-radius: var(--border-radius-md);
    box-shadow: var(--shadow-md);
    text-align: center;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    position: relative;
    overflow: hidden;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 5px;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.stat-number {
    font-size: 2.5rem;
    font-weight: 700;
    background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    margin-bottom: var(--spacing-xs);
}

.impact-stories h3 {
    text-align: center;
    margin-bottom: var(--spacing-md);
    position: relative;
    display: inline-block;
    left: 50%;
    transform: translateX(-50%);
}

.impact-stories h3::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 3px;
    background-color: var(--accent-color);
    border-radius: 2px;
}

.story-cards {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: var(--spacing-md);
}

.story-card {
    background-color: white;
    padding: var(--spacing-md);
    border-radius: var(--border-radius-md);
    box-shadow: var(--shadow-md);
    transition: transform 0.3s ease;
    border-left: 3px solid var(--accent-color);
}

.story-card:hover {
    transform: translateY(-5px);
}

.story-card h4 {
    color: var(--accent-color);
    margin-bottom: var(--spacing-xs);
}

/* ===== How It Works Section ===== */
.how-it-works {
    padding: var(--spacing-xl) 0;
    background-color: var(--background-color);
    position: relative;
    overflow: hidden;
}

.how-it-works::before {
    content: '';
    position: absolute;
    top: -150px;
    right: -150px;
    width: 300px;
    height: 300px;
    border-radius: 50%;
    background-color: rgba(0, 166, 81, 0.03);
    z-index: 0;
}

.how-it-works::after {
    content: '';
    position: absolute;
    bottom: -100px;
    left: -100px;
    width: 250px;
    height: 250px;
    border-radius: 50%;
    background-color: rgba(255, 193, 7, 0.03);
    z-index: 0;
}

.how-it-works .container {
    position: relative;
    z-index: 1;
}

.how-it-works .section-title {
    position: relative;
}

.how-it-works .section-title::after {
    content: '';
    position: absolute;
    bottom: -15px;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
    border-radius: 2px;
}

.steps {
    display: flex;
    justify-content: space-between;
    margin-bottom: var(--spacing-xl);
    position: relative;
}

.steps::before {
    content: '';
    position: absolute;
    top: 40px;
    left: 10%;
    width: 80%;
    height: 2px;
    background: linear-gradient(90deg,
        var(--primary-color) 0%,
        var(--secondary-color) 33%,
        var(--accent-color) 66%,
        var(--primary-color) 100%);
    z-index: 0;
}

.step {
    flex: 1;
    text-align: center;
    padding: 0 var(--spacing-sm);
    position: relative;
    z-index: 1;
    transition: transform 0.3s ease;
}

.step:hover {
    transform: translateY(-5px);
}

.step:not(:last-child)::after {
    content: '';
    position: absolute;
    top: 40px;
    right: -30px;
    width: 60px;
    height: 20px;
    background-image: url('assets/arrow-right.svg');
    background-repeat: no-repeat;
    background-position: center;
    z-index: 2;
}

.step-icon {
    width: 80px;
    height: 80px;
    margin: 0 auto var(--spacing-md);
    background: linear-gradient(135deg, var(--primary-color), #00c45c);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 10px 20px rgba(0, 166, 81, 0.2);
    position: relative;
    z-index: 2;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.step:hover .step-icon {
    transform: scale(1.1);
    box-shadow: 0 15px 30px rgba(0, 166, 81, 0.3);
}

.step-icon img {
    width: 40px;
    height: 40px;
    filter: brightness(0) invert(1);
}

.step h3 {
    margin-bottom: var(--spacing-xs);
    color: var(--dark-color);
    position: relative;
    display: inline-block;
}

.features h3 {
    text-align: center;
    margin-bottom: var(--spacing-md);
    position: relative;
    display: inline-block;
    left: 50%;
    transform: translateX(-50%);
}

.features h3::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 0;
    width: 100%;
    height: 3px;
    background-color: var(--secondary-color);
    border-radius: 2px;
}

.feature-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: var(--spacing-md);
}

.feature {
    text-align: center;
    padding: var(--spacing-md);
    background-color: var(--light-background);
    border-radius: var(--border-radius-md);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    position: relative;
    overflow: hidden;
}

.feature::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(0, 166, 81, 0.05) 0%, rgba(255, 193, 7, 0.05) 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.feature:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-md);
}

.feature:hover::before {
    opacity: 1;
}

.feature img {
    width: 60px;
    height: 60px;
    margin-bottom: var(--spacing-sm);
    transition: transform 0.3s ease;
}

.feature:hover img {
    transform: scale(1.1);
}

/* ===== Testimonials Section ===== */
.testimonials {
    padding: var(--spacing-xl) 0;
    background-color: var(--bg-light-green);
    position: relative;
    overflow: hidden;
}

.testimonials::before {
    content: '';
    position: absolute;
    top: -50px;
    right: -50px;
    width: 200px;
    height: 200px;
    background-image: url('assets/pattern-dots.svg');
    background-repeat: no-repeat;
    background-size: contain;
    opacity: 0.1;
    z-index: 0;
}

.testimonials::after {
    content: '';
    position: absolute;
    bottom: -50px;
    left: -50px;
    width: 200px;
    height: 200px;
    background-image: url('assets/pattern-dots.svg');
    background-repeat: no-repeat;
    background-size: contain;
    opacity: 0.1;
    z-index: 0;
}

.testimonials .container {
    position: relative;
    z-index: 1;
}

.testimonials .section-title {
    position: relative;
}

.testimonials .section-title::after {
    content: '';
    position: absolute;
    bottom: -15px;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 4px;
    background: linear-gradient(90deg, var(--secondary-color), var(--accent-color));
    border-radius: 2px;
}

.testimonial-slider {
    display: flex;
    overflow-x: auto;
    scroll-snap-type: x mandatory;
    gap: var(--spacing-md);
    padding: var(--spacing-xl) var(--spacing-sm);
    -ms-overflow-style: none;  /* Hide scrollbar for IE and Edge */
    scrollbar-width: none;  /* Hide scrollbar for Firefox */
    margin: 0 -var(--spacing-sm); /* Extend beyond container */
    position: relative;
    align-items: stretch; /* Ensure all cards have the same height */
}

.testimonial-slider::after {
    content: '';
    flex: 0 0 var(--spacing-sm);
    padding-right: var(--spacing-sm);
}

.testimonial-slider::-webkit-scrollbar {
    display: none; /* Hide scrollbar for Chrome, Safari and Opera */
}

.testimonial {
    flex: 0 0 calc(33.333% - var(--spacing-md));
    scroll-snap-align: start;
    background-color: white;
    border-radius: var(--border-radius-md);
    box-shadow: var(--shadow-md);
    overflow: hidden;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    position: relative;
    display: flex;
    flex-direction: column;
    height: 100%;
    min-height: 400px; /* Ensure consistent height */
    max-width: 350px; /* Limit maximum width */
    margin: 0 auto; /* Center cards when they wrap */
}

.testimonial::before {
    content: '"';
    position: absolute;
    top: -20px;
    right: 20px;
    font-size: 100px;
    color: var(--primary-color);
    opacity: 0.1;
    font-family: Georgia, serif;
    line-height: 1;
    z-index: 1;
}

.testimonial:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-lg);
}

.testimonial-image {
    position: relative;
    width: 100%;
    height: 0;
    padding-bottom: 75%; /* 4:3 aspect ratio */
    overflow: hidden;
    border-radius: var(--border-radius-md) var(--border-radius-md) 0 0;
    background-color: var(--light-background);
    display: flex;
    align-items: center;
    justify-content: center;
    border-bottom: 3px solid var(--primary-color);
}

.testimonial-image img {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center top; /* Focus on the face */
    transition: transform 0.5s ease;
}

.testimonial:hover .testimonial-image img {
    transform: scale(1.05);
}

.testimonial-content {
    padding: var(--spacing-md);
    position: relative;
    flex: 1;
    display: flex;
    flex-direction: column;
}

.testimonial-content p {
    font-style: italic;
    margin-bottom: var(--spacing-md);
    position: relative;
    z-index: 1;
    flex: 1;
    font-size: 0.95rem;
    line-height: 1.6;
    color: var(--text-color);
}

.testimonial-content h4 {
    color: var(--dark-color);
    margin-bottom: 5px;
    font-size: 1.1rem;
    font-weight: 600;
}

.testimonial-location {
    color: var(--primary-color);
    font-weight: 500;
    font-size: 0.9rem;
    display: flex;
    align-items: center;
}

.testimonial-location::before {
    content: '';
    display: inline-block;
    width: 8px;
    height: 8px;
    background-color: var(--primary-color);
    border-radius: 50%;
    margin-right: 8px;
    opacity: 0.7;
}

/* ===== Sign Up Section ===== */
.signup {
    padding: var(--spacing-xl) 0;
    background: var(--bg-gradient-gold);
    position: relative;
    overflow: hidden;
}

.signup::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 100%;
    height: 100%;
    background-image: url('assets/pattern-bg.svg');
    background-repeat: no-repeat;
    background-position: center right;
    background-size: contain;
    opacity: 0.05;
    z-index: 0;
}

.signup .container {
    display: flex;
    align-items: center;
    gap: var(--spacing-xl);
    position: relative;
    z-index: 1;
}

.signup-content {
    flex: 1;
}

.signup-content h2 {
    position: relative;
    display: inline-block;
    margin-bottom: var(--spacing-md);
}

.signup-content h2::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 0;
    width: 60px;
    height: 4px;
    background-color: var(--primary-color);
    border-radius: 2px;
}

.signup-form-container {
    background-color: white;
    padding: var(--spacing-lg);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-lg);
    margin-top: var(--spacing-md);
    border-top: 5px solid var(--primary-color);
}

.form-group {
    margin-bottom: var(--spacing-sm);
}

.form-group input,
.form-group select {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #ddd;
    border-radius: var(--border-radius-md);
    font-family: var(--body-font);
    transition: border-color 0.3s ease, box-shadow 0.3s ease;
}

.form-group input:focus,
.form-group select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(0, 166, 81, 0.1);
}

.password-field {
    position: relative;
}

.terms-checkbox {
    display: flex;
    align-items: center;
    font-size: 0.9rem;
}

.terms-checkbox input[type="checkbox"] {
    width: auto;
    margin-right: 10px;
}

.terms-checkbox a {
    color: var(--primary-color);
    text-decoration: underline;
}

.terms-checkbox a:hover {
    color: var(--accent-color);
}

.signup-image {
    flex: 1;
    text-align: center;
    position: relative;
}

.signup-image::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 80%;
    height: 80%;
    background-color: rgba(255, 193, 7, 0.1);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    z-index: -1;
}

.signup-image img {
    max-width: 80%;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-lg);
    transition: transform 0.5s ease;
}

.signup-image:hover img {
    transform: translateY(-10px);
}

/* ===== Footer ===== */
footer {
    background-color: var(--dark-color);
    color: white;
    padding: var(--spacing-xl) 0 var(--spacing-md);
}

.footer-grid {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr 2fr;
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-lg);
}

.footer-logo {
    height: 60px; /* Increased from 40px to match header logo size */
    margin-bottom: var(--spacing-sm);
}

.social-links {
    display: flex;
    gap: var(--spacing-sm);
    margin-top: var(--spacing-md);
}

.social-links a {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    color: white;
}

.social-links a:hover {
    background-color: var(--primary-color);
}

.footer-links ul {
    list-style: none;
}

.footer-links li {
    margin-bottom: var(--spacing-xs);
}

.footer-links a,
.footer-contact a {
    color: white;
    opacity: 0.8;
}

.footer-links a:hover,
.footer-contact a:hover {
    opacity: 1;
    color: var(--primary-color);
}

.footer-contact p {
    display: flex;
    align-items: center;
    margin-bottom: var(--spacing-xs);
}

.footer-contact i {
    margin-right: var(--spacing-xs);
    color: var(--primary-color);
}

.newsletter-form {
    display: flex;
    margin-top: var(--spacing-sm);
}

.newsletter-form input {
    flex: 1;
    padding: 0.75rem;
    border: none;
    border-radius: var(--border-radius-md) 0 0 var(--border-radius-md);
}

.newsletter-form button {
    border-radius: 0 var(--border-radius-md) var(--border-radius-md) 0;
}

.footer-bottom {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: var(--spacing-md);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.footer-bottom-links {
    display: flex;
    gap: var(--spacing-md);
}

.footer-bottom-links a {
    color: white;
    opacity: 0.8;
}

.footer-bottom-links a:hover {
    opacity: 1;
    color: var(--primary-color);
}

/* Alert Messages */
.alert {
    padding: var(--spacing-sm);
    border-radius: var(--border-radius-md);
    margin-bottom: var(--spacing-md);
}

.alert-success {
    background-color: rgba(0, 166, 81, 0.1);
    border: 1px solid var(--primary-color);
    color: var(--primary-color);
}

.alert-error {
    background-color: rgba(255, 87, 34, 0.1);
    border: 1px solid var(--accent-color);
    color: var(--accent-color);
}

/* FAQ Section */
.faq {
    padding: var(--spacing-xl) 0;
    background-color: var(--bg-light-gold);
    position: relative;
    overflow: hidden;
}

.faq::before {
    content: '';
    position: absolute;
    top: -100px;
    right: -100px;
    width: 300px;
    height: 300px;
    border-radius: 50%;
    background: radial-gradient(circle, rgba(0, 166, 81, 0.05) 0%, rgba(0, 166, 81, 0) 70%);
    z-index: 0;
}

.faq::after {
    content: '';
    position: absolute;
    bottom: -100px;
    left: -100px;
    width: 300px;
    height: 300px;
    border-radius: 50%;
    background: radial-gradient(circle, rgba(255, 193, 7, 0.05) 0%, rgba(255, 193, 7, 0) 70%);
    z-index: 0;
}

.faq .container {
    position: relative;
    z-index: 1;
}

.faq .section-title {
    position: relative;
}

.faq .section-title::after {
    content: '';
    position: absolute;
    bottom: -15px;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
    border-radius: 2px;
}

.faq-container {
    max-width: 800px;
    margin: 0 auto;
}

.faq-item {
    background-color: white;
    border-radius: var(--border-radius-md);
    box-shadow: var(--shadow-md);
    margin-bottom: var(--spacing-md);
    overflow: hidden;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    border-left: 3px solid transparent;
}

.faq-item:hover {
    transform: translateX(5px);
    box-shadow: var(--shadow-lg);
    border-left: 3px solid var(--primary-color);
}

.faq-item.active {
    border-left: 3px solid var(--primary-color);
    box-shadow: var(--shadow-lg);
}

.faq-question {
    padding: var(--spacing-md);
    display: flex;
    justify-content: space-between;
    align-items: center;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.faq-question:hover {
    background-color: rgba(0, 166, 81, 0.05);
}

.faq-question h3 {
    margin: 0;
    font-size: 1.2rem;
    color: var(--dark-color);
    transition: color 0.3s ease;
}

.faq-item.active .faq-question h3 {
    color: var(--primary-color);
}

.faq-toggle {
    font-size: 1.5rem;
    font-weight: bold;
    color: var(--primary-color);
    transition: transform 0.3s ease, color 0.3s ease;
}

.faq-item:hover .faq-toggle {
    color: var(--accent-color);
}

.faq-item.active .faq-toggle {
    transform: rotate(45deg);
    color: var(--accent-color);
}

.faq-answer {
    padding: 0 var(--spacing-md);
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.5s ease, padding 0.5s ease;
    background-color: rgba(0, 166, 81, 0.02);
}

.faq-item.active .faq-answer {
    padding: 0 var(--spacing-md) var(--spacing-md);
    max-height: 1000px;
}

/* ===== Contact Section ===== */
.contact {
    background-color: var(--light-background);
    padding: var(--spacing-xl) 0;
    position: relative;
}

.contact-container {
    display: flex;
    justify-content: center;
    margin-top: var(--spacing-lg);
}

.contact-form-container {
    width: 100%;
    max-width: 600px;
    background-color: white;
    padding: var(--spacing-lg);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-lg);
    border-top: 5px solid var(--primary-color);
}

.contact-form .form-group {
    margin-bottom: var(--spacing-md);
}

.contact-form input,
.contact-form textarea {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #ddd;
    border-radius: var(--border-radius-md);
    font-family: var(--body-font);
    transition: border-color 0.3s ease, box-shadow 0.3s ease;
}

.contact-form textarea {
    resize: vertical;
    min-height: 120px;
}

.contact-form input:focus,
.contact-form textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(0, 166, 81, 0.1);
}

.form-status {
    margin-top: var(--spacing-sm);
    padding: var(--spacing-sm);
    border-radius: var(--border-radius-md);
    display: none;
}

.form-status.success {
    display: block;
    background-color: rgba(0, 166, 81, 0.1);
    color: var(--primary-color);
    border: 1px solid var(--primary-color);
}

.form-status.error {
    display: block;
    background-color: rgba(255, 87, 34, 0.1);
    color: var(--accent-color);
    border: 1px solid var(--accent-color);
}



.faq-answer p {
    margin-top: 0;
}

.faq-answer ul,
.faq-answer ol {
    margin-top: 0;
    padding-left: var(--spacing-md);
}

.faq-answer li {
    margin-bottom: var(--spacing-xs);
}

.text-center {
    text-align: center;
}

/* Section Dividers */
.wave-divider {
    position: relative;
    height: 70px;
    margin-top: -70px;
}

.wave-divider svg {
    position: absolute;
    width: 100%;
    height: 70px;
    fill: var(--bg-light-orange);
}

.wave-divider.white svg {
    fill: var(--background-color);
}

.wave-divider.green svg {
    fill: var(--bg-light-green);
}

.wave-divider.gold svg {
    fill: var(--bg-light-gold);
}

.wave-divider.inverted {
    transform: rotate(180deg);
    margin-top: 0;
    margin-bottom: -70px;
    z-index: 1;
}

/* Animation Enhancements */
@keyframes float {
    0% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-10px);
    }
    100% {
        transform: translateY(0px);
    }
}

.animate-float {
    animation: float 6s ease-in-out infinite;
}

/* Button Enhancements */
.btn {
    position: relative;
    overflow: hidden;
}

.btn::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 5px;
    height: 5px;
    background: rgba(255, 255, 255, 0.5);
    opacity: 0;
    border-radius: 100%;
    transform: scale(1, 1) translate(-50%);
    transform-origin: 50% 50%;
}

.btn:hover::after {
    animation: ripple 1s ease-out;
}

@keyframes ripple {
    0% {
        transform: scale(0, 0);
        opacity: 0.5;
    }
    100% {
        transform: scale(20, 20);
        opacity: 0;
    }
}

/* ===== Responsive Styles ===== */
@media (max-width: 1024px) {
    /* General layout adjustments for tablets */
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .feature-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .footer-grid {
        grid-template-columns: 1fr 1fr;
        gap: var(--spacing-md);
    }

    /* Testimonial adjustments for tablets */
    .testimonial {
        flex: 0 0 calc(50% - var(--spacing-md));
        min-height: 420px;
    }

    /* Navbar adjustments for tablets */
    .nav-links {
        gap: 10px;
    }

    .nav-links li {
        margin-left: var(--spacing-sm); /* Reduce margin between nav items */
    }

    .logo img {
        height: 50px; /* Slightly smaller logo for tablets, but still prominent */
    }

    /* Make nav links text smaller on tablets */
    .nav-links a {
        font-size: 0.9rem;
    }

    /* Adjust CTA buttons for tablets */
    .cta-buttons {
        gap: 5px;
    }

    .cta-buttons .btn {
        padding: 0.5rem 0.8rem;
        font-size: 0.85rem;
    }

    /* Hero section adjustments for tablets */
    .hero {
        padding: calc(70px + var(--spacing-lg)) 0 var(--spacing-lg); /* Reduce top padding */
    }

    .hero .container {
        gap: var(--spacing-md);
        align-items: center; /* Center alignment for better balance */
    }

    .hero-content {
        max-width: 48%; /* Slightly smaller to prevent overflow */
        padding-top: 0; /* Remove top padding */
    }

    .hero h1 {
        font-size: 2.2rem;
        line-height: 1.2; /* Tighter line height for better fit */
    }

    .hero-subtitle {
        font-size: 1rem;
        margin-bottom: var(--spacing-md); /* Reduce bottom margin */
        line-height: 1.4; /* Improve readability */
    }

    .hero-cta {
        flex-wrap: wrap;
        gap: 10px; /* Smaller gap between buttons */
    }

    .hero-cta .btn {
        padding: 0.8rem 1.5rem;
        font-size: 1rem;
        margin-bottom: 5px; /* Add space between buttons when they wrap */
        min-width: 160px; /* Ensure buttons have a minimum width */
    }

    .hero-image {
        flex: 1;
        display: flex;
        justify-content: center; /* Center the image */
    }

    .hero-image img {
        max-width: 90%; /* Slightly smaller image for better fit */
        height: auto; /* Maintain aspect ratio */
    }
}

/* Specific media query for small tablets (768px-900px) */
@media (min-width: 769px) and (max-width: 900px) {
    /* Further optimize navbar for small tablets */
    .nav-links li {
        margin-left: 0.5rem;
    }

    .nav-links a {
        font-size: 0.8rem;
        padding: 0.3rem;
    }

    .cta-buttons .btn {
        padding: 0.4rem 0.6rem;
        font-size: 0.8rem;
    }

    /* Hero section adjustments for small tablets */
    .hero h1 {
        font-size: 2rem;
    }

    .hero-subtitle {
        font-size: 0.9rem;
        margin-bottom: var(--spacing-md);
    }

    .hero-cta .btn {
        padding: 0.7rem 1.2rem;
        font-size: 0.9rem;
    }
}

/* Specific media query for iPad Air and similar devices (820px width) */
@media (min-width: 800px) and (max-width: 840px), (width: 820px) and (height: 1180px) {
    /* Container adjustments */
    .container {
        padding: 0 var(--spacing-md);
    }

    /* Navbar specific fixes for iPad Air */
    nav {
        justify-content: space-between;
        padding: 0 5px;
    }

    .logo {
        flex: 0 0 auto;
    }

    .logo img {
        height: 45px; /* Increased from 32px for better visibility on iPad Air */
    }

    /* Nav links adjustments */
    .nav-links {
        flex: 1 1 auto;
        justify-content: center;
        margin: 0 10px;
    }

    .nav-links li {
        margin-left: 12px;
    }

    .nav-links a {
        font-size: 0.75rem;
        white-space: nowrap;
        padding: 5px 3px;
    }

    /* Authentication buttons */
    .cta-buttons {
        flex: 0 0 auto;
        display: flex;
        gap: 5px;
    }

    .cta-buttons .btn {
        padding: 0.4rem 0.5rem;
        font-size: 0.75rem;
        white-space: nowrap;
        min-width: 0;
    }

    .cta-buttons .btn-primary {
        padding: 0.4rem 0.6rem;
    }

    /* Specific adjustments for iPad Air in portrait mode */
    @media (width: 820px) and (height: 1180px) {
        /* Make navbar more compact */
        header {
            padding: 0.7rem 0;
        }

        nav {
            padding: 0 10px;
        }

        /* Ensure nav links are visible and properly spaced */
        .nav-links {
            display: flex !important;
            justify-content: space-between;
            width: auto;
            margin: 0 15px;
        }

        .nav-links li {
            margin-left: 8px;
        }

        /* Make authentication buttons more visible */
        .cta-buttons {
            display: flex !important;
        }

        .cta-buttons .btn {
            padding: 0.35rem 0.5rem;
            font-size: 0.7rem;
            letter-spacing: -0.2px;
        }

        .cta-buttons .btn-primary {
            background-color: var(--primary-color);
            color: white;
            padding: 0.35rem 0.6rem;
        }

        /* Hide mobile menu toggle on iPad Air */
        .mobile-menu-toggle {
            display: none !important;
        }

        /* Hero section specific fixes for iPad Air */
        .hero {
            padding: calc(65px + var(--spacing-lg)) 0 var(--spacing-lg);
        }

        .hero .container {
            flex-direction: row;
            align-items: center;
            gap: var(--spacing-md);
        }

        .hero-content {
            max-width: 48%;
            text-align: left;
            margin-bottom: 0;
        }

        .hero h1 {
            font-size: 2rem;
            line-height: 1.2;
        }

        .hero-subtitle {
            font-size: 0.95rem;
            line-height: 1.4;
        }

        .hero-cta {
            justify-content: flex-start;
            gap: 10px;
        }

        .hero-cta .btn {
            padding: 0.7rem 1.2rem;
            font-size: 0.9rem;
            min-width: 130px;
        }

        .hero-image {
            flex: 1;
            text-align: right;
        }

        .hero-image img {
            max-width: 90%;
        }
    }
}

/* Extra small devices */
@media (max-width: 480px) {
    .logo img {
        height: 35px; /* Smaller logo for very small screens */
    }

    .hero {
        padding: calc(70px + var(--spacing-md)) 0 var(--spacing-md); /* Adjusted padding for smaller header */
    }
}

@media (max-width: 768px) {
    h1 {
        font-size: 2.5rem;
    }

    h2 {
        font-size: 2rem;
    }

    /* Ensure logo is visible but not too large on mobile */
    .logo img {
        height: 40px; /* Adjusted size for mobile devices to prevent overflow */
    }

    /* Footer logo size for mobile */
    .footer-logo {
        height: 50px;
    }

    /* Contact section responsive styles */
    .contact-form-container {
        max-width: 100%;
        padding: var(--spacing-md);
    }

    /* Mobile menu styles */
    header {
        padding: 0.7rem 0; /* Reduced padding for more compact header on mobile */
    }

    .nav-links {
        display: none;
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100vh;
        background-color: white;
        flex-direction: column;
        justify-content: center; /* Center content vertically */
        align-items: center;
        z-index: 1000;
        opacity: 0;
        transform: translateY(-20px);
        transition: opacity 0.3s ease, transform 0.3s ease;
        padding: 0 2rem;
        box-sizing: border-box;
        overflow-y: auto; /* Allow scrolling if needed */
    }

    .nav-links.active {
        display: flex;
        opacity: 1;
        transform: translateY(0);
    }

    /* Show mobile CTA container when menu is active */
    .nav-links.active .mobile-cta-container {
        display: flex;
    }

    .nav-links li {
        margin: 0.8rem 0;
        margin-left: 0;
        width: 100%;
        text-align: center;
    }

    .nav-links a {
        font-size: 1.2rem;
        padding: 0.8rem 1rem;
        display: block;
        transition: all 0.3s ease;
    }

    .nav-links a:hover {
        background-color: rgba(0, 166, 81, 0.1);
        border-radius: var(--border-radius-md);
    }

    /* Mobile CTA buttons - only shown in mobile menu when active */
    .mobile-cta-container {
        width: 100%;
        margin-top: 3rem;
        flex-direction: column;
        align-items: center;
        gap: 1rem;
        padding-bottom: 2rem;
    }

    .mobile-cta-container .btn {
        width: 80%;
        max-width: 250px;
        margin: 0.5rem 0;
    }

    /* Hide desktop CTA buttons on mobile */
    .cta-buttons {
        display: none;
    }

    .mobile-menu-toggle {
        display: flex;
    }

    /* Improved hero section for mobile */
    .hero {
        padding: calc(80px + var(--spacing-md)) 0 var(--spacing-md); /* Increased top padding for mobile */
    }

    .hero .container {
        flex-direction: column;
        gap: var(--spacing-md);
    }

    .hero-content {
        text-align: center;
        margin-bottom: var(--spacing-md);
        max-width: 100%;
        padding: 0 var(--spacing-sm);
        display: flex;
        flex-direction: column;
        align-items: center;
    }

    .hero h1 {
        font-size: 2rem;
        line-height: 1.2;
        margin-bottom: var(--spacing-sm);
    }

    .hero-subtitle {
        font-size: 1rem;
        line-height: 1.4;
        margin-bottom: var(--spacing-md);
    }

    .hero-cta {
        justify-content: center;
        gap: 12px;
        width: 100%;
        display: flex;
        align-items: center;
    }

    .hero-cta .btn {
        padding: 0.7rem 1.2rem;
        font-size: 0.95rem;
        min-width: 140px;
        text-align: center;
    }

    .hero-image {
        width: 100%;
        text-align: center;
    }

    .hero-image img {
        max-width: 85%;
        margin: 0 auto;
    }

    .steps {
        flex-direction: column;
        gap: var(--spacing-lg);
    }

    .step:not(:last-child)::after {
        display: none;
    }

    .story-cards {
        grid-template-columns: 1fr;
    }

    .testimonial-slider {
        padding: var(--spacing-md) var(--spacing-sm);
    }

    .testimonial {
        flex: 0 0 85%;
        margin-bottom: var(--spacing-md);
        min-height: 450px;
    }

    .testimonial-image {
        padding-bottom: 65%; /* Slightly shorter aspect ratio on mobile */
    }

    .signup .container {
        flex-direction: column-reverse;
    }

    .signup-image {
        margin-bottom: var(--spacing-lg);
    }

    .footer-grid {
        grid-template-columns: 1fr;
    }

    .footer-bottom {
        flex-direction: column;
        text-align: center;
        gap: var(--spacing-sm);
    }
}

@media (max-width: 480px) {
    .stats-grid {
        grid-template-columns: 1fr;
    }

    .feature-grid {
        grid-template-columns: 1fr;
    }

    /* Further optimize hero section for small mobile devices */
    .hero {
        padding: calc(55px + var(--spacing-sm)) 0 var(--spacing-sm);
    }

    .hero h1 {
        font-size: 1.8rem;
        line-height: 1.2;
    }

    .hero-subtitle {
        font-size: 0.9rem;
        margin-bottom: var(--spacing-sm);
    }

    .hero-cta {
        flex-direction: column;
        gap: var(--spacing-sm);
        width: 100%;
        align-items: center;
        display: flex;
    }

    .hero-cta .btn {
        width: 100%;
        max-width: 250px;
        padding: 0.7rem 1rem;
        text-align: center;
        margin: 0 auto;
    }

    .hero-image img {
        max-width: 90%;
    }
}

/* ===== Blog Section ===== */
.blog {
    padding: var(--spacing-xl) 0;
    background: var(--bg-gradient-green);
    position: relative;
    overflow: hidden;
}

.blog::before {
    content: '';
    position: absolute;
    top: 10%;
    right: -150px;
    width: 400px;
    height: 400px;
    border-radius: 50%;
    background: radial-gradient(circle, rgba(0, 166, 81, 0.08) 0%, rgba(0, 166, 81, 0) 70%);
    z-index: 0;
}

.blog::after {
    content: '';
    position: absolute;
    bottom: 20%;
    left: -150px;
    width: 300px;
    height: 300px;
    border-radius: 50%;
    background: radial-gradient(circle, rgba(255, 193, 7, 0.08) 0%, rgba(255, 193, 7, 0) 70%);
    z-index: 0;
}

.blog .container {
    position: relative;
    z-index: 1;
}

.blog-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-lg);
}

.blog-card {
    background: white;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-md);
    overflow: hidden;
    transition: all 0.3s ease;
    position: relative;
}

.blog-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.blog-image {
    position: relative;
    width: 100%;
    height: 200px;
    overflow: hidden;
}

.blog-image::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(0, 166, 81, 0.1) 0%, rgba(255, 193, 7, 0.1) 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
}

.blog-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
    border-radius: 0;
    background-color: var(--light-background);
}

.blog-card:hover .blog-image img {
    transform: scale(1.05);
}

.blog-card:hover .blog-image::after {
    opacity: 1;
}

.blog-content {
    padding: var(--spacing-md);
}

.blog-title {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: var(--spacing-sm);
    line-height: 1.4;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.blog-excerpt {
    color: var(--light-text);
    font-size: 0.9rem;
    line-height: 1.5;
    margin-bottom: var(--spacing-md);
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.blog-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: var(--spacing-sm);
    border-top: 1px solid #eee;
}

.blog-date {
    font-size: 0.8rem;
    color: var(--light-text);
    display: flex;
    align-items: center;
    gap: 0.3rem;
}

.blog-date i {
    color: var(--primary-color);
}

.blog-read-more {
    color: var(--primary-color);
    font-weight: 600;
    font-size: 0.9rem;
    text-decoration: none;
    display: flex;
    align-items: center;
    gap: 0.3rem;
    transition: all 0.3s ease;
}

.blog-read-more:hover {
    color: var(--accent-color);
    transform: translateX(3px);
}

.blog-read-more i {
    font-size: 0.8rem;
    transition: transform 0.3s ease;
}

.blog-read-more:hover i {
    transform: translateX(3px);
}

.blog-cta {
    text-align: center;
    margin-top: var(--spacing-lg);
}

.blog-cta .btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 1rem 2rem;
    font-size: 1.1rem;
}

.blog-cta .btn i {
    font-size: 0.9rem;
}

/* Blog Section Responsive Design */
@media (max-width: 768px) {
    .blog-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
    }

    .blog-card {
        margin: 0 auto;
        max-width: 400px;
    }

    .blog-meta {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-sm);
    }

    .blog-read-more {
        align-self: flex-end;
    }
}

@media (max-width: 480px) {
    .blog {
        padding: var(--spacing-lg) 0;
    }

    .blog-content {
        padding: var(--spacing-sm);
    }

    .blog-title {
        font-size: 1.1rem;
    }

    .blog-excerpt {
        font-size: 0.85rem;
    }

    .blog-cta .btn {
        width: 100%;
        max-width: 250px;
    }
}

/* ===== Leaderboard Styles ===== */
.leaderboard-rank {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.rank-badge {
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
    padding: 0.375rem 0.75rem;
    border-radius: 0.375rem;
    font-weight: 600;
    font-size: 0.875rem;
}

.rank-badge.gold {
    background: linear-gradient(135deg, #FFD700, #FFA500);
    color: #333;
    box-shadow: 0 2px 4px rgba(255, 215, 0, 0.3);
}

.rank-badge.silver {
    background: linear-gradient(135deg, #C0C0C0, #A8A8A8);
    color: #333;
    box-shadow: 0 2px 4px rgba(192, 192, 192, 0.3);
}

.rank-badge.bronze {
    background: linear-gradient(135deg, #CD7F32, #B8860B);
    color: white;
    box-shadow: 0 2px 4px rgba(205, 127, 50, 0.3);
}

.rank-badge.default {
    background: linear-gradient(135deg, var(--primary-color), #008c44);
    color: white;
    box-shadow: 0 2px 4px rgba(0, 166, 81, 0.3);
}

.leaderboard-table {
    border-radius: 0.5rem;
    overflow: hidden;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.leaderboard-table .table-row {
    transition: all 0.3s ease;
}

.leaderboard-table .table-row:hover {
    background-color: rgba(0, 166, 81, 0.05);
    transform: translateY(-1px);
}

.leaderboard-table .user-row {
    background-color: rgba(0, 166, 81, 0.1);
    border-left: 4px solid var(--primary-color);
}

.leaderboard-stats-card {
    border: none;
    border-radius: 0.75rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.leaderboard-stats-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.leaderboard-preview {
    background: linear-gradient(135deg, rgba(255, 193, 7, 0.1), rgba(0, 166, 81, 0.1));
    border-radius: 0.75rem;
    padding: 1.5rem;
    margin-top: 1rem;
}

.rank-icon {
    font-size: 1.2em;
    margin-right: 0.25rem;
}

.rank-icon.gold {
    color: #FFD700;
}

.rank-icon.silver {
    color: #C0C0C0;
}

.rank-icon.bronze {
    color: #CD7F32;
}

.rank-icon.default {
    color: var(--primary-color);
}

/* Responsive adjustments for leaderboard */
@media (max-width: 768px) {
    .leaderboard-table {
        font-size: 0.875rem;
    }

    .rank-badge {
        padding: 0.25rem 0.5rem;
        font-size: 0.75rem;
    }

    .leaderboard-stats-card .card-body {
        padding: 1rem;
    }

    .leaderboard-preview {
        padding: 1rem;
    }
}
