<?php
// Set page title
$page_title = 'View Ticket';

// Include header
require_once 'includes/header.php';

// Check if ticket ID is provided
if (!isset($_GET['id']) || empty($_GET['id'])) {
    redirect('support_tickets.php');
}

$ticket_id = $_GET['id'];

// Get ticket details
$query = "SELECT t.*, u.name as user_name, a.name as admin_name 
          FROM support_tickets t 
          LEFT JOIN users u ON t.user_id = u.id 
          LEFT JOIN users a ON t.admin_id = a.id 
          WHERE t.id = :id AND t.user_id = :user_id";
$stmt = $db->prepare($query);
$stmt->bindParam(':id', $ticket_id);
$stmt->bindParam(':user_id', $user_id);
$stmt->execute();
$ticket = $stmt->fetch(PDO::FETCH_OBJ);

// If ticket not found or doesn't belong to user, redirect
if (!$ticket) {
    redirect('support_tickets.php');
}

// Process reply form
$reply_message = '';
$reply_err = '';
$success_message = '';
$error_message = '';

if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['submit_reply'])) {
    // Validate reply message
    if (empty(trim($_POST['reply_message']))) {
        $reply_err = 'Please enter your reply.';
    } else {
        $reply_message = sanitize($_POST['reply_message']);
    }
    
    // Process file upload if provided
    $attachment = null;
    if (isset($_FILES['reply_attachment']) && $_FILES['reply_attachment']['error'] != UPLOAD_ERR_NO_FILE) {
        $upload_result = upload_file($_FILES['reply_attachment'], 'uploads/support/');
        
        if (!$upload_result['success']) {
            $file_err = $upload_result['message'];
        } else {
            $attachment = $upload_result['filename'];
        }
    }
    
    // If no errors, process the reply
    if (empty($reply_err) && empty($file_err)) {
        // Insert reply as a new message
        $query = "INSERT INTO support_ticket_replies (ticket_id, user_id, message, attachment, is_admin) 
                  VALUES (:ticket_id, :user_id, :message, :attachment, 0)";
        $stmt = $db->prepare($query);
        
        $stmt->bindParam(':ticket_id', $ticket_id);
        $stmt->bindParam(':user_id', $user_id);
        $stmt->bindParam(':message', $reply_message);
        $stmt->bindParam(':attachment', $attachment);
        
        if ($stmt->execute()) {
            // Update ticket status to open if it was closed
            if ($ticket->status == 'closed') {
                $query = "UPDATE support_tickets SET status = 'open', updated_at = NOW() WHERE id = :id";
                $stmt = $db->prepare($query);
                $stmt->bindParam(':id', $ticket_id);
                $stmt->execute();
            }
            
            // Create notification for admins
            $query = "SELECT id FROM users WHERE role = 'admin'";
            $stmt = $db->prepare($query);
            $stmt->execute();
            $admins = $stmt->fetchAll(PDO::FETCH_OBJ);
            
            foreach ($admins as $admin) {
                create_notification(
                    $admin->id,
                    'Support Ticket Reply',
                    "User {$_SESSION['user_name']} has replied to ticket #{$ticket->ticket_number}.",
                    'support',
                    $db
                );
            }
            
            $success_message = 'Your reply has been sent successfully.';
            $reply_message = '';
            
            // Refresh ticket data
            $stmt = $db->prepare($query);
            $stmt->bindParam(':id', $ticket_id);
            $stmt->bindParam(':user_id', $user_id);
            $stmt->execute();
            $ticket = $stmt->fetch(PDO::FETCH_OBJ);
        } else {
            $error_message = 'An error occurred while sending your reply. Please try again.';
        }
    }
}

// Get ticket replies
$query = "SELECT r.*, u.name as user_name 
          FROM support_ticket_replies r 
          LEFT JOIN users u ON r.user_id = u.id 
          WHERE r.ticket_id = :ticket_id 
          ORDER BY r.created_at ASC";
$stmt = $db->prepare($query);
$stmt->bindParam(':ticket_id', $ticket_id);
$stmt->execute();
$replies = $stmt->fetchAll(PDO::FETCH_OBJ);
?>

<div class="container-fluid">
    <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
        <h1 class="h2">Ticket #<?php echo $ticket->ticket_number; ?></h1>
        <div class="btn-toolbar mb-2 mb-md-0">
            <a href="support_tickets.php" class="btn btn-sm btn-secondary">
                <i class="fas fa-arrow-left"></i> Back to Tickets
            </a>
        </div>
    </div>
    
    <?php if (!empty($success_message)): ?>
        <div class="alert alert-success"><?php echo $success_message; ?></div>
    <?php endif; ?>
    
    <?php if (!empty($error_message)): ?>
        <div class="alert alert-danger"><?php echo $error_message; ?></div>
    <?php endif; ?>
    
    <!-- Ticket Details -->
    <div class="card mb-4">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="mb-0"><?php echo $ticket->subject; ?></h5>
            <span class="badge badge-<?php echo ($ticket->status == 'open') ? 'primary' : (($ticket->status == 'in_progress') ? 'warning' : 'success'); ?>">
                <?php echo ucfirst($ticket->status); ?>
            </span>
        </div>
        <div class="card-body">
            <div class="row mb-3">
                <div class="col-md-6">
                    <p><strong>Category:</strong> <?php echo $ticket->category; ?></p>
                    <p><strong>Created:</strong> <?php echo format_date($ticket->created_at); ?></p>
                </div>
                <div class="col-md-6">
                    <p><strong>Last Updated:</strong> <?php echo format_date($ticket->updated_at); ?></p>
                    <?php if ($ticket->admin_id): ?>
                        <p><strong>Assigned To:</strong> <?php echo $ticket->admin_name; ?></p>
                    <?php endif; ?>
                </div>
            </div>
            
            <div class="ticket-message p-3 bg-light rounded mb-3">
                <div class="d-flex justify-content-between mb-2">
                    <span><strong><?php echo $_SESSION['user_name']; ?></strong></span>
                    <small><?php echo format_date($ticket->created_at); ?></small>
                </div>
                <p><?php echo nl2br(htmlspecialchars($ticket->message)); ?></p>
                <?php if ($ticket->attachment): ?>
                    <div class="mt-2">
                        <a href="uploads/support/<?php echo $ticket->attachment; ?>" target="_blank" class="btn btn-sm btn-outline-primary">
                            <i class="fas fa-paperclip"></i> View Attachment
                        </a>
                    </div>
                <?php endif; ?>
            </div>
            
            <?php if ($ticket->admin_response): ?>
                <div class="ticket-response p-3 bg-info text-white rounded mb-3">
                    <div class="d-flex justify-content-between mb-2">
                        <span><strong>Support Team</strong></span>
                        <small><?php echo format_date($ticket->updated_at); ?></small>
                    </div>
                    <p><?php echo nl2br(htmlspecialchars($ticket->admin_response)); ?></p>
                </div>
            <?php endif; ?>
            
            <!-- Ticket Replies -->
            <?php if (!empty($replies)): ?>
                <h6 class="mt-4 mb-3">Conversation History</h6>
                <?php foreach ($replies as $reply): ?>
                    <div class="ticket-reply p-3 <?php echo $reply->is_admin ? 'bg-info text-white' : 'bg-light'; ?> rounded mb-3">
                        <div class="d-flex justify-content-between mb-2">
                            <span><strong><?php echo $reply->is_admin ? 'Support Team' : $reply->user_name; ?></strong></span>
                            <small><?php echo format_date($reply->created_at); ?></small>
                        </div>
                        <p><?php echo nl2br(htmlspecialchars($reply->message)); ?></p>
                        <?php if ($reply->attachment): ?>
                            <div class="mt-2">
                                <a href="uploads/support/<?php echo $reply->attachment; ?>" target="_blank" class="btn btn-sm btn-outline-<?php echo $reply->is_admin ? 'light' : 'primary'; ?>">
                                    <i class="fas fa-paperclip"></i> View Attachment
                                </a>
                            </div>
                        <?php endif; ?>
                    </div>
                <?php endforeach; ?>
            <?php endif; ?>
            
            <!-- Reply Form -->
            <?php if ($ticket->status != 'closed'): ?>
                <div class="card mt-4">
                    <div class="card-header">
                        <h6 class="mb-0">Add Reply</h6>
                    </div>
                    <div class="card-body">
                        <form action="<?php echo htmlspecialchars($_SERVER["PHP_SELF"] . '?id=' . $ticket_id); ?>" method="post" enctype="multipart/form-data">
                            <div class="form-group">
                                <textarea name="reply_message" class="form-control <?php echo (!empty($reply_err)) ? 'is-invalid' : ''; ?>" rows="4" placeholder="Type your reply here..."><?php echo $reply_message; ?></textarea>
                                <span class="invalid-feedback"><?php echo $reply_err; ?></span>
                            </div>
                            
                            <div class="form-group">
                                <div class="custom-file">
                                    <input type="file" name="reply_attachment" class="custom-file-input" id="reply_attachment">
                                    <label class="custom-file-label" for="reply_attachment">Attach a file (optional)</label>
                                </div>
                                <small class="form-text text-muted">Accepted formats: JPG, PNG, PDF. Max size: 20MB</small>
                            </div>
                            
                            <button type="submit" name="submit_reply" class="btn btn-primary">Send Reply</button>
                        </form>
                    </div>
                </div>
            <?php else: ?>
                <div class="alert alert-info mt-4">
                    <p class="mb-0">This ticket is closed. If you need further assistance, please create a new ticket.</p>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<script>
// Update file input label with selected filename
$(document).ready(function() {
    $('.custom-file-input').on('change', function() {
        var fileName = $(this).val().split('\\').pop();
        $(this).next('.custom-file-label').html(fileName);
    });
});
</script>

<?php
// Include footer
require_once 'includes/footer.php';
?>
