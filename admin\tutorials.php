<?php
// Set page title
$page_title = 'Tutorials Management';

// Include configuration
require_once '../config/config.php';
require_once '../includes/functions.php';
require_once '../includes/tutorial_functions.php';
require_once '../database/db_connect.php';

// Start session
start_session();

// Check if user is logged in and is admin
if (!is_logged_in() || !is_admin()) {
    redirect('../login.php');
}

// Create database instance
$database = new Database();
$db = $database->getConnection();

// Get user ID
$user_id = $_SESSION['user_id'];

// Handle actions
$success_message = '';
$error_message = '';

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $action = sanitize($_POST['action']);

    if ($action == 'delete' && isset($_POST['tutorial_id'])) {
        $tutorial_id = (int)$_POST['tutorial_id'];

        if (delete_tutorial($db, $tutorial_id)) {
            $success_message = 'Tutorial deleted successfully.';
        } else {
            $error_message = 'Failed to delete tutorial.';
        }
    } elseif ($action == 'update_order' && isset($_POST['tutorial_orders'])) {
        $tutorial_orders = $_POST['tutorial_orders'];
        $updated = 0;

        foreach ($tutorial_orders as $tutorial_id => $order) {
            if (update_tutorial_order($db, (int)$tutorial_id, (int)$order)) {
                $updated++;
            }
        }

        if ($updated > 0) {
            $success_message = "Updated display order for $updated tutorials.";
        } else {
            $error_message = 'Failed to update tutorial order.';
        }
    }
}

// Get selected category filter
$selected_category = isset($_GET['category']) ? sanitize($_GET['category']) : '';

// Get all tutorials
$tutorials = get_all_tutorials($db, $selected_category);

// Get all categories for filter
$categories = get_tutorial_categories($db);
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - <?php echo SITE_NAME; ?></title>
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.1/css/all.min.css">
    <link rel="stylesheet" href="../assets/css/admin.css">
    <link rel="stylesheet" href="../assets/css/admin-dark-mode.css">
</head>
<body>
    <?php include 'includes/admin_navbar.php'; ?>

    <div class="container-fluid">
        <div class="row">
            <?php include 'includes/admin_sidebar.php'; ?>

            <main role="main" class="col-md-9 ml-sm-auto col-lg-10 px-md-4 py-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">
                        <i class="fas fa-graduation-cap"></i>
                        <?php echo $page_title; ?>
                    </h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <a href="tutorial_form.php" class="btn btn-success">
                            <i class="fas fa-plus"></i> Add New Tutorial
                        </a>
                    </div>
                </div>

                <?php if (!empty($success_message)): ?>
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <?php echo $success_message; ?>
                        <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                <?php endif; ?>

                <?php if (!empty($error_message)): ?>
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <?php echo $error_message; ?>
                        <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                <?php endif; ?>

                <!-- Category Filter -->
                <?php if (!empty($categories)): ?>
                <div class="row mb-4">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-body">
                                <h6 class="card-title">Filter by Category</h6>
                                <form method="GET" action="tutorials.php">
                                    <div class="input-group">
                                        <select name="category" class="form-control">
                                            <option value="">All Categories</option>
                                            <?php foreach ($categories as $category): ?>
                                                <option value="<?php echo htmlspecialchars($category); ?>"
                                                        <?php echo $selected_category === $category ? 'selected' : ''; ?>>
                                                    <?php echo htmlspecialchars(ucfirst($category)); ?>
                                                </option>
                                            <?php endforeach; ?>
                                        </select>
                                        <div class="input-group-append">
                                            <button class="btn btn-outline-secondary" type="submit">
                                                <i class="fas fa-filter"></i> Filter
                                            </button>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
                <?php endif; ?>

                <!-- Tutorials Table -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-list"></i>
                            Tutorials
                            <?php if ($selected_category): ?>
                                - <?php echo htmlspecialchars(ucfirst($selected_category)); ?> Category
                            <?php endif; ?>
                        </h5>
                    </div>
                    <div class="card-body">
                        <?php if (empty($tutorials)): ?>
                            <div class="text-center py-5">
                                <i class="fas fa-graduation-cap fa-3x text-muted mb-3"></i>
                                <h4 class="text-muted">No Tutorials Found</h4>
                                <p class="text-muted">
                                    <?php if ($selected_category): ?>
                                        No tutorials found in the "<?php echo htmlspecialchars(ucfirst($selected_category)); ?>" category.
                                        <br><a href="tutorials.php" class="btn btn-outline-secondary mt-2">View All Tutorials</a>
                                    <?php else: ?>
                                        Start by creating your first tutorial.
                                    <?php endif; ?>
                                </p>
                                <a href="tutorial_form.php" class="btn btn-success">
                                    <i class="fas fa-plus"></i> Create Tutorial
                                </a>
                            </div>
                        <?php else: ?>
                            <form method="POST" action="tutorials.php" id="orderForm">
                                <input type="hidden" name="action" value="update_order">
                                <div class="table-responsive admin-tutorial-table">
                                    <table class="table table-striped">
                                        <thead>
                                            <tr>
                                                <th>Order</th>
                                                <th>Title</th>
                                                <th>Category</th>
                                                <th>Status</th>
                                                <th>Created By</th>
                                                <th>Created</th>
                                                <th>Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($tutorials as $tutorial): ?>
                                                <tr>
                                                    <td>
                                                        <input type="number"
                                                               name="tutorial_orders[<?php echo $tutorial->id; ?>]"
                                                               value="<?php echo $tutorial->display_order; ?>"
                                                               class="form-control form-control-sm tutorial-order-input"
                                                               style="width: 80px;"
                                                               min="0">
                                                    </td>
                                                    <td>
                                                        <strong><?php echo htmlspecialchars($tutorial->title); ?></strong>
                                                        <?php if ($tutorial->image_file || $tutorial->video_file || $tutorial->video_url): ?>
                                                            <br>
                                                            <div class="tutorial-media-badges">
                                                                <?php if ($tutorial->image_file): ?>
                                                                    <span class="badge badge-info">
                                                                        <i class="fas fa-image"></i> Image
                                                                    </span>
                                                                <?php endif; ?>
                                                                <?php if ($tutorial->video_file || $tutorial->video_url): ?>
                                                                    <span class="badge badge-primary">
                                                                        <i class="fas fa-play"></i> Video
                                                                    </span>
                                                                <?php endif; ?>
                                                            </div>
                                                        <?php endif; ?>
                                                    </td>
                                                    <td>
                                                        <span class="badge badge-secondary">
                                                            <?php echo htmlspecialchars(ucfirst($tutorial->category)); ?>
                                                        </span>
                                                    </td>
                                                    <td>
                                                        <span class="tutorial-status-badge"><?php echo get_tutorial_status_badge($tutorial->status); ?></span>
                                                    </td>
                                                    <td><?php echo htmlspecialchars($tutorial->created_by_name); ?></td>
                                                    <td><?php echo date('M j, Y', strtotime($tutorial->created_at)); ?></td>
                                                    <td>
                                                        <div class="btn-group" role="group">
                                                            <a href="tutorial_form.php?id=<?php echo $tutorial->id; ?>"
                                                               class="btn btn-sm btn-outline-primary"
                                                               title="Edit">
                                                                <i class="fas fa-edit"></i>
                                                            </a>
                                                            <button type="button"
                                                                    class="btn btn-sm btn-outline-danger"
                                                                    onclick="confirmDelete(<?php echo $tutorial->id; ?>, '<?php echo htmlspecialchars($tutorial->title); ?>')"
                                                                    title="Delete">
                                                                <i class="fas fa-trash"></i>
                                                            </button>
                                                        </div>
                                                    </td>
                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                                <div class="mt-3">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save"></i> Update Display Order
                                    </button>
                                </div>
                            </form>
                        <?php endif; ?>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <div class="modal fade" id="deleteModal" tabindex="-1" role="dialog" aria-labelledby="deleteModalLabel" aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="deleteModalLabel">Confirm Delete</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <p>Are you sure you want to delete the tutorial "<span id="tutorialTitle"></span>"?</p>
                    <p class="text-danger"><strong>This action cannot be undone.</strong></p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                    <form method="POST" action="tutorials.php" style="display: inline;">
                        <input type="hidden" name="action" value="delete">
                        <input type="hidden" name="tutorial_id" id="deleteTutorialId">
                        <button type="submit" class="btn btn-danger">Delete Tutorial</button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.5.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.5.2/dist/js/bootstrap.bundle.min.js"></script>
    <script src="../assets/js/admin.js"></script>
    <script>
        function confirmDelete(tutorialId, tutorialTitle) {
            document.getElementById('deleteTutorialId').value = tutorialId;
            document.getElementById('tutorialTitle').textContent = tutorialTitle;
            $('#deleteModal').modal('show');
        }
    </script>
</body>
</html>
