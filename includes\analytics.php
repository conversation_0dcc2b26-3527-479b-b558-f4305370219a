<?php
/**
 * Analytics Integration
 *
 * This file contains code for integrating analytics services like Google Analytics.
 * It's included in the head section of pages to track user behavior.
 */

// Check if analytics is enabled in config
if (defined('ENABLE_ANALYTICS') && ENABLE_ANALYTICS === true) {
    // Google Analytics tracking code
    if (defined('GA_TRACKING_ID') && !empty(GA_TRACKING_ID)) {
        ?>
        <!-- Global site tag (gtag.js) - Google Analytics -->
        <script async src="https://www.googletagmanager.com/gtag/js?id=<?php echo GA_TRACKING_ID; ?>"></script>
        <script>
            window.dataLayer = window.dataLayer || [];
            function gtag(){dataLayer.push(arguments);}
            gtag('js', new Date());
            gtag('config', '<?php echo GA_TRACKING_ID; ?>');
        </script>
        <?php
    }
    
    // Facebook Pixel code
    if (defined('FB_PIXEL_ID') && !empty(FB_PIXEL_ID)) {
        ?>
        <!-- Facebook Pixel Code -->
        <script>
            !function(f,b,e,v,n,t,s)
            {if(f.fbq)return;n=f.fbq=function(){n.callMethod?
            n.callMethod.apply(n,arguments):n.queue.push(arguments)};
            if(!f._fbq)f._fbq=n;n.push=n;n.loaded=!0;n.version='2.0';
            n.queue=[];t=b.createElement(e);t.async=!0;
            t.src=v;s=b.getElementsByTagName(e)[0];
            s.parentNode.insertBefore(t,s)}(window, document,'script',
            'https://connect.facebook.net/en_US/fbevents.js');
            fbq('init', '<?php echo FB_PIXEL_ID; ?>');
            fbq('track', 'PageView');
        </script>
        <noscript>
            <img height="1" width="1" style="display:none" 
                src="https://www.facebook.com/tr?id=<?php echo FB_PIXEL_ID; ?>&ev=PageView&noscript=1"/>
        </noscript>
        <!-- End Facebook Pixel Code -->
        <?php
    }
}
?>
