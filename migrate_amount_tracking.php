<?php
/**
 * Migration script to add amount tracking to existing users in queue
 * This ensures the amount-aware matching system works correctly for all users
 */

// Include configuration
require_once 'config/config.php';
require_once 'includes/functions.php';
require_once 'database/db_connect.php';

// Create database instance
$database = new Database();
$db = $database->getConnection();

echo "P2P Donate Amount Tracking Migration\n";
echo "====================================\n\n";

// Check if amount_to_receive and original_pledge_amount columns exist
echo "1. Checking database schema...\n";

$query = "SHOW COLUMNS FROM users LIKE 'amount_to_receive'";
$stmt = $db->prepare($query);
$stmt->execute();
$amount_column_exists = $stmt->fetch(PDO::FETCH_OBJ);

$query = "SHOW COLUMNS FROM users LIKE 'original_pledge_amount'";
$stmt = $db->prepare($query);
$stmt->execute();
$original_column_exists = $stmt->fetch(PDO::FETCH_OBJ);

if (!$amount_column_exists) {
    echo "   Adding amount_to_receive column...\n";
    $query = "ALTER TABLE users ADD COLUMN amount_to_receive DECIMAL(10, 2) DEFAULT 0.00";
    $stmt = $db->prepare($query);
    if ($stmt->execute()) {
        echo "   ✓ amount_to_receive column added successfully\n";
    } else {
        echo "   ✗ Error adding amount_to_receive column\n";
        exit(1);
    }
} else {
    echo "   ✓ amount_to_receive column already exists\n";
}

if (!$original_column_exists) {
    echo "   Adding original_pledge_amount column...\n";
    $query = "ALTER TABLE users ADD COLUMN original_pledge_amount DECIMAL(10, 2) DEFAULT 0.00";
    $stmt = $db->prepare($query);
    if ($stmt->execute()) {
        echo "   ✓ original_pledge_amount column added successfully\n";
    } else {
        echo "   ✗ Error adding original_pledge_amount column\n";
        exit(1);
    }
} else {
    echo "   ✓ original_pledge_amount column already exists\n";
}

echo "\n2. Finding users in queue without amount tracking...\n";

// Find users in queue who don't have amount tracking set up
$query = "SELECT u.id, u.name, u.email, u.pledges_to_receive, u.amount_to_receive, u.original_pledge_amount,
          (SELECT COUNT(*) FROM pledges WHERE user_id = u.id AND status = 'completed') as pledges_made,
          (SELECT COUNT(*) FROM matches WHERE receiver_id = u.id AND status = 'completed') as pledges_received,
          (SELECT amount FROM pledges WHERE user_id = u.id AND status = 'completed' ORDER BY created_at DESC LIMIT 1) as last_pledge_amount
          FROM users u
          WHERE u.pledges_to_receive > 0 
          AND (u.amount_to_receive = 0 OR u.amount_to_receive IS NULL)";
$stmt = $db->prepare($query);
$stmt->execute();
$users_to_migrate = $stmt->fetchAll(PDO::FETCH_OBJ);

echo "   Found " . count($users_to_migrate) . " users needing migration\n\n";

if (count($users_to_migrate) == 0) {
    echo "✓ All users in queue already have amount tracking set up!\n";
    echo "\nMigration completed successfully.\n";
    exit(0);
}

echo "3. Migrating users to amount-aware system...\n\n";

$migrated_count = 0;
$failed_count = 0;

foreach ($users_to_migrate as $user) {
    echo "User: {$user->name} (ID: {$user->id})\n";
    echo "   Current state: {$user->pledges_to_receive} pledges to receive, {$user->pledges_received} received\n";
    
    // Try to determine the original pledge amount
    $original_pledge_amount = null;
    
    if ($user->last_pledge_amount) {
        // Use the last pledge amount as reference
        $original_pledge_amount = $user->last_pledge_amount;
        echo "   Using last pledge amount: GHS {$original_pledge_amount}\n";
    } else {
        // Try to find any completed pledge for this user
        $query = "SELECT amount FROM pledges WHERE user_id = :user_id AND status = 'completed' LIMIT 1";
        $stmt = $db->prepare($query);
        $stmt->bindParam(':user_id', $user->id);
        $stmt->execute();
        $pledge = $stmt->fetch(PDO::FETCH_OBJ);
        
        if ($pledge) {
            $original_pledge_amount = $pledge->amount;
            echo "   Using any completed pledge amount: GHS {$original_pledge_amount}\n";
        } else {
            // Default to the most common pledge amount (GHS 50)
            $original_pledge_amount = 50;
            echo "   No pledge history found, using default: GHS {$original_pledge_amount}\n";
        }
    }
    
    // Calculate how much they should still receive
    $pledges_received = $user->pledges_received;
    $total_expected = $original_pledge_amount * 2; // Double the original pledge
    $amount_already_received = $pledges_received * $original_pledge_amount; // Assume they received the same amount each time
    $amount_to_receive = max(0, $total_expected - $amount_already_received);
    
    echo "   Expected total: GHS {$total_expected} (GHS {$original_pledge_amount} × 2)\n";
    echo "   Already received: GHS {$amount_already_received} ({$pledges_received} × GHS {$original_pledge_amount})\n";
    echo "   Still to receive: GHS {$amount_to_receive}\n";
    
    // Update the user's amount tracking
    $query = "UPDATE users SET 
              amount_to_receive = :amount_to_receive,
              original_pledge_amount = :original_pledge_amount
              WHERE id = :user_id";
    $stmt = $db->prepare($query);
    $stmt->bindParam(':amount_to_receive', $amount_to_receive);
    $stmt->bindParam(':original_pledge_amount', $original_pledge_amount);
    $stmt->bindParam(':user_id', $user->id);
    
    if ($stmt->execute()) {
        echo "   ✓ Migration successful\n";
        $migrated_count++;
        
        // If they've already received their full amount, remove them from queue
        if ($amount_to_receive <= 0) {
            $query = "UPDATE users SET pledges_to_receive = 0 WHERE id = :user_id";
            $stmt = $db->prepare($query);
            $stmt->bindParam(':user_id', $user->id);
            $stmt->execute();
            echo "   ✓ User removed from queue (already received full amount)\n";
        }
    } else {
        echo "   ✗ Migration failed\n";
        $failed_count++;
    }
    
    echo "\n";
}

echo "Migration Summary:\n";
echo "==================\n";
echo "✓ Successfully migrated: {$migrated_count} users\n";
echo "✗ Failed migrations: {$failed_count} users\n";

if ($failed_count > 0) {
    echo "\n⚠️  Some migrations failed. Please check the database manually.\n";
    exit(1);
} else {
    echo "\n🎉 All migrations completed successfully!\n";
    echo "\nThe amount-aware matching system is now fully operational.\n";
    echo "Users will now receive exactly double their pledge amount.\n";
}
?>
