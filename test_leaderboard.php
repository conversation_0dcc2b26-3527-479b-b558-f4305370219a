<?php
/**
 * Test script for the referral leaderboard functionality
 * This script tests the leaderboard functions and displays sample data
 */

// Include necessary files
require_once 'config/config.php';
require_once 'database/db_connect.php';
require_once 'includes/functions.php';
require_once 'includes/leaderboard_functions.php';

// Create database instance
$database = new Database();
$db = $database->getConnection();

echo "<h1>P2P Donate Leaderboard Test</h1>";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
    .success { color: green; }
    .error { color: red; }
    .info { color: blue; }
    table { border-collapse: collapse; width: 100%; margin: 10px 0; }
    th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
    th { background-color: #f2f2f2; }
</style>";

// Test 1: Check if leaderboard functions exist
echo "<div class='test-section'>";
echo "<h2>Test 1: Function Availability</h2>";

$functions_to_test = [
    'get_top_referrers',
    'get_user_referral_rank',
    'get_leaderboard_stats',
    'get_user_referral_summary',
    'format_rank',
    'get_rank_badge_class',
    'get_rank_icon'
];

foreach ($functions_to_test as $function) {
    if (function_exists($function)) {
        echo "<span class='success'>✓ Function '$function' exists</span><br>";
    } else {
        echo "<span class='error'>✗ Function '$function' missing</span><br>";
    }
}
echo "</div>";

// Test 2: Database connectivity and table structure
echo "<div class='test-section'>";
echo "<h2>Test 2: Database Structure</h2>";

try {
    // Check if referrals table exists
    $query = "SHOW TABLES LIKE 'referrals'";
    $stmt = $db->prepare($query);
    $stmt->execute();
    if ($stmt->rowCount() > 0) {
        echo "<span class='success'>✓ Referrals table exists</span><br>";
    } else {
        echo "<span class='error'>✗ Referrals table missing</span><br>";
    }

    // Check if users table has referral columns
    $query = "SHOW COLUMNS FROM users LIKE 'referral_code'";
    $stmt = $db->prepare($query);
    $stmt->execute();
    if ($stmt->rowCount() > 0) {
        echo "<span class='success'>✓ Users table has referral_code column</span><br>";
    } else {
        echo "<span class='error'>✗ Users table missing referral_code column</span><br>";
    }

    $query = "SHOW COLUMNS FROM users LIKE 'referred_by'";
    $stmt = $db->prepare($query);
    $stmt->execute();
    if ($stmt->rowCount() > 0) {
        echo "<span class='success'>✓ Users table has referred_by column</span><br>";
    } else {
        echo "<span class='error'>✗ Users table missing referred_by column</span><br>";
    }

} catch (Exception $e) {
    echo "<span class='error'>✗ Database error: " . $e->getMessage() . "</span><br>";
}
echo "</div>";

// Test 3: Get leaderboard statistics
echo "<div class='test-section'>";
echo "<h2>Test 3: Leaderboard Statistics</h2>";

try {
    $stats = get_leaderboard_stats($db);
    echo "<table>";
    echo "<tr><th>Statistic</th><th>Value</th></tr>";
    echo "<tr><td>Total Referrers</td><td>" . ($stats->total_referrers ?? 0) . "</td></tr>";
    echo "<tr><td>Total Referrals</td><td>" . ($stats->total_referrals ?? 0) . "</td></tr>";
    echo "<tr><td>Completed Referrals</td><td>" . ($stats->completed_referrals ?? 0) . "</td></tr>";
    echo "<tr><td>Total Bonus Distributed</td><td>" . ($stats->total_bonus_distributed ?? 0) . "</td></tr>";
    echo "</table>";
    echo "<span class='success'>✓ Leaderboard statistics retrieved successfully</span><br>";
} catch (Exception $e) {
    echo "<span class='error'>✗ Error getting leaderboard statistics: " . $e->getMessage() . "</span><br>";
}
echo "</div>";

// Test 4: Get top referrers
echo "<div class='test-section'>";
echo "<h2>Test 4: Top Referrers</h2>";

try {
    $top_referrers = get_top_referrers($db, 10);

    if (empty($top_referrers)) {
        echo "<span class='info'>ℹ No referrers found in the database</span><br>";
    } else {
        echo "<table>";
        echo "<tr><th>Rank</th><th>Name</th><th>Email</th><th>Total Referrals</th><th>Completed Referrals</th><th>Bonus Earned</th></tr>";
        foreach ($top_referrers as $index => $referrer) {
            echo "<tr>";
            echo "<td>" . format_rank($index + 1) . "</td>";
            echo "<td>" . htmlspecialchars($referrer->name) . "</td>";
            echo "<td>" . htmlspecialchars($referrer->email) . "</td>";
            echo "<td>" . $referrer->total_referrals . "</td>";
            echo "<td>" . $referrer->completed_referrals . "</td>";
            echo "<td>" . ($referrer->total_bonus_earned ?? 0) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        echo "<span class='success'>✓ Top referrers retrieved successfully</span><br>";
    }
} catch (Exception $e) {
    echo "<span class='error'>✗ Error getting top referrers: " . $e->getMessage() . "</span><br>";
}
echo "</div>";

// Test 5: Test helper functions
echo "<div class='test-section'>";
echo "<h2>Test 5: Helper Functions</h2>";

echo "<h3>Rank Formatting:</h3>";
for ($i = 1; $i <= 5; $i++) {
    echo "Rank $i: " . format_rank($i) . "<br>";
}

echo "<h3>Badge Classes:</h3>";
for ($i = 1; $i <= 5; $i++) {
    echo "Rank $i: " . get_rank_badge_class($i) . "<br>";
}

echo "<h3>Rank Icons:</h3>";
for ($i = 1; $i <= 5; $i++) {
    echo "Rank $i: " . get_rank_icon($i) . "<br>";
}

echo "<span class='success'>✓ Helper functions working correctly</span><br>";
echo "</div>";

// Test 6: Sample user rank test (if users exist)
echo "<div class='test-section'>";
echo "<h2>Test 6: User Rank Test</h2>";

try {
    // Get a sample user ID
    $query = "SELECT id, name FROM users LIMIT 1";
    $stmt = $db->prepare($query);
    $stmt->execute();
    $user = $stmt->fetch(PDO::FETCH_OBJ);

    if ($user) {
        $rank = get_user_referral_rank($db, $user->id);
        $summary = get_user_referral_summary($db, $user->id);

        echo "<h3>Sample User: " . htmlspecialchars($user->name) . "</h3>";
        echo "<table>";
        echo "<tr><th>Metric</th><th>Value</th></tr>";
        echo "<tr><td>Rank</td><td>" . format_rank($rank) . "</td></tr>";
        echo "<tr><td>Total Referrals</td><td>" . ($summary->total_referrals ?? 0) . "</td></tr>";
        echo "<tr><td>Completed Referrals</td><td>" . ($summary->completed_referrals ?? 0) . "</td></tr>";
        echo "<tr><td>Pending Referrals</td><td>" . ($summary->pending_referrals ?? 0) . "</td></tr>";
        echo "<tr><td>Bonus Earned</td><td>" . ($summary->total_bonus_earned ?? 0) . "</td></tr>";
        echo "<tr><td>Current Bonus Tokens</td><td>" . ($summary->current_bonus_tokens ?? 0) . "</td></tr>";
        echo "</table>";
        echo "<span class='success'>✓ User rank and summary retrieved successfully</span><br>";
    } else {
        echo "<span class='info'>ℹ No users found in the database</span><br>";
    }
} catch (Exception $e) {
    echo "<span class='error'>✗ Error testing user rank: " . $e->getMessage() . "</span><br>";
}
echo "</div>";

echo "<div class='test-section'>";
echo "<h2>Test Summary</h2>";
echo "<p><strong>All tests completed!</strong></p>";
echo "<p>If you see any errors above, please check:</p>";
echo "<ul>";
echo "<li>Database connection and table structure</li>";
echo "<li>Required PHP files are included correctly</li>";
echo "<li>Function definitions in leaderboard_functions.php</li>";
echo "</ul>";
echo "<h3>Dark Mode Testing</h3>";
echo "<p>To test dark mode compatibility:</p>";
echo "<ol>";
echo "<li><a href='test_leaderboard_dark_mode.html' target='_blank'>Open Dark Mode Test Page</a></li>";
echo "<li>Toggle between light and dark modes using the button</li>";
echo "<li>Verify all text is readable and properly contrasted</li>";
echo "<li>Check that rank badges, icons, and colors display correctly</li>";
echo "</ol>";
echo "<p><a href='leaderboard.php'>View Leaderboard Page</a> | <a href='dashboard.php'>Back to Dashboard</a></p>";
echo "</div>";
?>
