<?php
// Set page title
$page_title = 'Edit Announcement';

// Include configuration
require_once '../config/config.php';
require_once '../includes/functions.php';
require_once '../database/db_connect.php';

// Start session
start_session();

// Check if user is logged in and is admin
if (!is_logged_in() || !is_admin()) {
    redirect('../login.php');
}

// Create database instance
$database = new Database();
$db = $database->getConnection();

// Get user ID
$user_id = $_SESSION['user_id'];

// Check if ID is provided
if (!isset($_GET['id']) || empty($_GET['id'])) {
    header('Location: announcements.php');
    exit;
}

$announcement_id = intval($_GET['id']);

// Get announcement details
$query = "SELECT * FROM admin_announcements WHERE id = :id";
$stmt = $db->prepare($query);
$stmt->bindParam(':id', $announcement_id);
$stmt->execute();
$announcement = $stmt->fetch(PDO::FETCH_OBJ);

// Check if announcement exists
if (!$announcement) {
    header('Location: announcements.php');
    exit;
}

// Process form submission
$success_message = '';
$error_message = '';

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    // Validate input
    $title = sanitize($_POST['title']);
    $message = sanitize($_POST['message']);
    $update_notifications = isset($_POST['update_notifications']) ? true : false;

    // Initialize file variables
    $image_file = $announcement->image_file;
    $video_file = $announcement->video_file;
    $file_err = '';

    // Process image upload if provided
    if (isset($_FILES['image_file']) && $_FILES['image_file']['error'] != UPLOAD_ERR_NO_FILE) {
        // Ensure the upload directory exists
        $upload_dir = '../uploads/announcements/';
        if (!file_exists($upload_dir)) {
            mkdir($upload_dir, 0777, true);
        }

        $upload_result = upload_file($_FILES['image_file'], $upload_dir);

        if (!$upload_result['success']) {
            $file_err = 'Image upload failed: ' . $upload_result['message'];
        } else {
            // Delete old image if exists
            if (!empty($announcement->image_file)) {
                $old_image_path = $upload_dir . $announcement->image_file;
                if (file_exists($old_image_path)) {
                    unlink($old_image_path);
                }
            }
            $image_file = $upload_result['filename'];
        }
    }

    // Process video upload if provided
    if (empty($file_err) && isset($_FILES['video_file']) && $_FILES['video_file']['error'] != UPLOAD_ERR_NO_FILE) {
        // Ensure the upload directory exists
        $upload_dir = '../uploads/announcements/';
        if (!file_exists($upload_dir)) {
            mkdir($upload_dir, 0777, true);
        }

        $upload_result = upload_file($_FILES['video_file'], $upload_dir);

        if (!$upload_result['success']) {
            $file_err = 'Video upload failed: ' . $upload_result['message'];
        } else {
            // Delete old video if exists
            if (!empty($announcement->video_file)) {
                $old_video_path = $upload_dir . $announcement->video_file;
                if (file_exists($old_video_path)) {
                    unlink($old_video_path);
                }
            }
            $video_file = $upload_result['filename'];
        }
    }

    // Validate title and message
    if (empty($title)) {
        $error_message = 'Please enter a title for the announcement.';
    } elseif (empty($message)) {
        $error_message = 'Please enter a message for the announcement.';
    } elseif (!empty($file_err)) {
        $error_message = $file_err;
    } else {
        // Start transaction
        $db->beginTransaction();

        try {
            // Update announcement
            $query = "UPDATE admin_announcements
                      SET title = :title, message = :message, image_file = :image_file, video_file = :video_file
                      WHERE id = :id";
            $stmt = $db->prepare($query);
            $stmt->bindParam(':title', $title);
            $stmt->bindParam(':message', $message);
            $stmt->bindParam(':image_file', $image_file);
            $stmt->bindParam(':video_file', $video_file);
            $stmt->bindParam(':id', $announcement_id);
            $stmt->execute();

            // Update notifications if requested
            if ($update_notifications) {
                $query = "UPDATE notifications
                          SET title = :title, message = :message
                          WHERE title = :old_title AND message = :old_message AND type = 'system'";
                $stmt = $db->prepare($query);
                $stmt->bindParam(':title', $title);
                $stmt->bindParam(':message', $message);
                $stmt->bindParam(':old_title', $announcement->title);
                $stmt->bindParam(':old_message', $announcement->message);
                $stmt->execute();

                $notifications_updated = $stmt->rowCount();
            }

            // Commit transaction
            $db->commit();

            $success_message = "Announcement updated successfully.";
            if (isset($notifications_updated) && $notifications_updated > 0) {
                $success_message .= " $notifications_updated notifications were also updated.";
            }

            // Refresh announcement data
            $query = "SELECT * FROM admin_announcements WHERE id = :id";
            $stmt = $db->prepare($query);
            $stmt->bindParam(':id', $announcement_id);
            $stmt->execute();
            $announcement = $stmt->fetch(PDO::FETCH_OBJ);

        } catch (Exception $e) {
            // Rollback transaction on error
            $db->rollBack();
            $error_message = 'Error updating announcement: ' . $e->getMessage();
        }
    }
}

// Get user list for custom recipients
$query = "SELECT id, name, email FROM users WHERE status = 'active' ORDER BY name";
$stmt = $db->prepare($query);
$stmt->execute();
$all_users = $stmt->fetchAll(PDO::FETCH_OBJ);
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - <?php echo SITE_NAME; ?></title>
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.1/css/all.min.css">
    <link rel="stylesheet" href="../assets/css/admin.css">
    <link rel="stylesheet" href="../assets/css/admin-dark-mode.css">
</head>
<body>
    <?php include 'includes/admin_navbar.php'; ?>

    <div class="container-fluid">
        <div class="row">
            <?php include 'includes/admin_sidebar.php'; ?>

            <main role="main" class="col-md-9 ml-sm-auto col-lg-10 px-md-4 py-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2"><?php echo $page_title; ?></h1>
                    <a href="announcements.php" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left"></i> Back to Announcements
                    </a>
                </div>

                <?php if (!empty($success_message)): ?>
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <?php echo $success_message; ?>
                        <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                <?php endif; ?>

                <?php if (!empty($error_message)): ?>
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <?php echo $error_message; ?>
                        <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                <?php endif; ?>

                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">Edit Announcement</h5>
                    </div>
                    <div class="card-body">
                        <form action="<?php echo htmlspecialchars($_SERVER["PHP_SELF"] . '?id=' . $announcement_id); ?>" method="post" enctype="multipart/form-data">
                            <div class="form-group">
                                <label for="title">Title</label>
                                <input type="text" name="title" id="title" class="form-control" value="<?php echo $announcement->title; ?>" required>
                            </div>
                            <div class="form-group">
                                <label for="message">Message</label>
                                <textarea name="message" id="message" class="form-control" rows="5" required><?php echo $announcement->message; ?></textarea>
                            </div>
                            <div class="form-group">
                                <label for="image_file">Image (Optional)</label>
                                <div class="custom-file">
                                    <input type="file" name="image_file" class="custom-file-input" id="image_file" accept=".jpg,.jpeg,.png">
                                    <label class="custom-file-label" for="image_file">Choose image</label>
                                </div>
                                <small class="form-text text-muted">Accepted formats: JPG, PNG. Max size: 20MB</small>
                            </div>
                            <?php if (!empty($announcement->image_file)): ?>
                            <div class="form-group">
                                <label>Current Image:</label>
                                <div>
                                    <img src="../uploads/announcements/<?php echo $announcement->image_file; ?>" class="img-fluid rounded" style="max-height: 200px;" alt="Current announcement image">
                                </div>
                                <small class="text-muted">Upload a new image to replace this one</small>
                            </div>
                            <?php endif; ?>
                            <div class="form-group">
                                <img id="image-preview" class="img-fluid mb-3" style="display: none; max-height: 200px;">
                            </div>
                            <div class="form-group">
                                <label for="video_file">Video (Optional)</label>
                                <div class="custom-file">
                                    <input type="file" name="video_file" class="custom-file-input" id="video_file" accept=".mp4,.webm,.mov">
                                    <label class="custom-file-label" for="video_file">Choose video</label>
                                </div>
                                <small class="form-text text-muted">Accepted formats: MP4, WebM, MOV. Max size: 20MB</small>
                            </div>
                            <?php if (!empty($announcement->video_file)): ?>
                            <div class="form-group">
                                <label>Current Video:</label>
                                <div>
                                    <video controls class="img-fluid rounded" style="max-height: 200px;">
                                        <source src="../uploads/announcements/<?php echo $announcement->video_file; ?>" type="video/mp4">
                                        Your browser does not support the video tag.
                                    </video>
                                </div>
                                <small class="text-muted">Upload a new video to replace this one</small>
                            </div>
                            <?php endif; ?>
                            <div class="form-group">
                                <div class="custom-control custom-checkbox">
                                    <input type="checkbox" class="custom-control-input" id="update_notifications" name="update_notifications">
                                    <label class="custom-control-label" for="update_notifications">Update existing notifications</label>
                                    <small class="form-text text-muted">This will update all notifications that were sent with this announcement</small>
                                </div>
                            </div>
                            <div class="form-group">
                                <button type="submit" class="btn btn-primary" id="update-btn">
                                    <i class="fas fa-save"></i> Update Announcement
                                </button>
                                <a href="announcements.php" class="btn btn-secondary">Cancel</a>
                            </div>
                        </form>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.5.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/popper.js@1.16.1/dist/umd/popper.min.js"></script>
    <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>
    <script src="../assets/js/admin.js"></script>
    <script>
        $(document).ready(function() {
            // File input preview for image
            $('#image_file').on('change', function() {
                var fileName = $(this).val().split('\\').pop();
                $(this).next('.custom-file-label').html(fileName);

                // Preview image if it's an image file
                if (this.files && this.files[0]) {
                    var reader = new FileReader();
                    reader.onload = function(e) {
                        $('#image-preview').attr('src', e.target.result).show();
                    }
                    reader.readAsDataURL(this.files[0]);
                }
            });

            // File input label for video
            $('#video_file').on('change', function() {
                var fileName = $(this).val().split('\\').pop();
                $(this).next('.custom-file-label').html(fileName);
            });

            // Confirmation dialog for updating notifications
            $('#update-btn').on('click', function(e) {
                if ($('#update_notifications').is(':checked')) {
                    if (!confirm('You are about to update all notifications that were sent with this announcement. This action cannot be undone. Are you sure you want to continue?')) {
                        e.preventDefault();
                    }
                }
            });
        });
    </script>
</body>
</html>
